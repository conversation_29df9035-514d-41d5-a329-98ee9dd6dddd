# 拉取node基础镜像
FROM iregistry.baidu-int.com/baidu-base/node:latest

EXPOSE 8004
EXPOSE 8003

# 容器中创建资源目录
RUN mkdir /monitor

COPY ./output /monitor

# 设置工作目录
WORKDIR /monitor

# 替换alpine的源为厂内源
ENV MIRROR=http://mirrors.baidubce.com/
RUN sed -i -E 's,https?://[^ ]+/alpine/,'$MIRROR'alpine/,g' /etc/apk/repositories

# 安装npm
RUN apk add --no-cache --update npm
# 安装PM2
RUN npm install -g pm2 --registry http://registry.npm.baidu-int.com/

CMD ["pm2-runtime", "start", "pm2.json", "--env", "production"]
