/**
 * @desc 测试环境初始化测试用例结果数据，key为`${service_id}-${testing_index}`
 */

export const testCaseData = {
  'bos-0': [
    {
      name: '登录',
      status: 'passed',
      duration: 16471,
      start: 1717492509,
      stop: 1717492525,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/auth.setup.ts',
      retries: 0,
      flaky: false,
      suite: 'setup > auth.setup.ts'
    },
    {
      name: '覆盖率统计',
      status: 'passed',
      duration: 9,
      start: 1717492525,
      stop: 1717492525,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/coverage.cov.ts',
      retries: 0,
      flaky: false,
      suite: 'coverage > coverage.cov.ts'
    },
    {
      name: '面板初始状态',
      status: 'passed',
      duration: 7178,
      start: 1717492526,
      stop: 1717492533,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > Bucket配额管理'
    },
    {
      name: '文档',
      status: 'passed',
      duration: 6129,
      start: 1717492533,
      stop: 1717492539,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > Bucket配额管理'
    },
    {
      name: '修改配额',
      status: 'passed',
      duration: 11977,
      start: 1717492539,
      stop: 1717492551,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > Bucket配额管理'
    },
    {
      name: '面板初始状态',
      status: 'passed',
      duration: 6241,
      start: 1717492551,
      stop: 1717492557,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > 容量管理'
    },
    {
      name: '文档',
      status: 'passed',
      duration: 6569,
      start: 1717492557,
      stop: 1717492564,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > 容量管理'
    },
    {
      name: '修改配额',
      status: 'passed',
      duration: 10448,
      start: 1717492564,
      stop: 1717492574,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > 容量管理'
    },
    {
      name: '面板初始状态',
      status: 'passed',
      duration: 6497,
      start: 1717492574,
      stop: 1717492581,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > 文件数量管理'
    },
    {
      name: '文档',
      status: 'passed',
      duration: 6871,
      start: 1717492581,
      stop: 1717492588,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > 文件数量管理'
    },
    {
      name: '修改配额',
      status: 'passed',
      duration: 11895,
      start: 1717492588,
      stop: 1717492600,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/02-quota/quota.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 02-quota/quota.spec.ts > 文件数量管理'
    },
    {
      name: '周边工具概览文档',
      status: 'passed',
      duration: 7197,
      start: 1717492600,
      stop: 1717492607,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '数据流转平台面板',
      status: 'passed',
      duration: 8461,
      start: 1717492607,
      stop: 1717492615,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: 'BOS Browser面板',
      status: 'passed',
      duration: 8445,
      start: 1717492615,
      stop: 1717492624,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: 'BOS Browser下载',
      status: 'failed',
      duration: 68507,
      start: 1717492624,
      stop: 1717492692,
      rawStatus: 'timedOut',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-CMD',
      status: 'passed',
      duration: 6591,
      start: 1717492693,
      stop: 1717492700,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-BOS Util',
      status: 'passed',
      duration: 6913,
      start: 1717492700,
      stop: 1717492707,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-BOS Porbe',
      status: 'passed',
      duration: 6870,
      start: 1717492707,
      stop: 1717492714,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-BOS FS',
      status: 'passed',
      duration: 6932,
      start: 1717492714,
      stop: 1717492721,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-BOS FTP',
      status: 'passed',
      duration: 8098,
      start: 1717492721,
      stop: 1717492729,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-BOS Import',
      status: 'passed',
      duration: 7021,
      start: 1717492729,
      stop: 1717492736,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-BOS HDFS',
      status: 'passed',
      duration: 6376,
      start: 1717492736,
      stop: 1717492742,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: '工具面板-BOS Alluxio Extension',
      status: 'passed',
      duration: 6990,
      start: 1717492742,
      stop: 1717492749,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/12-tools/tools.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 12-tools/tools.spec.ts'
    },
    {
      name: 'API请求工具',
      status: 'passed',
      duration: 9000,
      start: 1717492749,
      stop: 1717492758,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'SDK布局排列',
      status: 'passed',
      duration: 5421,
      start: 1717492758,
      stop: 1717492764,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'SDK文档-1',
      status: 'passed',
      duration: 6823,
      start: 1717492764,
      stop: 1717492770,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'SDK文档-2',
      status: 'passed',
      duration: 6593,
      start: 1717492770,
      stop: 1717492777,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'Java SDK',
      status: 'passed',
      duration: 6541,
      start: 1717492777,
      stop: 1717492783,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'Python SDK',
      status: 'passed',
      duration: 8436,
      start: 1717492784,
      stop: 1717492792,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'Android SDK',
      status: 'passed',
      duration: 7688,
      start: 1717492792,
      stop: 1717492800,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'IOS SDK',
      status: 'passed',
      duration: 6675,
      start: 1717492800,
      stop: 1717492806,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'JavaScript SDK',
      status: 'passed',
      duration: 6311,
      start: 1717492806,
      stop: 1717492813,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'C# SDK',
      status: 'passed',
      duration: 6471,
      start: 1717492813,
      stop: 1717492819,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'C++ SDK',
      status: 'passed',
      duration: 6415,
      start: 1717492819,
      stop: 1717492826,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'Ruby SDK',
      status: 'passed',
      duration: 9545,
      start: 1717492826,
      stop: 1717492835,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    },
    {
      name: 'Go SDK',
      status: 'passed',
      duration: 7492,
      start: 1717492835,
      stop: 1717492843,
      rawStatus: 'passed',
      tags: [],
      type: 'e2e',
      filePath:
        '/Users/<USER>/console/baidu/bce-console/console-bos/e2e/src/tests/13-sdk/sdk.spec.ts',
      retries: 0,
      flaky: false,
      suite: 'chromium > 13-sdk/sdk.spec.ts'
    }
  ] as any[]
};
