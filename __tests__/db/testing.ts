/**
 * @desc 测试环境初始化测试活动数据
 */

import moment from 'moment';
import {appData} from './app-manage';

export const testData = [
  {
    appId: appData[0]['service_id'],
    toolName: 'playwright',
    toolVersion: '1.44.0',
    startAt: 1717492508714,
    endAt: 1717492843287,
    suitesCount: 0,
    totalCount: 36,
    passedCount: 35,
    failedCount: 1,
    skippedCount: 0,
    pendingCount: 0,
    otherCount: 0,
    platform: 'darwin',
    arch: 'x64',
    repositoryName: 'baidu/bce-console/console-bos',
    repositoryUrl:
      'https://console.cloud.baidu-int.com/devops/icode/repos/baidu/bce-console/console-bos/tree/',
    branchName: 'fe-newbos',
    env: 'sandbox',
    createdBy: 'lurunze'
  }
] as any[];
