/**
 * @file token-gen.spect.ts
 * @desc API Key生成单测
 */

import TokenGenerator from '../src/utils/tokenGen';

test('生成API Key', async () => {
  const tokenGen = new TokenGenerator({keyPrefix: 'bos'});
  const tokenCmpt = await tokenGen.getToken();

  expect(tokenCmpt.token).toBeTruthy();
  expect(tokenCmpt.shortToken).toBeTruthy();
  expect(tokenCmpt.longToken).toBeTruthy();
  expect(tokenCmpt.longTokenHash).toBeTruthy();
});

test('验证API Key', async () => {
  const tokenHashFromDB =
    '52441411a93f51c17ab62ebaea9536990b6e0c63b80847a983d41050e146b837';
  const token = 'v1_bos_4BdAcX6J_CESXgaUB8XvDrodN1knwA38c';

  expect(TokenGenerator.validateToken(token, tokenHashFromDB)).toBe(true);
});
