/**
 * @desc 测试活动API用例
 */

require('dotenv').config();
import fs from 'node:fs';
import path from 'node:path';
import request from 'supertest';
import app from '../../src/index';

async function readFileAsBuffer(filePath: string) {
  try {
    const data = await fs.promises.readFile(filePath);

    return data;
  } catch (err) {
    console.error('Error reading file:', err);
  }

  return null;
}

test.only('POST /testing/v1/import 导入测试报告', async () => {
  const filePath = path.resolve(__dirname, '../db/ctrf-report.json');
  // const buffer = await readFileAsBuffer(filePath);
  const response = await request(app.callback())
    .post('/testing/v1/import')
    .field('appId', 'bos', {contentType: 'application/json'})
    .attach('file', filePath)
    .expect(200);

  expect(response.status).toBe(200);
  expect(response.body.success).toBe(true);
  expect(response.body.msg).toBe('测试活动创建成功');
  expect(response.body.data.uuid).toBeTruthy();
});

test('POST /testing/v1/get 查询测试活动列表', async () => {
  const response = await request(app.callback())
    .post('/testing/v1/get')
    .send({id: 1})
    .expect(200);

  expect(response.body).toHaveProperty('data');
  expect(response.body.data.appId).toEqual('bos');
  expect(response.body.data.totalCount).toEqual(36);
  expect(response.body.data.branchName).toEqual('fe-newbos');
  expect(response.body.data.env).toEqual('sandbox');
});

test('POST /testing/v1/list 查询测试活动列表', async () => {
  const response = await request(app.callback())
    .post('/testing/v1/list')
    .send({
      appId: 1,
      page: 1,
      perPage: 10
    })
    .expect(200);

  expect(response.body).toHaveProperty('data');
  expect(response.body.data.items).toEqual([]);
  expect(response.body.data.total).toEqual(0);
});
