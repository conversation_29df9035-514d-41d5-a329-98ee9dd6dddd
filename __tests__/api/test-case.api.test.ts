/**
 * @desc 测试用例API用例
 */

require('dotenv').config();
import request from 'supertest';
import app from '../../src/index';

test('POST /testcase/v1/get 查询测试用例', async () => {
  const response = await request(app.callback())
    .post('/testing/v1/get')
    .send({id: 1,})
    .expect(200);

  expect(response.body).toHaveProperty('data');
  expect(response.body.data.name).toEqual('登录');
  expect(response.body.data.status).toEqual('passed');
  expect(response.body.data.type).toEqual('e2e');
});

test('POST /testcase/v1/list 查询测试用例列表', async () => {
  const response = await request(app.callback())
    .post('/testing/v1/list')
    .send({
      testId: 1,
      page: 1,
      perPage: 10
    })
    .expect(200);

  expect(response.body).toHaveProperty('data');
  expect(response.body.data.items[0].type).toEqual('e2e');
  expect(response.body.data.items[0].status).toEqual('passed');
  expect(response.body.data.total).toEqual(10);
});
