/**
 * @desc 测试活动模型用例
 */

require('dotenv').config();
import moment from 'moment';
import {Sequelize} from 'sequelize';
import {AppManage, Testing, TestCase} from '../../src/model';
import {appData, testData, testCaseData} from '../db';
import {convertTimestampToDate} from '../../src/utils/helper';

const env = process.env;

function initDB() {
  const sequelize = new Sequelize(
    `mysql://${env.MYSQL_USER}:${env.MYSQL_PWD}@${env.MYSQL_HOST}/${env.MYSQL_DATABASE}`,
    {
      dialect: 'mysql',
      timezone: '+08:00',
      port: Number(env.MYSQL_PORT)
    }
  );

  return sequelize;
}

describe('Testing模型测试', () => {
  const db = initDB();

  beforeAll(async () => {
    // 数据库重建
    try {
      /** 外键限制导致初始化时DROP失败，Hack一下 */
      await db.query('SET FOREIGN_KEY_CHECKS = 0');
      await db.sync({force: env.NODE_ENV === 'development'});
      await db.query('SET FOREIGN_KEY_CHECKS = 1');

      console.log('[TEST] Database synchronized.');
    } catch (error) {
      console.log('[TEST] Database synchronized failed: ');
      console.log(error);
    }
  }, 60 * 60 * 1000);

  afterAll(async () => {
    await db.close();
  });

  test('新增应用数据', async () => {
    const transaction = await db.transaction();

    try {
      const apps = await AppManage.bulkCreate(appData, {transaction});
      await transaction.commit();
    } catch (error) {
      transaction.rollback();
    }

    const {rows, count} = await AppManage.findAndCountAll();
    const app = rows[0].toJSON();

    expect(count).toEqual(1);
    expect(app['service_id']).toEqual('bos');
    expect(app['serviceName']).toEqual('对象存储');
  });

  test('新增测试数据', async () => {
    const transaction = await db.transaction();

    try {
      await Testing.create({
        ...testData[0],
        startAt: convertTimestampToDate(testData[0].startAt),
        endAt: convertTimestampToDate(testData[0].endAt),
      }, {transaction});
      await transaction.commit();
    } catch (error) {
      transaction.rollback();
    }

    const {rows: testings, count: testCount} = await Testing.findAndCountAll();
    const test = testings[0].toJSON();

    expect(testCount).toEqual(1);
    expect(test['appId']).toEqual('bos');
    expect(test['totalCount']).toEqual(36);
    expect(test['passedCount']).toEqual(35);
    expect(test['failedCount']).toEqual(1);
  });

  test('新增测试用例数据', async () => {
    const transaction = await db.transaction();

    try {
      const testing = await Testing.findOne({
        where: {
          appId: 'bos'
        }
      });

      expect(testing).toBeDefined();

      if (testing) {
        const testCases = await TestCase.bulkCreate(
          testCaseData['bos-0'].map(i => ({
            suite: i.suite,
            message: i?.message,
            trace: i.trace,
            filePath: i.filePath,
            name: i.name,
            status: i.status,
            browser: i.browser ?? 'chromium',
            duration: i.duration ?? 0,
            retries: i.retries ?? 0,
            flaky: i.flaky ?? false,
            tags: i.tags?.join(','),
            startAt: convertTimestampToDate(i.start),
            endAt: convertTimestampToDate(i.stop),
            testId: testing.id
          })) as any[],
          {transaction}
        );

        // 添加关联关系
        await testing.addTestCases(testCases, {transaction});
      }

      await transaction.commit();
    } catch (error) {
      transaction.rollback();
    }

    const {rows: cases, count: testCasesCount} = await TestCase.findAndCountAll();
    const testCase = cases[0].toJSON();

    expect(testCasesCount).toEqual(36);
    expect(testCase['testId']).toEqual(1);
  });
});
