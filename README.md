# bce-monitor-server

## 开发 & 部署

```bash
# 安装依赖
npm i

# 项目启动
npm run dev

# 执行构建
npm run build

# 生产环境启动
pm2 start pm2.json
```

## 数据库更新

沙盒环境通过以下方式变更表结构，**生产环境**必须通过[SQL 评审](https://console.cloud.baidu-int.com/drds/apps/bceonline2/dmsView)变更表结构。

数据库变更语句统一放在`/migrations`目录下，通过以下命令创建迁移文件：

```bash
# 创建迁移问卷
npx sequelize-cli migration:generate --name <file-name>

```

执行完毕后会在`/migrations`目录下生成一个 JS 格式的变更文件，名称格式形如`<timestamp>-<file-name>.js`。一个典型的迁移文件格式如下：

```javascript
'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * eg: 对Person表添加petName和favoriteColor列
     *
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.addColumn(
          'Person',
          'petName',
          {
            type: Sequelize.DataTypes.STRING,
          },
          {transaction: t},
        ),
        queryInterface.addColumn(
          'Person',
          'favoriteColor',
          {
            type: Sequelize.DataTypes.STRING,
          },
          {transaction: t},
        ),
      ]);
    });
    **/
  },

  async down(queryInterface, Sequelize) {
    /**
     * eg: 回滚迁移对Person表删除petName和favoriteColor列
     *
    return queryInterface.sequelize.transaction(t => {
      return Promise.all([
        queryInterface.removeColumn('Person', 'petName', { transaction: t }),
        queryInterface.removeColumn('Person', 'favoriteColor', {
          transaction: t,
        }),
      ]);
    });
    **/
  }
};
```