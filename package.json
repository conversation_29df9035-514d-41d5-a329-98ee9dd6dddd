{"name": "bce-monitor-server", "version": "0.0.1", "description": "", "main": "src/index.ts", "scripts": {"dev": "NODE_ENV=development nodemon", "build": "sh ./scripts/build.sh", "build-ts": "tsc", "stop": "pm2 delete pm2.json", "start": "pm2 start pm2.json --env production", "performanceTest": "node ./test/performance.js", "test": "jest", "test:api": "jest --testPathPattern=\".api\\.test\\.ts\""}, "repository": {"type": "git"}, "dependencies": {"@babel/node": "^7.6.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/preset-env": "^7.5.5", "@babel/register": "^7.5.5", "@baiducloud/sdk": "^1.0.2", "@elastic/elasticsearch": "7.9.1", "@koa/multer": "^3.0.2", "@rollup/plugin-typescript": "^11.0.0", "@types/koa__multer": "^2.0.7", "async-validator": "^4.2.5", "axios": "^1.4.0", "bce-sdk-js": "^0.2.9", "bs58": "^6.0.0", "chokidar": "^4.0.3", "crypto": "^1.0.1", "dotenv": "^16.4.5", "ejs": "^3.1.10", "hashids": "^2.3.0", "ioredis": "^5.3.2", "js-md5": "^0.8.3", "koa": "^2.14.1", "koa-body": "^4.1.1", "koa-bodyparser": "^3.2.0", "koa-router": "^7.1.1", "lodash": "^4.17.21", "multer": "^1.4.5-lts.1", "mysql2": "^3.3.3", "node-schedule": "^2.1.1", "node-xlsx": "0.22.0", "nodemailer": "^6.9.14", "querystring": "^0.2.0", "reflect-metadata": "^0.1.13", "sequelize": "^6.31.1", "shx": "^0.3.4", "source-map": "^0.7.3", "tmp": "^0.2.3", "typedi": "^0.10.0", "uuid": "^9.0.0", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "xss": "^1.0.15"}, "devDependencies": {"@baidu/cbt-report": "^1.0.9", "@playwright/test": "^1.46.1", "@rollup/plugin-alias": "^4.0.3", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-run": "^3.0.1", "@rollup/plugin-typescript": "^11.0.0", "@types/ejs": "^3.1.5", "@types/jest": "^29.5.12", "@types/koa": "^2.13.5", "@types/koa-bodyparser": "^4.3.10", "@types/koa-router": "^7.4.4", "@types/lodash": "^4.14.191", "@types/moment": "^2.13.0", "@types/multer": "^1.4.11", "@types/node": "^22.2.0", "@types/node-schedule": "^2.1.0", "@types/nodemailer": "^6.4.15", "@types/supertest": "^6.0.2", "@types/tmp": "^0.2.6", "@types/uuid": "^9.0.1", "builtin-modules": "^3.3.0", "jest": "^29.7.0", "nodemon": "^1.19.4", "rollup": "^3.20.0", "rollup-plugin-node-builtins": "^2.1.2", "sequelize-cli": "^6.6.2", "supertest": "^7.0.0", "ts-jest": "^29.2.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.2", "tslib": "^2.5.0", "typescript": "^4.9.5"}, "author": "yangwei14", "license": "UNLICENSED", "devEngines": {"node": ">=18.16.1 <18.19.0", "npm": ">=9.5.1"}}