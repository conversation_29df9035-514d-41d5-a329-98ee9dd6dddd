#!/usr/bin/env bash
npm install --legacy-peer-deps --include=dev
rm -rf ./dist
rm -rf ./output
npm run build-ts

# # 转移打包文件
mkdir output
cp -R ./scripts/start*.sh ./output
cp -R ./dist/* ./output
cp ./pm2.json ./output
cp -R ./cert ./output
cp ./package.json ./output
# # 转移模板文件
mkdir output/src/template
cp ./src/template/* ./output/src/template

cp ./package-lock.json ./output
# cp -R ./config ./output/
cp -R ./bin ./output/
cp -R ./node_modules ./output

