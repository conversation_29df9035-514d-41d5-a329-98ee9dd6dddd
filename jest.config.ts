import {createDefaultEsmPreset, type JestConfigWithTsJest} from 'ts-jest';
import {defaults} from 'jest-config';

const config: JestConfigWithTsJest = {
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'node',
  displayName: {
    name: 'bce-monitor-server',
    color: 'yellowBright'
  },
  verbose: true,
  testMatch: ['**/__tests__/**/(*.)+(spec|test).[jt]s?(x)'],
  testPathIgnorePatterns: [...defaults.testPathIgnorePatterns, '/test/'],
  moduleFileExtensions: ['js', 'ts'],
  setupFilesAfterEnv: ['./__tests__/jest.setup.js'],
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  transform: {
    ...createDefaultEsmPreset().transform
  }
};

export default config;
