/**
 * @desc 数据库表变更，删除`test_case`、`question`、`submission`外键约束，修改`test_case`、`testing`中JSON字段为TEXT字段
 */
'use strict';

const isProd = process.env.NODE_ENV === 'production';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /** 生产环境走云上百度SQL评审执行变更，无法通过sequlize同步表结构 */
    if (isProd) {
      return;
    }

    /** 删除 questions 表中的外键约束 */
    try {
      const [constraintNames] = await queryInterface.sequelize.query(`
        SELECT
          CONSTRAINT_NAME
        FROM
          INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE
          TABLE_NAME = 'questions'
          AND TABLE_SCHEMA = 'monitor-action'
          AND CONSTRAINT_NAME != 'PRIMARY';
      `);

      if (constraintNames.length > 0) {
        const constraintName = constraintNames[0].CONSTRAINT_NAME;
        console.log(
          `Removing constraint ${constraintName} from questions table`
        );
        await queryInterface.removeConstraint('questions', constraintName);
      } else {
        console.log('No foreign key constraint found in questions table.');
      }
    } catch (error) {
      console.log(
        'Error occured while removing foreign key in questions table.'
      );
    }

    /** 删除 submission 表中的外键约束 */
    try {
      const [constraintNames] = await queryInterface.sequelize.query(`
        SELECT
          CONSTRAINT_NAME
        FROM
          INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE
          TABLE_NAME = 'submission'
          AND TABLE_SCHEMA = 'monitor-action'
          AND CONSTRAINT_NAME != 'PRIMARY';
      `);

      if (constraintNames.length > 0) {
        const constraintName = constraintNames[0].CONSTRAINT_NAME;
        console.log(
          `Removing constraint ${constraintName} from submission table`
        );
        await queryInterface.removeConstraint('submission', constraintName);
      } else {
        console.log('No foreign key constraint found in submission table.');
      }
    } catch (error) {
      console.log(
        'Error occured while removing foreign key in submission table.'
      );
    }

    /** 删除 test_case 表中的外键约束 */
    try {
      const [constraintNames] = await queryInterface.sequelize.query(`
        SELECT
          CONSTRAINT_NAME
        FROM
          INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE
          TABLE_NAME = 'test_case'
          AND TABLE_SCHEMA = 'monitor-action'
          AND CONSTRAINT_NAME != 'PRIMARY'
          AND CONSTRAINT_NAME != 'uuid';
      `);

      if (constraintNames.length > 0) {
        const constraintName = constraintNames[0].CONSTRAINT_NAME;
        console.log(
          `Removing constraint ${constraintName} from test_case table`
        );
        await queryInterface.removeConstraint('test_case', constraintName);
      } else {
        console.log('No foreign key constraint found in test_case table.');
      }
    } catch (error) {
      console.log(
        'Error occured while removing foreign key in test_case table.'
      );
    }

    try {
      const tableName = 'testing';
      const testingTableDesc = await queryInterface.describeTable(tableName);

      // 如果 `pipeline` 字段不是 LONGTEXT，则执行迁移
      if (testingTableDesc.pipeline.type !== 'LONGTEXT') {
        await queryInterface.changeColumn(tableName, 'pipeline', {
          type: Sequelize.DataTypes.TEXT('long'),
          allowNull: true
        });
      } else {
        console.log('`pipeline` 字段已经是 LONGTEXT，跳过迁移。');
      }

      // 如果 `coverage` 字段不是 LONGTEXT，则执行迁移
      if (testingTableDesc.coverage.type !== 'LONGTEXT') {
        await queryInterface.changeColumn(tableName, 'coverage', {
          type: Sequelize.DataTypes.TEXT('long'),
          allowNull: true
        });
      } else {
        console.log('`coverage` 字段已经是 LONGTEXT，跳过迁移。');
      }
    } catch (error) {
      console.log('`testing`表修改 `pipeline`、`coverage`字段类型失败');
    }

    try {
      const testCaseTableDesc = await queryInterface.describeTable('test_case');

      if (testCaseTableDesc.steps.type !== 'LONGTEXT') {
        await queryInterface.changeColumn('test_case', 'steps', {
          type: Sequelize.DataTypes.TEXT('long'),
          allowNull: true
        });
      } else {
        console.log('`steps` 字段已经是 LONGTEXT，跳过迁移。');
      }
    } catch (error) {
      console.log('`test_case`表修改 `steps`字段类型失败');
    }
  },

  async down(queryInterface, Sequelize) {
    /** 不需要回滚 */
  }
};
