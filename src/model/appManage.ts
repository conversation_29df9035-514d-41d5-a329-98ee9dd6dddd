/**
 * 应用管理数据表
 */
import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {MonitorModel} from '../decorators';
import moment from 'moment';

@MonitorModel('app_manage')
export class AppManage extends DBModel {
  static fields = {
    // 应用id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // 服务id
    service_id: {
      type: Sequelize.STRING,
      primaryKey: true,
      allowNull: false,
      unique: true
    },
    // 名称
    service_name: {
      type: Sequelize.STRING
    },
    // 环境
    env: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 名称
    serviceName: {
      type: Sequelize.STRING
    },
    // 描述
    description: {
      type: Sequelize.STRING
    },
    // 标签（标签之间以 , 分割）
    tags: {
      type: Sequelize.STRING
    },
    // 创建人
    creator: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 更新人
    updater: {
      type: Sequelize.STRING
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      get() {
        return moment(this.getDataValue('created_at')).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
    } as any,
    updated_at: {
      type: Sequelize.DATE,
      get() {
        return this.getDataValue('updated_at')
          ? moment(this.getDataValue('updated_at')).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          : null;
      }
    } as any
  };
}

AppManage.sync();
