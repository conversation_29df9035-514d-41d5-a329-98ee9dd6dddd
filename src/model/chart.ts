import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {PageMakerModel, MonitorModel} from '../decorators';
import moment from 'moment';

@MonitorModel('chart_manage')
export class ChartManage extends DBModel {
  static fields = {
    // 自增id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // 服务id
    service_id: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 环境
    env: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 图表类型
    type: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 名称
    name: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 描述
    description: {
      type: Sequelize.STRING
    },
    // 创建人
    creator: {
      type: Sequelize.STRING
    },
    // 更新人
    updater: {
      type: Sequelize.STRING
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      get() {
        return moment(this.getDataValue('created_at')).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
    } as any,
    updated_at: {
      type: Sequelize.DATE,
      get() {
        return this.getDataValue('updated_at')
          ? moment(this.getDataValue('updated_at')).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          : null;
      }
    } as any,
    deleted_at: {
      type: Sequelize.DATE,
      get() {
        return this.getDataValue('deleted_at')
          ? moment(this.getDataValue('deleted_at')).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          : null;
      }
    } as any
  };
}

ChartManage.sync();
