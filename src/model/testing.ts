/**
 * @file testing.ts
 * @desc 测试任务执行表
 */

import {
  Model,
  DataTypes,
  type InferAttributes,
  type InferCreationAttributes,
  type CreationOptional,
  type NonAttribute,
  type CreateOptions,
  type InstanceUpdateOptions,
  type ForeignKey,
  type Association,
  type HasManyAddAssociationMixin,
  type HasManyCountAssociationsMixin,
  type HasManyCreateAssociationMixin,
  type HasManyGetAssociationsMixin,
  type HasManyHasAssociationMixin,
  type HasManySetAssociationsMixin,
  type HasManyAddAssociationsMixin,
  type HasManyHasAssociationsMixin,
  type HasManyRemoveAssociationMixin,
  type HasManyRemoveAssociationsMixin,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {new_db as db} from '../utils';
import {TestCase} from './test-case';
import {AppManage} from './appManage';

import type {CoverageReport, IPipeContext} from '@baidu/cbt-report';

export class Testing extends Model<
  InferAttributes<Testing>,
  InferCreationAttributes<Testing, {omit: 'uuid'}>
> {
  /** 自增ID, 主键 */
  declare id: CreationOptional<number>;

  /** 测试任务UUID, 全局唯一 */
  declare uuid: string;

  /** 测试框架名称 */
  declare toolName?: string;
  /** 测试框架版本号 */
  declare toolVersion?: string;

  /** 操作系统平台 */
  declare platform: string;
  /** CPU架构 */
  declare arch: string;

  /** 测试运行时环境，iPipe代表CI，local代表本地 */
  declare runtimeEnv: 'iPipe' | 'local';
  /** 测试环境，sandbox代表沙箱环境，online代表线上环境 */
  declare testEnv: 'sandbox' | 'online';
  /** 当前测试代码所属分支 */
  declare branch: string;

  /** 测试执行完毕后的状态 */
  declare status: 'passed' | 'failed' | 'timedout' | 'interrupted' | 'initial';
  /** 测试开始时间 */
  declare startAt: Date;
  /** 测试结束时间 */
  declare endAt: Date;
  /** 用例Group的数量 */
  declare suitesCount: number;
  /** 用例总数 */
  declare totalCount: number;
  /** 成功的用例总数 */
  declare passedCount: number;
  /** 失败的用例总数 */
  declare failedCount: number;
  /** 跳过的用例总数 */
  declare skippedCount: number;
  /** 挂起的用例总数 */
  declare pendingCount: number;
  /** 未知状态的用例总数 */
  declare otherCount: number;
  /** 标记误报的用例总数 */
  declare falsePositiveCount?: number;
  /** 本次测试包含的浏览器类型，英文逗号分隔 */
  declare browsers: string;

  /**
   * 覆盖率相关信息（格式为JSON，受限于线上MySQL版本，所以存为字符串）
   * @type {CoverageReport}
   */
  declare coverage: any | null;
  /** 覆盖率相关信息（虚拟字段） */
  declare _coverageReport: CoverageReport | null;

  /**
   * iPipe相关系统参数（格式为JSON，受限于线上MySQL版本，所以存为字符串）
   * @type {IPipeContext}
   */
  declare pipeline: any | null;
  /** iPipe相关系统参数（虚拟字段） */
  declare _iPipeContext: IPipeContext | null;

  /** 测试用例集合: 包含全量用例的执行和结果的详细信息 */
  declare TestCases?: TestCase[];

  declare static associations: {
    TestCases: Association<Testing, TestCase>;
  };

  declare getTestCases: HasManyGetAssociationsMixin<TestCase>;
  declare addTestCase: HasManyAddAssociationMixin<TestCase, number>;
  declare addTestCases: HasManyAddAssociationsMixin<TestCase, number>;
  declare setTestCases: HasManySetAssociationsMixin<TestCase, number>;
  declare removeTestCase: HasManyRemoveAssociationMixin<TestCase, number>;
  declare removeTestCass: HasManyRemoveAssociationsMixin<TestCase, number>;
  declare hasTestCase: HasManyHasAssociationMixin<TestCase, number>;
  declare hasTestCases: HasManyHasAssociationsMixin<TestCase, number>;
  declare countTestCases: HasManyCountAssociationsMixin;
  declare createTestCase: HasManyCreateAssociationMixin<TestCase, 'testId'>;

  /** 所属应用ID，FK */
  declare appId: ForeignKey<string>;
  /** 所属应用 */
  declare app?: NonAttribute<AppManage>;

  declare getApp: BelongsToGetAssociationMixin<AppManage>;
  declare setApp: BelongsToSetAssociationMixin<AppManage, string>;
  declare createApp: BelongsToCreateAssociationMixin<AppManage>;

  /** 创建人 */
  declare createdBy: CreationOptional<string>;
  /** 更新人 */
  declare updatedBy: CreationOptional<string>;
  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;
  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;

  /** 聚合数据时的统计结果 */
  declare count?: number;
  declare date?: string;
  declare avgDurationInSecond?: number;
  declare passedRatio?: number;
  declare avgTestCaseCount?: number;
  declare latestStartAt?: number;
  declare app_manage?: AppManage;
}

Testing.init(
  {
    id: {
      type: DataTypes.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    uuid: {
      type: DataTypes.UUID,
      unique: true,
      defaultValue: DataTypes.UUIDV4
    },
    toolName: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'playwright'
    },
    toolVersion: {
      type: DataTypes.STRING,
      allowNull: true
    },
    platform: {
      type: DataTypes.ENUM,
      values: [
        'aix',
        'android',
        'darwin',
        'freebsd',
        'haiku',
        'linux',
        'openbsd',
        'sunos',
        'win32',
        'cygwin',
        'netbsd'
      ],
      allowNull: false,
      defaultValue: 'linux'
    },
    arch: {
      type: DataTypes.ENUM,
      values: [
        'arm',
        'arm64',
        'ia32',
        'loong64',
        'mips',
        'mipsel',
        'ppc',
        'ppc64',
        'riscv64',
        's390',
        's390x',
        'x64'
      ],
      allowNull: false,
      defaultValue: 'x64'
    },
    runtimeEnv: {
      type: DataTypes.ENUM,
      values: ['iPipe', 'local'],
      allowNull: false,
      defaultValue: 'local'
    },
    testEnv: {
      type: DataTypes.ENUM,
      values: ['sandbox', 'online'],
      allowNull: false,
      defaultValue: 'sandbox'
    },
    branch: {
      type: DataTypes.STRING,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM,
      values: ['passed', 'failed', 'timedout', 'interrupted', 'initial'],
      allowNull: false,
      defaultValue: 'initial'
    },
    startAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    endAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    suitesCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    totalCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    passedCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    failedCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    skippedCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    pendingCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    otherCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    falsePositiveCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    browsers: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'chromium'
    },
    coverage: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
      get() {
        const value = this.getDataValue('coverage');

        if (value == null) {
          return value;
        }

        try {
          return JSON.parse(value) as CoverageReport;
        } catch (error) {
          return null;
        }
      },
      set(value: CoverageReport | null) {
        if (value == null) {
          this.setDataValue('coverage', null);
        }

        try {
          this.setDataValue('coverage', JSON.stringify(value));
        } catch (error) {
          this.setDataValue('coverage', null);
        }
      }
    },
    _coverageReport: {
      type: DataTypes.VIRTUAL,
      get() {
        const value = this.getDataValue('coverage');

        if (value == null) {
          return value;
        }

        try {
          return JSON.parse(value) as CoverageReport;
        } catch (error) {
          return null;
        }
      },
      set() {
        throw new Error(
          '`_coverageReport` is a virtual column, you can not set a value!'
        );
      }
    },
    pipeline: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
      get() {
        const value = this.getDataValue('pipeline');

        if (value == null) {
          return value;
        }

        try {
          return JSON.parse(value) as IPipeContext;
        } catch (error) {
          return null;
        }
      },
      set(value: IPipeContext | null) {
        if (value == null) {
          this.setDataValue('pipeline', null);
        }

        try {
          this.setDataValue('pipeline', JSON.stringify(value));
        } catch (error) {
          this.setDataValue('pipeline', null);
        }
      }
    },
    _iPipeContext: {
      type: DataTypes.VIRTUAL,
      get() {
        const value = this.getDataValue('pipeline');

        if (value == null) {
          return value;
        }

        try {
          return JSON.parse(value) as IPipeContext;
        } catch (error) {
          return null;
        }
      },
      set() {
        throw new Error(
          '`_iPipeContext` is a virtual column, you can not set a value!'
        );
      }
    },
    createdBy: DataTypes.STRING,
    updatedBy: DataTypes.STRING,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  },
  {
    sequelize: db,
    tableName: 'testing',
    modelName: 'Testing',
    freezeTableName: true,
    underscored: true,
    timestamps: true,
    hooks: {
      beforeCreate: (test, options) => {
        type CreationOptions = CreateOptions<InferCreationAttributes<Testing>> &
          ExtraCreationAttributes;

        // 默认从CreationOptions中获取用户名，如果未传就从iPipeContext中获取
        const usernameFromOptions = (options as CreationOptions)?.user
          ?.username;
        const iPipeContext = test.get('_iPipeContext');
        const username =
          usernameFromOptions ??
          iPipeContext?.pipelineTriggerUser ??
          iPipeContext?.triggerUser ??
          'system';

        test.createdBy = username;
        test.updatedBy = username;
      },
      beforeUpdate: (test, options) => {
        type UpdateOptions = InstanceUpdateOptions<
          InferCreationAttributes<Testing>
        > &
          ExtraCreationAttributes;

        // 默认从CreationOptions中获取用户名，如果未传就从iPipeContext中获取
        const usernameFromOptions = (options as UpdateOptions)?.user?.username;
        const iPipeContext = test.get('_iPipeContext');
        const username =
          usernameFromOptions ??
          iPipeContext?.pipelineTriggerUser ??
          iPipeContext?.triggerUser ??
          'system';

        test.createdBy = username;
        test.updatedBy = username;
      }
    }
  }
);
