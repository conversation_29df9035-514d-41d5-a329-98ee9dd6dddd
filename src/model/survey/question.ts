/**
 * @file survey/question.ts
 * @desc 问题表
 */

import {
  Sequelize,
  Model,
  DataTypes,
  type InferAttributes,
  type InferCreationAttributes,
  type CreationOptional,
  type ForeignKey,
  type NonAttribute,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {SurveyModel} from './survey';

export type QuestionType = 'rating' | 'multiple_choice' | 'essay';

export class QuestionModel extends Model<
  InferAttributes<QuestionModel>,
  InferCreationAttributes<QuestionModel>
> {
  /** 自增ID, 主键 */
  declare id: CreationOptional<number>;

  /** 问题在当前问卷中的排序 */
  declare order: number;

  /** 问题类型 */
  declare type: QuestionType;

  /** 问题内容 */
  declare title: string;

  /** 问题图片的CDN地址 */
  declare images: any | null;

  /** 问题信息描述 */
  declare description: string | null;

  /** 是否为必填问题 */
  declare required: boolean | null;

  /** 是否为子问题，即子问题需要选择父问题的选项后才可显示，值为所属问题的ID */
  declare isSubQuestion: boolean | null;

  /** 问题选项（仅"multiple_choice"题目支持） */
  declare options: any | null;

  /** 最大输入字数（仅"essay"题目支持） */
  declare maxLength: number | null;

  /** 创建人 */
  declare createdBy: string | null;

  /** 更新人 */
  declare updatedBy: string | null;

  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;

  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;

  /** 所属调查问卷ID，FK */
  declare surveyId: ForeignKey<SurveyModel['id']>;

  /** 所属调查问卷，使用1-N关联关系查询 */
  declare survey: NonAttribute<SurveyModel>;

  /** 获取绑定的应用实体 */
  declare getSurvey: BelongsToGetAssociationMixin<SurveyModel>;

  /** 更新绑定的应用实体 */
  declare setSurvey: BelongsToSetAssociationMixin<
    SurveyModel,
    SurveyModel['id']
  >;

  /** 创建绑定的应用实体 */
  declare createSurvey: BelongsToCreateAssociationMixin<SurveyModel>;

  /** 扩展属性：考虑了子问题之后的排序 */
  declare serialNumber?: number;

  static initialize(sequelize: Sequelize) {
    QuestionModel.init(
      {
        id: {
          type: DataTypes.BIGINT,
          autoIncrement: true,
          primaryKey: true
        },
        order: {
          type: DataTypes.INTEGER,
          allowNull: false,
          defaultValue: 1
        },
        type: {
          type: DataTypes.ENUM,
          values: ['rating', 'multiple_choice', 'essay'],
          allowNull: false,
          defaultValue: 'rating'
        },
        title: {
          type: DataTypes.STRING,
          allowNull: false
        },
        images: {
          type: DataTypes.TEXT('long'),
          allowNull: true,
          get() {
            const value = this.getDataValue('images');

            if (value == null) {
              return value;
            }

            try {
              return JSON.parse(value);
            } catch (error) {
              return {};
            }
          },
          set(value: string | null) {
            if (value == null) {
              this.setDataValue('images', null);
            }

            try {
              this.setDataValue('images', JSON.stringify(value));
            } catch (error) {
              this.setDataValue('images', '');
            }
          }
        },
        description: {
          type: DataTypes.STRING,
          allowNull: true
        },
        isSubQuestion: {
          type: DataTypes.BOOLEAN,
          allowNull: true,
          defaultValue: false
        },
        required: {
          type: DataTypes.BOOLEAN,
          allowNull: true,
          defaultValue: false
        },
        options: {
          type: DataTypes.TEXT('long'),
          allowNull: true,
          get() {
            const value = this.getDataValue('options');

            if (value == null) {
              return value;
            }

            try {
              return JSON.parse(value);
            } catch (error) {
              return {};
            }
          },
          set(value: string | null) {
            if (value == null) {
              this.setDataValue('options', null);
            }

            try {
              this.setDataValue('options', JSON.stringify(value));
            } catch (error) {
              this.setDataValue('options', '');
            }
          }
        },
        maxLength: {
          type: DataTypes.INTEGER,
          allowNull: true,
          defaultValue: 1000
        },
        createdBy: {
          type: DataTypes.STRING,
          allowNull: true
        },
        updatedBy: {
          type: DataTypes.STRING,
          allowNull: true
        },
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE
      },
      {
        sequelize: sequelize,
        tableName: 'questions',
        modelName: 'QuestionModel',
        freezeTableName: true,
        underscored: true,
        timestamps: true
      }
    );
  }

  static associate() {
    QuestionModel.belongsTo(SurveyModel, {
      targetKey: 'id',
      foreignKey: 'surveyId',
      constraints: false,
      as: 'Survey'
    });
  }
}
