/**
 * @file survey/submission.ts
 * @desc 问卷提交表
 */

import {
  Sequelize,
  Model,
  DataTypes,
  type InferAttributes,
  type InferCreationAttributes,
  type CreationOptional,
  type ForeignKey,
  type NonAttribute,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {SurveyModel} from './survey';

export interface SubmissionAnswerItem {
  questionId: number;
  content?: string;
}

export interface SubmissionAnswerItemFE
  extends Omit<SubmissionAnswerItem, 'questionId'> {
  questionId: string;
}

/**
 * 因为没有限制填写次数，所以一个用户可能有多次提交，且每次提交的设备信息可能也会变化
 */
export class SubmissionModel extends Model<
  InferAttributes<SubmissionModel>,
  InferCreationAttributes<SubmissionModel>
> {
  /** 自增ID, 主键 */
  declare id: CreationOptional<number>;

  /** 浏览器名称 */
  declare browser: string | null;

  /** 浏览器版本 */
  declare browserVersion: string | null;

  /** 操作系统 */
  declare platform: string | null;

  /** 屏幕分辨率 */
  declare resolution: string | null;

  /** 浏览器可视区分辨率 */
  declare browserResolution: string | null;

  /** 浏览器语言 */
  declare language: string | null;

  /** 打开调查问卷的来源页面 */
  declare referer: string | null;

  /* 用户ID（子用户支持） */
  declare userId: string | null;

  /* 账户ID */
  declare accountId: string;

  /* 用户名称，可选 */
  declare name: string | null;

  /* 用户联系方式，可选 */
  declare phone: string | null;

  /** 提交答案集合 */
  declare answers: any;

  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;

  /** 更新时间 */
  declare updatedAt: CreationOptional<Date>;

  /** 所属调查问卷ID，FK */
  declare surveyId: ForeignKey<SurveyModel['id']>;

  /** 所属调查问卷，使用1-N关联关系查询 */
  declare survey: NonAttribute<SurveyModel>;

  declare getSurvey: BelongsToGetAssociationMixin<SurveyModel>;
  declare setSurvey: BelongsToSetAssociationMixin<
    SurveyModel,
    SurveyModel['id']
  >;
  declare createSurvey: BelongsToCreateAssociationMixin<SurveyModel>;

  static initialize(sequelize: Sequelize) {
    SubmissionModel.init(
      {
        id: {
          type: DataTypes.BIGINT,
          autoIncrement: true,
          primaryKey: true
        },
        browser: {
          type: DataTypes.STRING,
          allowNull: true
        },
        browserVersion: {
          type: DataTypes.STRING,
          allowNull: true
        },
        platform: {
          type: DataTypes.STRING,
          allowNull: true
        },
        resolution: {
          type: DataTypes.STRING,
          allowNull: true
        },
        browserResolution: {
          type: DataTypes.STRING,
          allowNull: true
        },
        language: {
          type: DataTypes.STRING,
          allowNull: true
        },
        referer: {
          type: DataTypes.STRING,
          allowNull: true
        },
        userId: {
          type: DataTypes.STRING,
          allowNull: true
        },
        accountId: {
          type: DataTypes.STRING,
          allowNull: false
        },
        name: {
          type: DataTypes.STRING,
          allowNull: true
        },
        phone: {
          type: DataTypes.STRING,
          allowNull: true
        },
        answers: {
          type: DataTypes.TEXT('long'),
          allowNull: false,
          get() {
            const value = this.getDataValue('answers');

            try {
              return JSON.parse(value);
            } catch (error) {
              return {};
            }
          },
          set(value: string) {
            try {
              this.setDataValue('answers', JSON.stringify(value));
            } catch (error) {
              this.setDataValue('answers', '');
            }
          }
        },
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE
      },
      {
        sequelize: sequelize,
        tableName: 'submission',
        modelName: 'SubmissionModel',
        freezeTableName: true,
        underscored: true,
        timestamps: true
      }
    );
  }

  static associate() {
    SubmissionModel.belongsTo(SurveyModel, {
      targetKey: 'id',
      foreignKey: 'surveyId',
      constraints: false
    });
  }
}
