/**
 * @file survey/index.ts
 * @desc 调查问卷相关表
 */

import {SurveyModel} from './survey';
import {QuestionModel} from './question';
import {SubmissionModel} from './submission';
import {new_db as db} from '../../utils/db';

/** 初始化模型 */
SurveyModel.initialize(db);
QuestionModel.initialize(db);
SubmissionModel.initialize(db);

/** 设置关联关系 */
SurveyModel.associate();
QuestionModel.associate();
SubmissionModel.associate();

(async () => {
  try {
    await SurveyModel.sync();
    await QuestionModel.sync();
    await SubmissionModel.sync();
  } catch (error: any) {
    console.log('Error syncing survey related models: ', error.message);
  }
})();

export {SurveyModel, QuestionModel, SubmissionModel};
