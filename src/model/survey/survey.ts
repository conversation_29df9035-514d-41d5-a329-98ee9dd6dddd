/**
 * @file survey/survey.ts
 * @desc 调查问卷表 & 问题表
 */

import {
  Sequelize,
  Model,
  DataTypes,
  type InferAttributes,
  type InferCreationAttributes,
  type CreationOptional,
  type NonAttribute,
  type ForeignKey,
  type Association,
  type HasManyAddAssociationMixin,
  type HasManyCountAssociationsMixin,
  type HasManyCreateAssociationMixin,
  type HasManyGetAssociationsMixin,
  type HasManyHasAssociationMixin,
  type HasManySetAssociationsMixin,
  type HasManyAddAssociationsMixin,
  type HasManyHasAssociationsMixin,
  type HasManyRemoveAssociationMixin,
  type HasManyRemoveAssociationsMixin,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {AppManage} from '../appManage';
import {QuestionModel} from './question';
import {SubmissionModel} from './submission';

export class SurveyModel extends Model<
  InferAttributes<SurveyModel>,
  InferCreationAttributes<SurveyModel>
> {
  /** 自增ID, 主键 */
  declare id: CreationOptional<number>;

  /** 调查问卷标题 */
  declare title: string;

  /** 问题图片的CDN地址 */
  declare images: any | null;

  /** 调查问卷描述信息 */
  declare description: string | null;

  /** 创建人 */
  declare createdBy: string | null;

  /** 更新人 */
  declare updatedBy: string | null;

  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;

  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;

  /** 测试用例集合: 包含全量用例的执行和结果的详细信息 */
  declare Questions?: QuestionModel[];

  declare static associations: {
    Questions: Association<SurveyModel, QuestionModel>;
  };

  declare getQuestions: HasManyGetAssociationsMixin<QuestionModel>;
  declare addQuestion: HasManyAddAssociationMixin<
    QuestionModel,
    QuestionModel['id']
  >;
  declare addQuestions: HasManyAddAssociationsMixin<
    QuestionModel,
    QuestionModel['id']
  >;
  declare setQuestions: HasManySetAssociationsMixin<
    QuestionModel,
    QuestionModel['id']
  >;
  declare removeQuestion: HasManyRemoveAssociationMixin<
    QuestionModel,
    QuestionModel['id']
  >;
  declare removeTestCass: HasManyRemoveAssociationsMixin<
    QuestionModel,
    QuestionModel['id']
  >;
  declare hasQuestion: HasManyHasAssociationMixin<
    QuestionModel,
    QuestionModel['id']
  >;
  declare hasQuestions: HasManyHasAssociationsMixin<
    QuestionModel,
    QuestionModel['id']
  >;
  declare countQuestions: HasManyCountAssociationsMixin;
  declare createQuestion: HasManyCreateAssociationMixin<
    QuestionModel,
    'surveyId'
  >;

  /** 所属应用ID，FK */
  declare appId: ForeignKey<string>;
  /** 所属应用 */
  declare app?: NonAttribute<AppManage>;

  declare getApp: BelongsToGetAssociationMixin<AppManage>;
  declare setApp: BelongsToSetAssociationMixin<AppManage, number>;
  declare createApp: BelongsToCreateAssociationMixin<AppManage>;

  static initialize(sequelize: Sequelize) {
    SurveyModel.init(
      {
        id: {
          type: DataTypes.BIGINT,
          autoIncrement: true,
          primaryKey: true
        },
        title: {
          type: DataTypes.STRING,
          allowNull: false
        },
        images: {
          type: DataTypes.TEXT('long'),
          allowNull: true,
          get() {
            const value = this.getDataValue('images');

            if (value == null) {
              return value;
            }

            try {
              return JSON.parse(value);
            } catch (error) {
              return {};
            }
          },
          set(value: string | null) {
            if (value == null) {
              this.setDataValue('images', null);
            }

            try {
              this.setDataValue('images', JSON.stringify(value));
            } catch (error) {
              this.setDataValue('images', '');
            }
          }
        },
        description: {
          type: DataTypes.STRING,
          allowNull: true
        },
        createdBy: {
          type: DataTypes.STRING,
          allowNull: true
        },
        updatedBy: {
          type: DataTypes.STRING,
          allowNull: true
        },
        createdAt: DataTypes.DATE,
        updatedAt: DataTypes.DATE
      },
      {
        sequelize: sequelize,
        tableName: 'surveys',
        modelName: 'SurveyModel',
        freezeTableName: true,
        underscored: true,
        timestamps: true
      }
    );
  }

  static associate() {
    /** 关联应用 */
    SurveyModel.belongsTo(AppManage, {
      targetKey: 'service_id',
      foreignKey: 'appId',
      constraints: false
    });

    /** 关联问题 */
    SurveyModel.hasMany(QuestionModel, {
      sourceKey: 'id',
      /** 指定虚拟外键，但不添加外键约束，因为线上环境数据库不允许设置外键 */
      foreignKey: 'surveyId',
      constraints: false,
      as: {
        singular: 'Question',
        plural: 'Questions'
      },
      hooks: true
    });

    /** 关联提交 */
    SurveyModel.hasMany(SubmissionModel, {
      sourceKey: 'id',
      foreignKey: 'surveyId',
      constraints: false
    });
  }
}
