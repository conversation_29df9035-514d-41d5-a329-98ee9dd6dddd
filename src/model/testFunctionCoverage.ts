/**
 * @file testFunctionCoverage.ts
 * @desc 功能覆盖率信息表
 */

import {
  Model,
  DataTypes,
  type InferAttributes,
  type InferCreationAttributes,
  type CreationOptional,
  type NonAttribute,
  type CreateOptions,
  type InstanceUpdateOptions,
  type ForeignKey,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {new_db as db} from '../utils';
import {AppManage} from './appManage';

interface ExtraCreationAttributes {
  user: UUAPUserInfo;
}

export class TestFunctionCoverage extends Model<
  InferAttributes<TestFunctionCoverage>,
  InferCreationAttributes<TestFunctionCoverage, {omit: 'uuid'}>
> {
  /** 自增ID, 主键 */
  declare id: CreationOptional<number>;
  /** 功能覆盖率id, 全局唯一 */
  declare uuid: string;
  /** 测试环境，sandbox代表沙箱环境，online代表线上环境 */
  declare testEnv: 'sandbox' | 'online';
  /**
   * 功能覆盖率相关信息（格式为JSON，受限于线上MySQL版本，所以存为字符串）
   */
  declare functionCoverage: any;

  /** 所属应用ID，FK */
  declare appId: ForeignKey<string>;
  /** 所属应用 */
  declare app?: NonAttribute<AppManage>;

  declare getApp: BelongsToGetAssociationMixin<AppManage>;
  declare setApp: BelongsToSetAssociationMixin<AppManage, string>;
  declare createApp: BelongsToCreateAssociationMixin<AppManage>;

  /** 创建人 */
  declare createdBy: CreationOptional<string>;
  /** 更新人 */
  declare updatedBy: CreationOptional<string>;
  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;
  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;
}

TestFunctionCoverage.init(
  {
    id: {
      type: DataTypes.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    uuid: {
      type: DataTypes.UUID,
      unique: true,
      defaultValue: DataTypes.UUIDV4
    },
    testEnv: {
      type: DataTypes.ENUM,
      values: ['sandbox', 'online'],
      allowNull: false,
      defaultValue: 'sandbox'
    },
    functionCoverage: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
      get() {
        const value = this.getDataValue('functionCoverage');

        if (value == null) {
          return value;
        }

        try {
          return JSON.parse(value) as any;
        } catch (error) {
          return null;
        }
      },
      set(value: any) {
        if (value == null) {
          this.setDataValue('functionCoverage', null);
        }

        try {
          this.setDataValue('functionCoverage', JSON.stringify(value));
        } catch (error) {
          this.setDataValue('functionCoverage', null);
        }
      }
    },
    appId: DataTypes.STRING,
    createdBy: DataTypes.STRING,
    updatedBy: DataTypes.STRING,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  },
  {
    sequelize: db,
    tableName: 'test_function_coverage',
    modelName: 'TestFunctionCoverage',
    freezeTableName: true,
    underscored: true,
    timestamps: true,
    hooks: {
      beforeCreate: (test, options) => {
        type CreationOptions = CreateOptions<InferCreationAttributes<TestFunctionCoverage>> & ExtraCreationAttributes;
        const usernameFromOptions = (options as CreationOptions)?.user?.username;
        const username = usernameFromOptions ?? 'system';
        test.createdBy = username;
        test.updatedBy = username;
      },
      beforeUpdate: (test, options) => {
        type UpdateOptions = InstanceUpdateOptions<InferCreationAttributes<TestFunctionCoverage>> &
          ExtraCreationAttributes;
        const usernameFromOptions = (options as UpdateOptions)?.user?.username;
        const username = usernameFromOptions ?? 'system';
        test.updatedBy = username;
      }
    }
  }
);

/** 关联表 */
TestFunctionCoverage.belongsTo(AppManage, {
  targetKey: 'service_id',
  foreignKey: 'appId',
  constraints: false
});

TestFunctionCoverage.sync();
