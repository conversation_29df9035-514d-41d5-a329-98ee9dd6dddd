import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {MonitorModel} from '../decorators';
import moment from 'moment';

@MonitorModel('emails')
export class Emails extends DBModel {
  static fields = {
    // 主键自增id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // 邮件周期
    title: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 邮件类型
    type: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 产品id，本次邮件涉及的服务id列表，逗号分隔
    service_ids: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 更新时间
    created_time: {
      type: Sequelize.DATE,
      allowNull: false,
      get() {
        return this.getDataValue('created_time')
          ? moment(this.getDataValue('created_time')).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          : null;
      }
    } as any,
    // 更新时间
    update_time: {
      type: Sequelize.DATE,
      allowNull: false,
      get() {
        return this.getDataValue('update_time')
          ? moment(this.getDataValue('update_time')).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          : null;
      }
    } as any,
    // 更新人
    update_user: {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'system'
    },
    publish_time: {
      type: Sequelize.DATE,
      allowNull: true,
      get() {
        return this.getDataValue('publish_time')
          ? moment(this.getDataValue('publish_time')).format(
              'YYYY-MM-DD HH:mm:ss'
            )
          : null;
      }
    } as any,
    // 是否发布
    published: {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    // 可渲染的带数据的邮件内容
    detail: {
      type: Sequelize.TEXT('long'),
      allowNull: false
    },
    // 趋势图base64
    trend_img: {
      type: Sequelize.TEXT('long'),
      allowNull: true
    },
    // 上升原因
    reason: {
      type: Sequelize.TEXT('long'),
      allowNull: false
    },
    // 优化进展
    progress: {
      type: Sequelize.TEXT('long'),
      allowNull: false
    },
    // 源数据
    source_data: {
      type: Sequelize.TEXT('long'),
      allowNull: false
    }
  };
}
