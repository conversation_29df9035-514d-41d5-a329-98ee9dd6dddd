import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {PageMakerModel, MonitorModel} from '../decorators';
import moment from 'moment';

@MonitorModel('api_exemption_rules')
export class ApiExemptionRules extends DBModel {
    static fields = {
        // 应用id
        id: {
            type: Sequelize.BIGINT,
            primaryKey: true,
            allowNull: false,
            autoIncrement: true
        },
        // 服务id
        service_id: {
            type: Sequelize.STRING,
            allowNull: false
        },
        // 环境
        env: {
            type: Sequelize.STRING,
            allowNull: false
        },
        // 页面地址
        href: {
            type: Sequelize.STRING,
        },
        // 接口地址
        path: {
            type: Sequelize.STRING,
        },
        // 报错code
        code: {
            type: Sequelize.STRING
        },
        // 报错描述
        message: {
            type: Sequelize.STRING
        },
        // 豁免的用户
        user: {
            type: Sequelize.STRING
        },
        region: {
            type: Sequelize.STRING
        },
        // 豁免场景说明
        reason: {
            type: Sequelize.STRING
        },
        // 创建人
        created_by: {
            type: Sequelize.STRING
        },
        // 删除
        deleted_by: {
            type: Sequelize.STRING
        },
        created_at: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.NOW,
            get() {
                return moment(this.getDataValue('created_at')).format(
                    'YYYY-MM-DD HH:mm:ss'
                );
            }
        } as any,
        deleted_at: {
            type: Sequelize.DATE,
            get() {
                return this.getDataValue('deleted_at')
                    ? moment(this.getDataValue('deleted_at')).format(
                        'YYYY-MM-DD HH:mm:ss'
                    )
                    : null;
            }
        } as any
    };
}

ApiExemptionRules.sync();
