// /**
//  * 产品文案管理
//  */
// import Sequelize from 'sequelize';
// import {Model as DBModel} from 'sequelize';
// import {MonitorModel} from '../decorators';
// import moment from 'moment';

// @MonitorModel('service_content')
// export class ServiceContentManage extends DBModel {
//   static fields = {
//     // 自增主键
//     id: {
//       type: Sequelize.BIGINT,
//       primaryKey: true,
//       allowNull: false,
//       autoIncrement: true
//     },
//     // 服务id
//     service_id: {
//       type: Sequelize.STRING,
//       allowNull: false
//     },
//     // 环境
//     env: {
//       type: Sequelize.STRING,
//       allowNull: false
//     },
//     // 是否发布
//     published: {
//       type: Sequelize.BOOLEAN,
//       allowNull: false
//     },
//     // 有待发布内容
//     updated: {
//       type: Sequelize.BOOLEAN,
//       allowNull: false
//     },
//     // 数据结构
//     schema:{
//       type: Sequelize.TEXT,
//       allowNull: false,
//     },
//     // 文案内容
//     content:{
//       type: Sequelize.TEXT,
//     },
//     // 创建人
//     created_by: {
//       type: Sequelize.STRING,
//       allowNull: false,
//     },
//     // 更新人
//     updated_by: {
//       type: Sequelize.STRING,
//     },
//     created_at: {
//       type: Sequelize.DATE,
//       defaultValue: Sequelize.NOW,
//       get() {
//         return moment(this.getDataValue('created_at')).format(
//           'YYYY-MM-DD HH:mm:ss'
//         );
//       }
//     } as any,
//     updated_at: {
//       type: Sequelize.DATE,
//       get() {
//         return this.getDataValue('updated_at')
//           ? moment(this.getDataValue('updated_at')).format(
//               'YYYY-MM-DD HH:mm:ss'
//             )
//           : null;
//       }
//     } as any,
//   };
// }

// ServiceContentManage.sync();
