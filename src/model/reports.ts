import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {PageMakerModel} from '../decorators';

@PageMakerModel('bce_monitor_reports')
export class Reports extends DBModel {
  static fields = {
    // 主键自增id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // 产品uuid，来自console center
    service_id: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 产品名
    env: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 产品状态
    start_time: {
      type: Sequelize.DATE,
      allowNull: false
    },
    end_time: {
      type: Sequelize.DATE,
      allowNull: false
    },
    detail: {
      type: Sequelize.JSON,
      allowNull: false
    }
  };
}
