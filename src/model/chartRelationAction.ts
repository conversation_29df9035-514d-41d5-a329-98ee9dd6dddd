import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {PageMakerModel, MonitorModel} from '../decorators';
import {ChartManage} from './chart';
import {ActionManage} from './action';

@MonitorModel('chart_relation_action')
export class ChartRelationActionManage extends DBModel {
  static fields = {
    // 自增id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    order: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 颜色
    color: {
      type: Sequelize.STRING,
      allowNull: false
    },
    chart_id: {
      type: Sequelize.STRING,
      allowNull: false
    },
    action_id: {
      type: Sequelize.STRING,
      allowNull: false
    }
  };
}

ChartManage.hasMany(ChartRelationActionManage, {
  foreignKey: 'chart_id',
  as: 'actions',
  constraints: false
});
ChartRelationActionManage.belongsTo(ChartManage, {
  foreignKey: 'chart_id',
  constraints: false

});

ActionManage.hasOne(ChartRelationActionManage, {
  foreignKey: 'action_id',
  constraints: false

});
ChartRelationActionManage.belongsTo(ActionManage, {
  foreignKey: 'action_id',
  constraints: false
});
 
ChartRelationActionManage.sync();
