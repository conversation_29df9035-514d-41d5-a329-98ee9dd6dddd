/**
 * @file api-key.ts
 * @desc API Key表
 */

import {
  Model,
  DataTypes,
  type InferAttributes,
  type InferCreationAttributes,
  type CreationOptional,
  type ForeignKey,
  type NonAttribute,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {new_db as db} from '../utils';
import {AppManage} from './appManage';

export class ApiKey extends Model<
  InferAttributes<ApiKey>,
  InferCreationAttributes<ApiKey>
> {
  /** 自增ID, 主键 */
  declare id: CreationOptional<number>;

  /** API Key short token */
  declare token: string;

  /** API Key long token hash */
  declare hash: string;

  /** 创建人 */
  declare createdBy: string;

  /** 过期时间 */
  declare expiredAt?: Date;

  /** 最近一次使用时间 */
  declare lastUsedAt?: Date;

  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;

  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;

  /** 所属应用ID，FK */
  declare appId: ForeignKey<string>;

  /** 所属应用，使用关联关系查询 */
  declare app?: NonAttribute<AppManage>;

  /** 获取绑定的应用实体 */
  declare getApp: BelongsToGetAssociationMixin<AppManage>;

  /** 更新绑定的应用实体 */
  declare setApp: BelongsToSetAssociationMixin<AppManage, string>;

  /** 创建绑定的应用实体 */
  declare createApp: BelongsToCreateAssociationMixin<AppManage>;
}

ApiKey.init(
  {
    id: {
      type: DataTypes.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    token: {
      type: DataTypes.STRING,
      unique: true,
      allowNull: false
    },
    hash: {
      type: DataTypes.STRING,
      allowNull: false
    },
    createdBy: {
      type: DataTypes.STRING,
      allowNull: false
    },
    expiredAt: {
      type: DataTypes.DATE,
      allowNull: false,
      /** 有效期默认1年 */
      defaultValue: () => {
        const date = new Date();
        date.setFullYear(date.getFullYear() + 1);
        return date;
      }
    },
    lastUsedAt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  },
  {
    sequelize: db,
    tableName: 'api_key',
    modelName: 'ApiKey',
    freezeTableName: true,
    underscored: true,
    timestamps: true
  }
);

ApiKey.belongsTo(AppManage, {
  targetKey: 'service_id',
  foreignKey: 'appId',
  constraints: false
});

ApiKey.sync();
