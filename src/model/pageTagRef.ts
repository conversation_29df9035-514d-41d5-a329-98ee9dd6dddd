/**
 * 应用标签数据表
 */
import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {MonitorModel} from '../decorators';

@MonitorModel('page_tag_ref')
export class PageTagRef extends DBModel {
  static fields = {
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // tag id
    tag_id: {
      type: Sequelize.BIGINT,
      allowNull: false,
    },
    // tag name
    tag_name: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 页面url
    url: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 页面名称
    name: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 服务id
    service_id: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 环境
    env: {
      type: Sequelize.STRING,
      allowNull: false
    },
  };
}
