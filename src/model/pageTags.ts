/**
 * 应用标签数据表
 */
import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {MonitorModel} from '../decorators';
import moment from 'moment';

@MonitorModel('page_tag')
export class PageTag extends DBModel {
  static fields = {
    // tag id
    tag_id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // tag 名称
    tag_name: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 服务id
    service_id: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 环境
    env: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // 创建人
    creator: {
      type: Sequelize.STRING,
      allowNull: false
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      get() {
        return moment(this.getDataValue('created_at')).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
    } as any,
  };
}
