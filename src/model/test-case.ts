/**
 * @file test-case.ts
 * @desc 测试用例执行表
 */

import {
  Model,
  InferAttributes,
  InferCreationAttributes,
  CreationOptional,
  DataTypes,
  ForeignKey,
  NonAttribute,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {new_db as db} from '../utils';
import {AppManage} from './appManage';
import {Testing} from './testing';
import {TestErrorTrace} from './test-error-trace';

import type {CTRFTestStep} from '@baidu/cbt-report';

export class TestCase extends Model<
  InferAttributes<TestCase>,
  InferCreationAttributes<TestCase, {omit: 'uuid'}>
> {
  /** 自增ID，PK */
  declare id: CreationOptional<number>;
  /** 所属测试ID，FK */
  declare testId: ForeignKey<Testing['id']>;

  /** 所属测试 */
  declare Test?: NonAttribute<Testing>;

  declare getTest: BelongsToGetAssociationMixin<Testing>;
  declare setTest: BelongsToSetAssociationMixin<Testing, number>;
  declare createTest: BelongsToCreateAssociationMixin<Testing>;

  /** 对应的错误追踪记录 */
  declare TestErrorTrace?: NonAttribute<TestErrorTrace>;

  declare getTestErrorTrace: BelongsToGetAssociationMixin<TestErrorTrace>;
  declare setTestErrorTrace: BelongsToSetAssociationMixin<TestErrorTrace, number>;
  declare createTestErrorTrace: BelongsToCreateAssociationMixin<TestErrorTrace>;

  /** 用例UUID, 全局唯一 */
  declare uuid: string;
  /** 测试报告内部用例ID */
  declare ctrfId?: string;
  /** 用例名称 */
  declare name: string;
  /** 浏览器类型 */
  declare browser: 'chromium' | 'firefox' | 'webkit';
  /** 浏览器版本号 */
  declare browserVersion?: string;
  /** 用例结束状态 */
  declare status: 'passed' | 'failed' | 'skipped' | 'pending' | 'other';
  /** 用例开始时间 */
  declare startAt: Date;
  /** 用例结束时间 */
  declare endAt: Date;
  /** 用例执行花费的时间，ms为单位 */
  declare duration: number;
  /** 用例失败重试的次数 */
  declare retries: number;
  /** 是否为脆弱用例，当用例执行成功，且重试的次数 > 1, 则被标记为脆弱用例 */
  declare flaky: boolean;
  /** 测试所属的group */
  declare suite?: string;
  /** 与用例结果相关的描述性消息或注释 */
  declare message?: string;
  /** 用例失败的错误堆栈 */
  declare trace?: string;
  /** 用例的标签，使用英文逗号分隔 */
  declare tags?: string;
  /** 测试所在的文件路径 */
  declare filePath?: string;
  /** 测试中的文件截图，base64编码 */
  declare screenshot?: string;
  /**
   * 用例详细执行步骤（格式为JSON，受限于线上MySQL版本，所以存为字符串）
   * @type {CTRFTestStep[]}
   */
  declare steps: any | null;
  /** 用例详细执行步骤（虚拟字段） */
  declare _testSteps: CTRFTestStep[] | null;

  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;
  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;

  /** 聚合数据时的统计结果 */
  declare count?: number;
}

TestCase.init(
  {
    id: {
      type: DataTypes.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    uuid: {
      type: DataTypes.UUID,
      unique: true,
      allowNull: false,
      defaultValue: DataTypes.UUIDV4
    },
    ctrfId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: ''
    },
    browser: {
      type: DataTypes.ENUM,
      values: ['chromium', 'firefox', 'webkit'],
      allowNull: true,
      defaultValue: 'chromium'
    },
    browserVersion: {
      type: DataTypes.STRING,
      allowNull: true
    },
    status: {
      type: DataTypes.ENUM,
      values: ['passed', 'failed', 'skipped', 'pending', 'other'],
      allowNull: false,
      defaultValue: 'failed'
    },
    startAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    endAt: {
      type: DataTypes.DATE,
      allowNull: false
    },
    duration: {
      type: DataTypes.DECIMAL,
      allowNull: false,
      defaultValue: 0
    },
    retries: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0
    },
    flaky: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    suite: DataTypes.STRING,
    message: {
      type: DataTypes.STRING(5000),
      allowNull: true
    },
    trace: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    filePath: {
      type: DataTypes.STRING,
      allowNull: true
    },
    tags: {
      type: DataTypes.STRING,
      allowNull: true
    },
    screenshot: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    steps: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
      get() {
        const value = this.getDataValue('steps');

        if (value == null) {
          return value;
        }

        try {
          return JSON.parse(value) as CTRFTestStep[];
        } catch (error) {
          return [];
        }
      },
      set(value: CTRFTestStep[] | null) {
        if (value == null) {
          this.setDataValue('steps', null);
        }

        try {
          this.setDataValue('steps', JSON.stringify(value));
        } catch (error) {
          this.setDataValue('steps', null);
        }
      }
    },
    _testSteps: {
      type: DataTypes.VIRTUAL,
      get() {
        const value = this.getDataValue('steps');

        if (value == null) {
          return value;
        }

        try {
          return JSON.parse(value) as CTRFTestStep[];
        } catch (error) {
          return [];
        }
      },
      set() {
        throw new Error(
          '`_testSteps` is a virtual column, you can not set a value!'
        );
      }
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  },
  {
    sequelize: db,
    tableName: 'test_case',
    modelName: 'TestCase',
    freezeTableName: true,
    underscored: true,
    timestamps: true
  }
);

AppManage.hasMany(Testing, {
  sourceKey: 'service_id',
  foreignKey: 'appId',
  /** 指定虚拟外键，但不添加外键约束，因为线上环境数据库不允许设置外键 */
  constraints: false
});

Testing.belongsTo(AppManage, {
  targetKey: 'service_id',
  foreignKey: 'appId',
  constraints: false
});

Testing.hasMany(TestCase, {
  sourceKey: 'id',
  foreignKey: 'testId',
  constraints: false,
  as: {
    singular: 'TestCase',
    plural: 'TestCases'
  },
  hooks: true
});

TestCase.belongsTo(Testing, {
  targetKey: 'id',
  foreignKey: 'testId',
  constraints: false,
  as: 'Test'
});

// 创建测试活动表
Testing.sync()
  .then(() => {
    // 创建用例表
    TestCase.sync();
  })
  .catch((error: any) => {
    console.log('Error syncing Testing/TestCase models: ', error.message);
  });
