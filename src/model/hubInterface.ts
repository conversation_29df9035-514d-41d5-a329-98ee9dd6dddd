/**
 * 应用管理数据表
 */
import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {MonitorModel} from '../decorators';
import moment from 'moment';

@MonitorModel('hub_interfaces')
export class HubInterface extends DBModel {
  static fields = {
    // 应用id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    service_name: {
      type: Sequelize.STRING
    },
    interface_type: {
      type: Sequelize.STRING
    },
    method: {
      type: Sequelize.STRING
    },
    interface_name: {
      type: Sequelize.STRING
    },
    path: {
      type: Sequelize.STRING
    },
    prefix: {
      type: Sequelize.STRING
    },
    prefix_uuid: {
      type: Sequelize.STRING
    },
    protocol: {
      type: Sequelize.STRING
    },
    query_params: {
      type: Sequelize.STRING
    },
    uuid: {
      type: Sequelize.STRING
    },
    created_at: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.NOW,
      get() {
        return moment(this.getDataValue('created_at')).format(
          'YYYY-MM-DD HH:mm:ss'
        );
      }
    } as any,
    source: {
      type: Sequelize.STRING
    },
  };
}

HubInterface.sync();
