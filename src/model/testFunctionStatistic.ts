/**
 * @file testFunctionStatistic.ts
 * @desc 功能覆盖率表统计数据表
 */

import {
  Model,
  DataTypes,
  type InferAttributes,
  type InferCreationAttributes,
  type CreationOptional,
  type CreateOptions,
  type ForeignKey
} from 'sequelize';
import {new_db as db} from '../utils';

export class TestFunctionStatistic extends Model<
  InferAttributes<TestFunctionStatistic>,
  InferCreationAttributes<TestFunctionStatistic, {omit: 'uuid'}>
> {
  /** 自增ID, 主键 */
  declare id: CreationOptional<number>;
  /** 功能覆盖率id, 全局唯一 */
  declare uuid: string;
  /** 测试环境，sandbox代表沙箱环境，online代表线上环境 */
  declare testEnv: 'sandbox' | 'online';
  /** 根用例组数量 */
  declare rootGroupCount: number;
  /** 用例总数 */
  declare totalCount: number;
  /** 已覆盖的用例总数 */
  declare coveredCount: number;
  /** 未覆盖的用例总数 */
  declare uncoveredCount: number;
  /** 所属应用ID，FK */
  declare appId: ForeignKey<string>;
  /** 创建人 */
  declare createdBy: CreationOptional<string>;
  /** 更新人 */
  declare updatedBy: CreationOptional<string>;
  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;
  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;
}

TestFunctionStatistic.init(
  {
    id: {
      type: DataTypes.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    uuid: {
      type: DataTypes.UUID,
      unique: true,
      defaultValue: DataTypes.UUIDV4
    },
    testEnv: {
      type: DataTypes.ENUM,
      values: ['sandbox', 'online'],
      allowNull: false,
      defaultValue: 'sandbox'
    },
    rootGroupCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    totalCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    coveredCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    uncoveredCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    appId: DataTypes.STRING,
    createdBy: DataTypes.STRING,
    updatedBy: DataTypes.STRING,
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  },
  {
    sequelize: db,
    tableName: 'test_function_statistic',
    modelName: 'TestFunctionStatistic',
    freezeTableName: true,
    underscored: true,
    timestamps: true,
    hooks: {
      beforeCreate: (test, options) => {
        type CreationOptions = CreateOptions<InferCreationAttributes<TestFunctionStatistic>> & ExtraCreationAttributes;
        const usernameFromOptions = (options as CreationOptions)?.user?.username;
        const username = usernameFromOptions ?? 'system';
        test.createdBy = username;
        test.updatedBy = username;
      }
    }
  }
);

TestFunctionStatistic.sync();
