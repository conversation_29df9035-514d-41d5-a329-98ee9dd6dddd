import Sequelize from 'sequelize';
import {Model as DBModel} from 'sequelize';
import {PageMakerModel} from '../decorators';

@PageMakerModel('application')
export class Application extends DBModel {
  static fields = {
    // 应用id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // 组织id
    company_id: {
      type: Sequelize.BIGINT,
      allowNull: false
    },
    // 产品key
    key: {
      type: Sequelize.STRING,
      allowNull: false
    },
    // config
    config: {
      type: Sequelize.JSON
    },
    deleted_at: {
      type: Sequelize.DATE
    }
  };
}

@PageMakerModel('api_center')
export class ApiCenter extends DBModel {
  static fields = {
    // 主键自增id
    id: {
      type: Sequelize.BIGINT,
      primaryKey: true,
      allowNull: false,
      autoIncrement: true
    },
    // 组织id
    company_id: {
      type: Sequelize.BIGINT,
      allowNull: false
    },
    // 应用id
    app_id: {
      type: Sequelize.BIGINT,
      allowNull: false
    },
    // 接口信息
    api: {
      type: Sequelize.JSON,
      allowNull: false
    },
    deleted_at: {
      type: Sequelize.DATE
    }
  };
}
