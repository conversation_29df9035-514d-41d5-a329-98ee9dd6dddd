/**
 * @file test-error-trace.ts
 * @desc 测试用例错误追踪表
 */

import {
  Model,
  DataTypes,
  type CreationOptional,
  type CreateOptions,
  type InferAttributes,
  type InferCreationAttributes,
  type InstanceUpdateOptions,
  type ForeignKey,
  type NonAttribute,
  type BelongsToGetAssociationMixin,
  type BelongsToSetAssociationMixin,
  type BelongsToCreateAssociationMixin
} from 'sequelize';
import {new_db as db} from '../utils';
import {AppManage} from './appManage';
import {TestCase} from './test-case';
import {Testing} from './testing';

interface ExtraCreationAttributes {
  user: UUAPUserInfo;
}

export class TestErrorTrace extends Model<
  InferAttributes<TestErrorTrace>,
  InferCreationAttributes<TestErrorTrace>
> {
  /** 自增ID，PK */
  declare id: CreationOptional<number>;

  /** 对应测试用例ID，FK */
  declare testCaseId: ForeignKey<TestCase['id']>;

  /** 所属测试ID，FK */
  declare testId: ForeignKey<Testing['id']>;

  /** 对应测试用例 */
  declare TestCase?: NonAttribute<TestCase>;

  /** 获取绑定的用例实体 */
  declare getTestCase: BelongsToGetAssociationMixin<TestCase>;

  /** 更新绑定的用例实体 */
  declare setTestCase: BelongsToSetAssociationMixin<TestCase, number>;

  /** 创建绑定的用例实体 */
  declare createTestCase: BelongsToCreateAssociationMixin<TestCase>;

  /** 所属应用ID，FK */
  declare appId: ForeignKey<string>;

  /** 所属应用，使用关联关系查询 */
  declare app?: NonAttribute<AppManage>;

  /** 获取绑定的应用实体 */
  declare getApp: BelongsToGetAssociationMixin<AppManage>;

  /** 更新绑定的应用实体 */
  declare setApp: BelongsToSetAssociationMixin<AppManage, string>;

  /** 创建绑定的应用实体 */
  declare createApp: BelongsToCreateAssociationMixin<AppManage>;

  /** 是否误报 */
  declare isFalsePositive: boolean;

  /** icafe卡片ID */
  declare icafeId?: string;

  /** icafe卡片所属空间 */
  declare icafeSpace?: string;

  /** 创建人 */
  declare createdBy: string | null;

  /** 更新人 */
  declare updatedBy: string | null;

  /* 创建时间 */
  declare createdAt: CreationOptional<Date>;

  /* 更新时间 */
  declare updatedAt: CreationOptional<Date>;
}

TestErrorTrace.init(
  {
    id: {
      type: DataTypes.BIGINT,
      autoIncrement: true,
      primaryKey: true
    },
    isFalsePositive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    icafeId: {
      type: DataTypes.STRING,
      allowNull: true
    },
    icafeSpace: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdBy: {
      type: DataTypes.STRING,
      allowNull: true
    },
    updatedBy: {
      type: DataTypes.STRING,
      allowNull: true
    },
    createdAt: DataTypes.DATE,
    updatedAt: DataTypes.DATE
  },
  {
    sequelize: db,
    tableName: 'test_error_trace',
    modelName: 'TestErrorTrace',
    freezeTableName: true,
    underscored: true,
    timestamps: true
  }
);

AppManage.hasMany(TestErrorTrace, {
  sourceKey: 'service_id',
  foreignKey: 'appId',
  /** 指定虚拟外键，但不添加外键约束，因为线上环境数据库不允许设置外键 */
  constraints: false
});

TestErrorTrace.belongsTo(AppManage, {
  targetKey: 'service_id',
  foreignKey: 'appId',
  constraints: false
});

TestCase.hasOne(TestErrorTrace, {
  foreignKey: 'testCaseId',
  constraints: false
});

TestErrorTrace.belongsTo(TestCase, {
  foreignKey: 'testCaseId',
  constraints: false
});

Testing.hasMany(TestErrorTrace, {
  foreignKey: 'testId',
  constraints: false
});

TestErrorTrace.belongsTo(Testing, {
  foreignKey: 'testId',
  constraints: false
});

TestErrorTrace.sync();
