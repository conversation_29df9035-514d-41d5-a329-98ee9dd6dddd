import { redisClient } from "../client";
import { Api } from "../service";
import Container from "typedi";

export const refreshRedisRules = async (serviceId: string, env: string) => {

  try {
    const redisInstance = Container.get(redisClient);
    const {items = []} = await Api.getApiExemptionRules({serviceId, env, isFullData: true});
    await redisInstance.set(`api_exemption_rules:${serviceId}:${env}`, items);
  } catch (error) {
    console.error(error);
  }

}


export const getRules = async (serviceId: string, env: string):Promise<ExemptionRule[]> => {

  let rules: any = [];
  try {
    const redisInstance = Container.get(redisClient);
    let redisData: any = await redisInstance.get(`api_exemption_rules:${serviceId}:${env}`);
    if (!redisData) {
      await refreshRedisRules(serviceId, env);
    }
    redisData = await redisInstance.get(`api_exemption_rules:${serviceId}:${env}`);
    try {
      rules = JSON.parse(redisData);
    } catch (error) {
      rules = [];
    }
  } catch (error) {
    console.error(error)
  }
  return rules || [];
}


// target 是否满足rule的条件
export function exemptionValidate(rules: any[], target: {code?:string, message?:string, href?:string, region?:string, user?: string}) {

  const {code, message, href, user, region} = target;
  return rules.some(rule => {
    return (!rule.code || rule.code.split(',').includes(code))
      && (!rule.href || isUrlMatch(href || '', rule.href) )
      && (!rule.message || isUrlMatch(message || '', rule.message))
      && (!rule.user || rule.user.split(',').includes(user))
      && (!rule.region || rule.region.split(',').includes(region))
  })
}

export function isUrlMatch(target: string, patten: string): boolean {

  try {
    const sLen = `${target}`.length;
    const pLen = `${patten}`.length;
    if (pLen === 0) {
      return sLen === 0;
    }
    const dp: boolean[][] = [new Array(sLen + 1).fill(false)];
    dp[0][0] = true;
    let num = 0;
    while (num < pLen && patten[num] === "*") {
      num++;
      dp[num] = new Array(sLen + 1).fill(true);
    }
    for (let i = num + 1; i <= pLen; i++) {
      dp[i] = new Array(sLen + 1).fill(false);
      const str1 = patten[i - 1];
      for (let j = 1; j <= sLen; j++) {
        if (str1 === "*") {
          dp[i][j] = dp[i - 1][j] || dp[i][j - 1];
        } else if (str1 === target[j - 1] || str1 === "?") {
          dp[i][j] = dp[i - 1][j - 1];
        }
      }
    }
    return dp[pLen][sLen];
  } catch (error) {
    console.log('处理参数报错：', target, patten)
    console.error(error);
    return false;
  }
  
}