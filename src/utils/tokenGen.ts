/**
 * @file utils/TokenGen.ts
 * @desc 生成API Key
 */

import {randomBytes, createHash, timingSafeEqual} from 'node:crypto';
import {promisify} from 'node:util';
import bs58 from 'bs58';

const randomBytesAsync = promisify(randomBytes);

export interface TokenGeneratorOptions {
  keyPrefix: string;
  shortTokenPrefix?: string;
  shortTokenLength?: number;
  longTokenLength?: number;
}

class TokenGenerator {
  keyPrefix: string;
  shortTokenPrefix: string;
  shortTokenLength: number;
  longTokenLength: number;
  version = 'v1'

  constructor(options: TokenGeneratorOptions) {
    this.keyPrefix = options.keyPrefix;
    this.shortTokenPrefix = options.shortTokenPrefix ?? '';
    this.shortTokenLength = options.shortTokenLength ?? 8;
    this.longTokenLength = options.longTokenLength ?? 24;
  }

  static extractVersion(token: string) {
    return token.split('_')?.[0];
  }

  static extractPrefix(token: string) {
    return token.split('_')?.[1];
  }

  static extractShortToken(token: string) {
    return token.split('_')?.[2];
  }

  static extractLongToken(token: string) {
    return token.split('_').slice(-1)?.[0];
  }

  static getTokenDetails(token: string) {
    return {
      longToken: TokenGenerator.extractLongToken(token),
      shortToken: TokenGenerator.extractShortToken(token),
      longTokenHash: TokenGenerator.hashToken(
        TokenGenerator.extractLongToken(token)
      ),
      token
    };
  }

  static hashToken2Buffer(token: string) {
    return createHash('sha256').update(token).digest();
  }

  static hashToken(token: string) {
    return TokenGenerator.hashToken2Buffer(token).toString('hex');
  }

  static validateToken(token: string, expectedLongTokenHash: string) {
    return timingSafeEqual(
      Buffer.from(expectedLongTokenHash, 'hex'),
      TokenGenerator.hashToken2Buffer(TokenGenerator.extractLongToken(token))
    );
  }

  async getToken() {
    if (!this.keyPrefix) {
      throw Error('keyPrefix is required to generate api key.');
    }

    const shortTokenPrefix = this.shortTokenPrefix;
    const shortTokenLength = this.shortTokenLength;
    const longTokenLength = this.longTokenLength;

    const [shortTokenBytes, longTokenBytes] = await Promise.all([
      randomBytesAsync(this.shortTokenLength),
      randomBytesAsync(this.longTokenLength)
    ]);

    const shortToken = (
      shortTokenPrefix +
      bs58
        .encode(shortTokenBytes)
        .padStart(shortTokenLength, '0')
        .slice(0, shortTokenLength)
    ).slice(0, shortTokenLength);
    const longToken = bs58
      .encode(longTokenBytes)
      .padStart(longTokenLength, '0')
      .slice(0, longTokenLength);

    const longTokenHash = TokenGenerator.hashToken(longToken);
    const token = `${this.version}_${this.keyPrefix}_${shortToken}_${longToken}`;

    return {
      token,
      shortToken,
      longToken,
      longTokenHash
    };
  }
}

export default TokenGenerator;
