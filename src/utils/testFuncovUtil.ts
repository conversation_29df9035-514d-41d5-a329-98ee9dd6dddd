/**
 * 生成功能覆盖率统计数据
 */

import {v4 as uuidV4} from 'uuid';

interface TreeNode {
  id: string;
  type: TestNodeType;
  name: string;
  doc: string;
  path?: string;
  level?: number;
  description: string;
  features?: TreeNode[];
  [key: string]: any;
}

/** 测试节点类型 */
export enum TestNodeType {
  CASE = 'case',
  GROUP = 'group'
}

/** 测试用例状态 */
export enum TestCaseStatus {
  COVERED = 'COVERED',
  UNCOVERED = 'UNCOVERED'
}

/**
 * 生成功能覆盖率统计数据
 * @param ds 覆盖率原始json数据
 * @returns
 */
export const genAutoTestFuncovStatisic = (trees: TreeNode[] | null) => {
  let rootGroupCount = 0;
  let totalCount = 0;
  let coveredCount = 0;
  let uncoveredCount = 0;

  if (!trees || !Array.isArray(trees) || trees.length === 0) {
    return {
      rootGroupCount,
      totalCount,
      coveredCount,
      uncoveredCount
    };
  }

  trees.forEach((node: TreeNode) => {
    rootGroupCount++; // 根用例组统计
    function transformFeatures(features: TreeNode[] | undefined | null) {
      features &&
        features.forEach((feature: TreeNode) => {
          if (feature.type === TestNodeType.CASE) {
            totalCount++; // 总用例统计
            feature.status === TestCaseStatus.COVERED ? coveredCount++ : uncoveredCount++; // 已覆盖用例/未覆盖用例统计
          }
          if (feature.type === TestNodeType.GROUP) {
            transformFeatures(feature.features);
          }
        });
    }
    transformFeatures(node.features);
  });

  return {
    rootGroupCount,
    totalCount,
    coveredCount,
    uncoveredCount
  };
};

/** 生成测试用例组ID */
export const genTestGroupId = (appId: string): string => `${appId}-group-${uuidV4()}`;

/** 生成测试用例ID */
export const genTestCaseId = (appId: string): string => `${appId}-case-${uuidV4()}`;
