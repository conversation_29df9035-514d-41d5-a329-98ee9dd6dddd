import {readFileSync, readdirSync} from 'fs';
import path from 'path';

export function getSnapshotLog(uuid: string) {
  try {
    const files = readdirSync(
      path.resolve(__dirname, '../../../logs/snapshot'),
      'utf-8'
    );
    const regExp = new RegExp('^' + uuid);
    const fileName = files.find(item => regExp.test(item));
    if (fileName) {
      const log = readFileSync(
        path.resolve(__dirname, `../../../logs/snapshot/${fileName}`),
        'utf-8'
      );
      const res = JSON.parse(log).snapshotEvent;

      if (res) {
        return {
          success: true,
          data: res
        };
      }
    }

    return {
      success: false,
      data: '找不到对应的录屏信息'
    };
  } catch (error: any) {
    return {
      success: false,
      data: error.message
    };
  }
}
