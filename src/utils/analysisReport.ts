import {ESClient} from '../client';
import {
  PAGE_INDEX,
  API_INDEX,
  envs,
  allServiceList,
  CONSOLE_CLIENT
} from './const';
import {Api, Base, Page} from '../service';

// todo:定时生成报告时，应该按集群获取所有服务，这里需要动态调整
const getServices = async (
  index: string,
  startTime: number,
  endTime: number
) => {
  const query: any = {
    bool: {
      must: [],
      filter: [
        {
          range: {
            '@timestamp': {
              gte: startTime,
              lte: endTime
            }
          }
        }
      ]
    }
  };
  let serviceTotal = await ESClient[CONSOLE_CLIENT].search(index, {
    size: 1,
    from: 1,
    body: {
      query,
      aggs: {
        total: {
          cardinality: {
            field: 'baseInfo.serviceId'
          }
        }
      }
    }
  });
  serviceTotal = serviceTotal?.body?.aggregations?.total?.value || 0;
  if (!serviceTotal) {
    return [];
  }
  const services: any = await ESClient[CONSOLE_CLIENT].search(index, {
    size: 1,
    from: 1,
    body: {
      query,
      aggs: {
        group_by: {
          terms: {
            field: 'baseInfo.serviceId',
            size: serviceTotal
          }
        }
      }
    }
  });
  return services?.body?.aggregations?.group_by?.buckets?.map(
    (item: any) => item.key
  );
};

export const getServiceApiReport = async (
  id: string,
  env: string,
  startTime: number,
  endTime: number
) => {
  const esType = allServiceList.some(item => item.serviceId === id)
    ? CONSOLE_CLIENT
    : '';
  const baseQuery = {
    serviceId: id,
    startTime,
    endTime,
    env,
    perPage: 100,
    page: 1
  };
  const apiBaseDaily = await Api.getDailyApiInfo(esType, baseQuery);
  const apiBase = await Api.getApiInfo(esType, baseQuery);
  const apiUrlAggs = await Api.getAggsData(esType, {
    ...baseQuery,
    aggType: 'api'
  });
  const apiPageAggs = await Api.getAggsData(esType, {
    ...baseQuery,
    aggType: 'page'
  });
  const apiRegionAggs = await Api.getAggsData(esType, {
    ...baseQuery,
    aggType: 'region'
  });
  const apiUserAggs = await Api.getAggsData(esType, {
    ...baseQuery,
    aggType: 'user'
  });
  const apiErrorAggs = await Api.getAggsData(esType, {
    ...baseQuery,
    aggType: 'error'
  });
  return {
    apiBase,
    apiBaseDaily,
    apiUrlAggs,
    apiPageAggs,
    apiRegionAggs,
    apiUserAggs,
    apiErrorAggs
  };
};

export const getServicePageReport = async (
  id: string,
  env: string,
  startTime: number,
  endTime: number
) => {
  const baseQuery = {
    serviceId: id,
    startTime,
    endTime,
    env,
    perPage: 100,
    page: 1
  };
  const esType = allServiceList.some(item => item.serviceId === id)
    ? CONSOLE_CLIENT
    : '';
  const baseInfoDaily = await Base.getBaseInfoDaily(esType, baseQuery);
  const baseInfo = await Base.getBaseInfo(esType, baseQuery);

  const pageBaseDaily = await Page.getDailyPageInfo(esType, baseQuery);
  const pageBase = await Page.getPageInfo(esType, baseQuery);

  const pageHrefAggs = await Page.getAggsData(esType, {
    ...baseQuery,
    aggType: 'page'
  });
  const pageBrowserAggs = await Page.getAggsData(esType, {
    ...baseQuery,
    aggType: 'browser'
  });
  return {
    baseInfo,
    baseInfoDaily,
    pageBase,
    pageBaseDaily,
    pageHrefAggs,
    pageBrowserAggs
  };
};

export const getAllReports = async (startTime: number, endTime: number) => {
  const apiServices: any = await getServices(API_INDEX, startTime, endTime);
  const pageServices: any = await getServices(PAGE_INDEX, startTime, endTime);
  const serviceReports: any = {};
  for (let service of apiServices) {
    if (!serviceReports[service]) {
      serviceReports[service] = {};
    }
    for (let env of envs) {
      try {
        // eslint-disable-line
        const apiReport = await getServiceApiReport(
          service,
          env,
          startTime,
          endTime
        );
        serviceReports[service][env] = {
          ...apiReport,
          ...(serviceReports[service][env] || [])
        };
      } catch (error) {}
    }
  }

  for (let service of pageServices) {
    if (!serviceReports[service]) {
      serviceReports[service] = {};
    }
    for (let env of envs) {
      try {
        // eslint-disable-line
        const pageReport = await getServicePageReport(
          service,
          env,
          startTime,
          endTime
        );
        serviceReports[service][env] = {
          ...pageReport,
          ...(serviceReports[service][env] || [])
        };
      } catch (error) {}
    }
  }

  return serviceReports;
};
