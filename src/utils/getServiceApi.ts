import {
  ApiCenter as ApiCenterDTO,
  Application as ApplicationDTO
} from '../model/apicenter';
import crypto from 'crypto';
import { redisClient } from '../client';
import { Container } from 'typedi';
import { HubInterface } from '../model';
import { Hub<PERSON><PERSON> } from '../service';

const options = {
  algorithm: 'aes-256-cbc',
  // 如果设置了 ISUDA_ENCRYPT_KEY 环境变量就用那个
  key: '2eb426fc24c3a2e3667732e8da0342b4dad85117047fd06d3afa79b6e44ee3a5',
  iv: '',
  ivLength: 16
};

// 线上pagemaker 组织固定为1  本地自行更改
const company_id = 1;
// const getApisByServiceOld = async (serviceId: string) => {
//   try {
//     let apiArr: any[] = [];
//     const appInfo: any = await ApplicationDTO.findOne({
//       where: {
//         company_id: company_id,
//         key: serviceId,
//         deleted_at: null
//       },
//       attributes: ['id', 'config']
//     });
//     if (!appInfo?.id) {
//       return apiArr;
//     }
//     const consts = getConsts(appInfo.config);
//     const apiInfos: any = await ApiCenterDTO.findAll({
//       where: {
//         company_id: company_id,
//         app_id: appInfo.id,
//         deleted_at: null
//       },
//       attributes: ['api']
//     });
//     if (!apiInfos.length) {
//       return apiArr;
//     }
//     apiInfos.forEach((item: any) => {
//       const url = replaceConsts(item.api.url, consts);
//       if (/\/\*\*|\/\*|\/\${p\..+}/g.test(url)) {
//         apiArr.push({
//           url: removeHostFromUrl(url),
//           method: item.api.method
//         });
//       }
//     });
//     return apiArr;
//   } catch (error) {
//     console.error(error);
//     return [];
//   }
// };

const getApisByService = async function name(serviceId: string) {
  try {
    let apis: any = await HubInterface.findAll({
      where: {
        service_name: serviceId
      }
    });
    return apis.map((el: any) => {
      return {
        method: el.method,
        url: el.prefix + el.path
      };
    });
  } catch (error) {
    console.error(error);
    return [];
  }
}

const isChildPath = (parentPath: string, childPath: string) => {
  // 将 parentPath 转换为正则表达式
  // ** => .+
  // ${p.xxxxx} => [^/]+
  // * => [^/]+
  const parentRegex = parentPath
    .replace(/\*\*/g, '.+')
    .replace(/\${p\..+}/g, '[^/]+')
    .replace(/\*/g, '[^/]+');
  // 使用正则表达式进行匹配
  const parentPattern = new RegExp(`^${parentRegex}$`);
  return (
    parentPattern.test(childPath) || (parentPath === '/' && childPath === '/')
  );
};

const getConsts = (config: any) => {
  const data = Buffer.from(config.ciphertext, 'base64');
  const iv = data.slice(0, options.ivLength);
  const decipher = crypto.createDecipheriv(
    options.algorithm,
    Buffer.from(options.key, 'hex'),
    iv
  );
  const start = decipher.update(data.slice(options.ivLength));
  const final = decipher.final();
  const result = Buffer.concat([start, final]).toString('utf8');
  let obj: { [propName: string]: string } = {};
  const arr = JSON.parse(result).consts || [];
  arr.forEach((item: any) => {
    obj[item.var] = item.value;
  });
  if (!obj['API_ENDPOINT']) {
    obj['API_ENDPOINT'] = '';
  }
  return obj;
};

const replaceConsts = (
  text: string,
  consts: {
    [propName: string]: string;
  } = {}
) => {
  if (!text || typeof text !== 'string') {
    return text;
  }

  return text.replace(
    /(\\)?\$(?:([A-Z][A-Z0-9_\-]*)|{([A-Z][A-Z0-9_\-+:\s]*?)})/g,
    (_: any, escape, key1, key2) => {
      const key = key1 || key2;

      return escape ? _.substring(1) : envFilter(key, consts, _);
    }
  );
};

const envFilter = (
  key: string,
  consts: {
    [propName: string]: string;
  },
  raw: string
) => {
  const parts = key.split(':');

  if (parts.length > 1) {
    key = parts[0].trim();
    parts[1] = parts[1].trim();
    const word = parts[1].substring(1);
    const directive = parts[1].substring(0, 1);

    if (directive === '+') {
      return consts[key] ? consts[word] ?? word : '';
    } else if (directive === '-') {
      return consts[key] || (consts[word] ?? word);
    }
  }

  return consts[key] ?? raw;
};

const removeHostFromUrl = (url: string) => {
  const reg = /^(http(s)?:\/\/)[^\/]+\//gi;
  const _url = url.replace(reg, '/');
  return _url;
};

export const refreshApiRedis = async (prefixs: string[]) => {
  try {
    const redisInstance = Container.get(redisClient);
    const allPrefix = await HubApi.getAllPrefix();
    await redisInstance.set('apicenter:allprefixs', allPrefix);

    const res = await HubApi.getInterfacesByPrefixs(prefixs);
    const items: any = []
    Object.keys(res).forEach(async key => {
      if (key) {
        items.push(await redisInstance.set(`apicenter:prefix:${key}`, res[key]));
      }
    });
    return await Promise.all(items)
  } catch (error) {
    console.error(error);
  }

}

const getApisByPrefix = async (prefix: string) =>  {
  try {
    const redisInstance = Container.get(redisClient);
    const hasRedisData = await redisInstance.exist(`apicenter:prefix:${prefix}`);

    if (!hasRedisData) {
      const result = await HubApi.getInterfacesByPrefixs([prefix]);
      const list = result?.[prefix];
      await redisInstance.set(`apicenter:prefix:${prefix}`, list);
    }
    try {
      const res = await redisInstance.get(`apicenter:prefix:${prefix}`);

      if (res) {
        return JSON.parse(res);
      }
    } catch (error) {
    }
    return  null;
  } catch (error) {
    return null
  }
  
}

export const getMatchedApis = async (api: { [propsName: string]: string }) => {

  try {
    const redisInstance = Container.get(redisClient);
    let list: string[] = [];
    const hasprefixs = await redisInstance.exist(`apicenter:allprefixs`);

    if (!hasprefixs) {
      // todo
      await refreshApiRedis([]);
    }

    const allprefixs = await redisInstance.get(`apicenter:allprefixs`);

    if (!allprefixs) {
      return [];
    }
    try {
      list = JSON.parse(allprefixs);
    } catch (error) {
      return [];
    }

    if (list && list.length) {
      const matchedPrefix = list.filter(el => api.url.includes(el));

      const items = matchedPrefix.map(item => getApisByPrefix(item));

      const res = await Promise.all(items)

      let results: any = [];

      res.forEach(el => {

        if (!el) {
          return 
        }

        try {
          results = results.concat(el);
        } catch (error) {
          console.error(error)
         }
      });
      return results;
    }
    return [];

  } catch (error) {
    return [];
  }
}

export const getRegUrl = async (
  serviceId: string,
  api: { [propsName: string]: string }
) => {
  try {
    const apis = await getMatchedApis(api);

    let pathname = api.url;
    for (let i = 0; i < apis.length; i++) {
      const item = apis[i];
      const ItemMethod = item?.method?.toLowerCase();
      const ApiMethod = api?.method?.toLowerCase();
      if (ItemMethod && ApiMethod && ItemMethod === ApiMethod) {
        if (isChildPath(item.url, api.url)) {
          pathname = item.url;
          break;
        }
      }
    }
    return pathname;
  } catch (err) {
    console.error(err);
    return api.url;
  }
};

/**
 * 将类似 127.0.0.1:8080,*********:8081 这样的字符串转成对象
 * @param hosts
 */
export function generateRedisClusterHosts(hosts: string) {
  return hosts.split(',').map(hostPort => {
    const split = hostPort.trim().split(':');
    return {
      host: split[0],
      port: parseInt(split[1], 10)
    };
  });
}
