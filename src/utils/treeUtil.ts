/**
 * 树结构操作工具类
 */

interface TreeNode {
  id?: string;
  type?: 'group' | 'case';
  name?: string;
  doc?: string;
  path?: string;
  level?: number;
  description?: string;
  features?: TreeNode[];
  [key: string]: any;
}

interface TreeUtilConfig {
  id: string;
  parentId: string;
  children: string;
}

interface TreeUtilSettings {
  rever?: boolean;
}

export class TreeUtil {
  idField: string;
  pidField: string;
  childrenField: string;

  constructor(config: TreeUtilConfig = {id: 'id', parentId: 'parentId', children: 'children'}) {
    this.idField = config.id;
    this.pidField = config.parentId;
    this.childrenField = config.children;
  }

  /**
   * 遍历所有节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Function} callback 回调函数 回调参数 (node 节点对象, index 节点索引, lv 层级)
   * @param {Object} setting 扩展设置。setting.rever: Boolean（从叶子节点开始遍历）
   */
  forEach(trees: TreeNode[], callback: (node: TreeNode, i: number, l: number) => void, setting: TreeUtilSettings | null) {
    let i = 0,
        l = 1;
    const _forEach = (_trees: TreeNode[]) => {
      _trees.forEach((item: TreeNode) => {
        !setting?.rever && callback(item, i++, l);
        if (item[this.childrenField]?.length) {
          l++;
          _forEach(item[this.childrenField]);
          l--;
        }
        setting?.rever && callback(item, i++, l);
      });
    };
    _forEach(trees);
  }

  /**
   * 功能更强的遍历。return 0 可中断遍历
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Function} callback 回调函数 回调参数 (node 节点对象, index 节点索引, lv 层级, parent 父级节点, root 根节点)
   * @param {Object} setting 扩展设置。setting.rever: Boolean（true->从叶子节点开始遍历）
   */
  foreach(trees: TreeNode[], callback: (node: TreeNode, i: number, l: number, parent?: TreeNode | null, root?: TreeNode) => void, setting: TreeUtilSettings | null) {
    let breakFlag: any,
        i = 0,
        l = 1;
    const _foreach = (node: TreeNode, parent: TreeNode | null, root: TreeNode) => {
      if (breakFlag === 0) return;
      if (!setting?.rever) breakFlag = callback(node, i++, l, parent, root);
      if (node[this.childrenField]?.length) {
        l++;
        for (let index = 0; index < node[this.childrenField].length; index++) {
          _foreach(node[this.childrenField][index], node, root);
        }
        l--;
      }
      if (setting?.rever) breakFlag = callback(node, i++, l, parent, root);
    };

    for (let index = 0; index < trees.length; index++) {
      if (breakFlag === 0) return;
      _foreach(trees[index], null, trees[index]);
    }
  }

  /**
   * 树转列表
   * @param {TreeNode[]} trees 树结构数据列表
   * @returns{TreeNode[]} 数据列表
   */
  treeToList(trees: TreeNode[]): TreeNode[] {
    if (!trees?.length) {
      return trees;
    }
    const list: TreeNode[] = [];
    this.forEach(
      trees,
      (item: TreeNode) => {
        list.push(item);
      },
      null
    );
    return list;
  }

  /**
   * 过滤节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Function} callback 回调函数 参数为节点对象，需要返回布尔类型
   * @returns {TreeNode[]} 满足条件的节点（包含祖先，保持树状结构，与UI树组件的过滤效果相同），没有则返回空数组
   */
  filter(trees: TreeNode[], callback: (node: TreeNode) => boolean): TreeNode[] {
    const _filterTree = (node: TreeNode) => {
      const nodeChildren = node[this.childrenField];
      if (callback(node)) {
        const result = {...node};
        result[this.childrenField] = nodeChildren?.length
          ? nodeChildren.map((child: TreeNode[]) => _filterTree(child)).filter(Boolean)
          : nodeChildren;
        return result;
      }

      let filteredChildren = [];
      if (nodeChildren?.length) {
        filteredChildren = nodeChildren.map((child: TreeNode) => _filterTree(child)).filter(Boolean);
      }

      if (filteredChildren.length) {
        const result = {...node};
        result[this.childrenField] = filteredChildren;
        return result;
      }

      return null;
    };

    return trees.map((root: TreeNode) => _filterTree(root)).filter(Boolean) as TreeNode[];
  }

  /**
   * 查找首个满足条件的节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Function} callback 回调函数 参数为节点对象，需要返回布尔类型
   * @returns {Object} 首个满足条件的节点对象，没有则返回 null
   */
  find(trees: TreeNode[], callback: (node: TreeNode) => boolean): TreeNode | null {
    let node = null;
    this.foreach(
      trees,
      (item: TreeNode) => {
        if (callback(item)) {
          node = item;
          return 0;
        }
      },
      null
    );
    return node;
  }

  /**
   * 查找所有满足条件的节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Function} callback 回调函数 参数为节点对象，需要返回布尔类型
   * @returns {TreeNode[]} 所有满足条件的节点，没有则返回空数组
   */
  findAll(trees: TreeNode[], callback: (node: TreeNode) => boolean): TreeNode[] | null {
    const nodes: TreeNode[] = [];
    this.forEach(
      trees,
      (item: TreeNode) => {
        if (callback(item)) {
          nodes.push(item);
        }
      },
      null
    );
    return nodes;
  }

  /**
   * 根据 ID 查询其节点的根节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {*} idValue id值
   * @returns{Object} id对应节点的根节点，没有则返回 null
   */
  getRootById(trees: TreeNode[], idValue: string): TreeNode | null {
    let rootNode = null;
    this.foreach(
      trees,
      (node: TreeNode, index: number, lv: number, parent?: TreeNode | null, root?: TreeNode) => {
        if (node[this.idField] === idValue) {
          rootNode = root;
          return 0;
        }
      },
      null
    );
    return rootNode;
  }

  /**
   * 根据 ID 查询其节点的父级节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {*} idValue id值
   * @returns {Object} id对应节点的父节点，没有则返回 null
   */
  getParentById(trees: TreeNode[], idValue: string): TreeNode | null {
    const _parent = (_trees: TreeNode[], parent: TreeNode | null): TreeNode | null => {
      for (let index = 0; index < _trees.length; index++) {
        const node = _trees[index];
        if (node[this.idField] === idValue) return parent;
        if (node[this.childrenField]?.length) {
          const parentNode = _parent(node[this.childrenField], node);
          if (parentNode) return parentNode;
        }
      }
      return null;
    };
    return _parent(trees, null);
  }

  /**
   * 根据 ID 查询其节点的祖籍节点列表（包含自身节点）
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {*} idValue id值
   * @returns 节点列表
   */
  getAncestorsById(trees: TreeNode[], idValue: string) {
    const filters = this.filter(trees, (node: TreeNode) => node[this.idField] === idValue);
    if (!filters?.length) return [];
    if (filters.length !== 1) {
      console.warn('tree-util --> getAncestorsById warn message: The id may not be unique');
    }
    return this.treeToList(filters);
  }

  /**
   * 查找所有叶子节点，所有 children 字段为空或空数组的节点都为叶子节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @returns {TreeNode[]} 叶子节点列表
   */
  getLeafs(trees: TreeNode[]): TreeNode[] {
    const leafs: TreeNode[] = [];
    this.forEach(
      trees,
      (node: TreeNode) => {
        if (!node[this.childrenField]?.length) {
          leafs.push(node);
        }
      },
      null
    );
    return leafs;
  }

  /**
   * 插入新的节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Object} node 需要插入的节点
   * @param {?*} targetNodeId 插入目标节点的id(可选)
   */
  insertNode(trees: TreeNode[], node: TreeNode, targetNodeId = node[this.pidField]) {
    const targetNode: TreeNode | null = this.find(trees, (item: TreeNode) => item[this.idField] === targetNodeId);
    if (targetNode) {
      if (targetNode[this.childrenField]) {
        targetNode[this.childrenField].push(node);
      } else {
        targetNode[this.childrenField] = [node];
      }
    } else {
      trees.push(node);
    }
  }

  /**
   * 修改节点信息
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Object} node 需要修改的节点
   */
  updateNode(trees: TreeNode[], node: TreeNode) {
    const targetNode: TreeNode | null = this.find(trees, (item: TreeNode) => item[this.idField] === node[this.idField]);
    if (!targetNode) {
      console.warn('tree-utils --> updateNode warn message: Target node not found');
      return;
    }
    if (this.pidField && targetNode[this.pidField] !== node[this.pidField]) {
      this.removeNode(trees, targetNode[this.idField]);
      Object.keys(node).forEach(key => {
        targetNode[key] = node[key];
      });
      this.insertNode(trees, targetNode);
    } else {
      Object.keys(node).forEach(key => {
        targetNode[key] = node[key];
      });
    }
  }

  /**
   * 保存节点(根据节点id判断，有则修改，没有则插入)
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Object} node 需要插入/修改的节点
   */
  saveNode(trees: TreeNode[], node: TreeNode) {
    const targetNode = this.find(trees, (item: TreeNode) => item[this.idField] === node[this.idField]);
    if (targetNode) {
      this.updateNode(trees, node);
    } else {
      this.insertNode(trees, node);
    }
  }

  /**
   * 移除节点
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {*} targetNodeId 需要删除的节点的 id
   */
  removeNode(trees: TreeNode[], targetNodeId: string) {
    let childrenNodes;
    if (trees.find((node: TreeNode) => node[this.idField] === targetNodeId)) {
      childrenNodes = trees;
    } else {
      const parentNode = this.getParentById(trees, targetNodeId);
      if (parentNode) {
        childrenNodes = parentNode[this.childrenField];
      }
    }
    if (childrenNodes?.length) {
      let targetIndex;
      for (let index = 0; index < childrenNodes.length; index++) {
        if (childrenNodes[index][this.idField] === targetNodeId) {
          targetIndex = index;
          break;
        }
      }
      childrenNodes.splice(targetIndex, 1);
    }
  }

  /**
   * 排序
   * @param {TreeNode[]} trees 树结构数据列表
   * @param {Function} callback 回调函数。参数为(节点对象1,节点对象2)，需要数字类型
   */
  sort(trees: TreeNode[], callback: (nodeA: TreeNode, nodeB: TreeNode) => number) {
    const _sort = (_trees: TreeNode[]) => {
      _trees.forEach((node: TreeNode) => {
        if (node[this.childrenField]?.length) {
          _sort(node[this.childrenField]);
        } else {
          return;
        }
        node[this.childrenField].sort(callback);
      });
    };
    _sort(trees);
    trees.sort(callback);
  }

  /**
   * 获取全部节点祖籍
   * @param {*[]} trees 树结构数据列表
   * @returns [{id: [root, ..., parent2, parent1, node]}]
   */
  getAncestors(trees: TreeNode[]) {
    const result: TreeNode = {};
    this.foreach(
      trees,
      (route: TreeNode, i: number, l: number, parent?: TreeNode | null) => {
        if (parent) {
          result[route[this.idField]] = [...result[parent[this.idField]], route];
        } else {
          result[route[this.idField]] = [route];
        }
      },
      null
    );
    return result;
  }
}
