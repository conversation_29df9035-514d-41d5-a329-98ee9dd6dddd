import xss, {IFilterXSSOptions} from 'xss';

// 去掉url中的query信息，比如?a=b&c=d&e=f
type urlType = 'page' | 'api';
export const urlHandler = (url: string, type: urlType): string => {
  if (!url) {
    return '';
  }
  if (type === 'page') {
    const hashFlagIndex = url.indexOf('#');
    if (hashFlagIndex > -1) {
      const pre = url.substring(0, hashFlagIndex);
      const lat = url.substring(hashFlagIndex + 1);
      const noQueryPre = pre.substring(
        0,
        pre.indexOf('?') > -1 ? pre.indexOf('?') : pre.length
      );
      const noQueryLat = lat.substring(
        0,
        lat.indexOf('?') > -1
          ? lat.indexOf('?')
          : lat.indexOf('~') > -1
          ? lat.indexOf('~')
          : lat.length
      );
      return `${noQueryPre}${
        noQueryPre.endsWith('/') ? '#' : '/#'
      }${noQueryLat}`;
    } else {
      const queryIndex = url.indexOf('?');
      return url.substring(0, queryIndex > -1 ? queryIndex : url.length);
    }
  } else if (type === 'api') {
    return url.substring(
      0,
      url.indexOf('?') > -1 ? url.indexOf('?') : url.length
    );
  } else {
    return '';
  }
};
/*
  执行方式：直接复制到kibana开发工具中
  脚本作用：
    将completeurl中的query信息，比如?a=b&c=d&e=去掉并存为url字段
    将baseInfo.href中的query信息去掉，并存为将baseInfo._href
    responseError.message.gobal =   ctx._source.responseError.message.global || ctx._source.responseError.message.noSession || || ctx._source.responseError.message.description || '';
*/
const normallizeOldUrlError = `
POST /api-log/_update_by_query
{
  "script": {
        "source": "String url = ctx._source.baseInfo.href;if (url != null) {int urlLength = url.length(); int hashFlagIndex = url.indexOf('#'); if (hashFlagIndex > -1) {String pre = url.substring(0, hashFlagIndex); int latStartIndex = hashFlagIndex + 1; String lat = url.substring(latStartIndex, urlLength);int preIndex = (pre.indexOf('?') > -1) ? pre.indexOf('?') : pre.length();int latIndex = (lat.indexOf('?') > -1) ? lat.indexOf('?') : ((lat.indexOf('~') > -1) ? lat.indexOf('~') : lat.length());String noQueryPre = pre.substring(0, preIndex);String noQueryLat = lat.substring(0, latIndex);String baseInfoHref = noQueryPre + (noQueryPre.endsWith('/') ? '#' : '/#') + noQueryLat;ctx._source.baseInfo._href = baseInfoHref;} else {int queryIndex = (url.indexOf('?') > -1) ? url.indexOf('?') : urlLength; ctx._source.baseInfo._href = url.substring(0, queryIndex);}}String api = ctx._source.completeUrl;int apiQueryIndex = (api.indexOf('?') > -1) ? api.indexOf('?') : api.length();ctx._source.url = api.substring(0, apiQueryIndex);String global = ctx._source.responseError.message?.global;String noSession = ctx._source.responseError.message?.noSession;String description = ctx._source.responseError.message?.description;if (global=='') {if(noSession != ''){ctx._source.responseError.message.global=noSession}if(description!=''){ctx._source.responseError.message.global=description}}",
        "lang": "painless"
      }
}
`;
// 刷新沙盒域名但是env online的数据
const normallizeOldEnvHostMap = `
POST /api-log/_update_by_query
{
  "query": {
    "bool":{

      "filter": [
        {
          "term": {
            "baseInfo.env.keyword": "consoleOnline"
          }
        },
        {
          "prefix": {
            "baseInfo.href.keyword": "https://qasandbox.bcetest.baidu.com"
          }
        }
      ]
    }
  },
  "script": {
        "source": "ctx._source.baseInfo.env = 'consoleOffline'",
        "lang": "painless"
      }
}
`;

export const handlerMergeArr = (attr: string, oldArr: any[], newArr: any[]) => {
  const mergedArray = oldArr.concat(newArr);
  const result = mergedArray.reduce((acc, current) => {
    const found = acc.find((item: any) => item[attr] === current[attr]);
    if (found) {
      Object.assign(found, current);
      return acc;
    }
    return acc.concat([current]);
  }, []);

  return result;
};

/**
 * @desc 计算百分比总和为1，使用EChart的最大余额法
 * @param arr 数据数组
 * @param index 被计算百分比的数据的数组下标
 * @param precision 精度，保留小数的位数
 * @returns 百分比
 */
export const handlerPercent = (
  arr: any[],
  index: number,
  precision: number
) => {
  if (!arr[index]) {
    return 0;
  }

  const sum = arr.reduce((acc, val) => acc + (isNaN(val) ? 0 : val), 0);

  if (sum === 0) {
    return 0;
  }

  const digits = Math.pow(10, precision);
  const quotaArr = arr.map(
    (val: any) => ((isNaN(val) ? 0 : val) / sum) * digits
  );
  // 向下取整数后的数组
  const seatsArr = quotaArr.map((val: any) => Math.floor(val));
  let currentSum = seatsArr.reduce((acc, val) => acc + val, 0);
  // 向下取整数后的余数数组
  const remainderArr = quotaArr.map(
    (val: any, index: number) => val - seatsArr[index]
  );

  while (currentSum < digits) {
    const max = Math.max(...remainderArr);
    const maxIndex = remainderArr.indexOf(max);
    ++seatsArr[maxIndex];
    remainderArr[maxIndex] = 0;
    ++currentSum;
  }

  return seatsArr[index] / digits;
};
/**
 * 将时间戳转换为秒数
 *
 * @param timestamp 时间戳，单位为毫秒
 * @returns 返回转换后的秒数
 */
export function timestampToSecond(timestamp: number) {
  return Math.floor(timestamp / 1000);
}

/**
 * 将UNIX时间戳转化为JS Date对象
 */
export function convertTimestampToDate(timestamp: number): Date {
  if (timestamp.toString().length === 10) {
    // 秒级别时间戳，乘以 1000 转换为毫秒级别
    return new Date(timestamp * 1000);
  } else if (timestamp.toString().length === 13) {
    // 毫秒级别时间戳，直接转换
    return new Date(timestamp);
  } else {
    return new Date(timestamp);
  }
}

export function omitNil<T extends Record<string, any>>(
  obj: T
): {[K in keyof T]?: Exclude<T[K], null | undefined>} {
  const result: any = {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      if (value !== null && value !== undefined) {
        result[key] = value;
      }
    }
  }

  return result as {[K in keyof T]?: Exclude<T[K], null | undefined>};
}

/**
 * 计算百分比
 */
export function calculatePercentage(
  currentValue: any,
  baseValue: any
): number | null {
  if (typeof currentValue !== 'number' || typeof baseValue !== 'number') {
    return null;
  }

  // 处理 NaN 和无穷大情况
  if (
    isNaN(currentValue) ||
    isNaN(baseValue) ||
    !isFinite(currentValue) ||
    !isFinite(baseValue)
  ) {
    return null;
  }

  // 防止前期值为负数的异常情况
  if (baseValue < 0) {
    return null;
  }

  // 防止前期值为 0 的情况，避免除以 0
  if (baseValue === 0) {
    return null;
  }

  const percentageChange = (currentValue / baseValue) * 100;

  return parseFloat(percentageChange.toFixed(2));
}

/**
 * 计算环比变化的百分比
 * @returns 环比变化的百分比，若输入无效则返回null
 */
export function calculatePercentageChange(
  currentValue: any,
  previousValue: any
): number | null {
  if (typeof currentValue !== 'number' || typeof previousValue !== 'number') {
    return null;
  }

  // 处理 NaN 和无穷大情况
  if (
    isNaN(currentValue) ||
    isNaN(previousValue) ||
    !isFinite(currentValue) ||
    !isFinite(previousValue)
  ) {
    return null;
  }

  // 防止前期值为负数的异常情况
  if (previousValue < 0) {
    return null;
  }

  // 防止前期值为 0 的情况，避免除以 0
  if (previousValue === 0) {
    return null;
  }

  const percentageChange =
    ((currentValue - previousValue) / previousValue) * 100;

  return parseFloat(percentageChange.toFixed(2));
}

/** 将每一项Base64编码后再用Base64不支持的字符连接 */
export function safeJoin(data: string[]) {
  return Array.isArray(data)
    ? data.map(item => Buffer.from(item).toString('base64')).join('|')
    : '';
}

/** 将Base64编码后的字符串用Base64不支持的字符分割 */
export function safeSplit(data: string) {
  return typeof data === 'string'
    ? data.split('|').map(item => Buffer.from(item, 'base64').toString())
    : [];
}

export function xssFilter(value: unknown, options?: IFilterXSSOptions) {
  if (!value || typeof value !== 'string') {
    return '';
  }

  return xss(value, options);
}

export function getJSONFromText(value: string | null, fallback: any) {
  if (value == null) {
    return fallback;
  }

  if (typeof value !== 'string') {
    return value;
  }

  try {
    return JSON.parse(value);
  } catch (error) {
    return fallback;
  }
}

/** 是否为本地开发环境 */
export function isDev() {
  return process.env.NODE_ENV === 'development';
}

/** 是否为沙盒环境 */
export function isSandbox() {
  return process.env.NODE_ENV === 'sandbox';
}

/** 是否为本地 or 沙盒开发环境 */
export function isDevOrSandbox() {
  return (
    process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'sandbox'
  );
}

/** 是否为生产环境 */
export function isProd() {
  return process.env.NODE_ENV === 'production';
}
