/**
 * @file utils/error.ts
 * @desc API错误处理
 */

export const ErrorMsg = Object.freeze({
  InvalidEmptyParameter: '参数验证错误，参数不能为空',
  InvalidNonObjectParameter: '参数验证错误，参数格式不合法',
  InvalidParameter: (name: string) => `参数验证错误，缺少「${name}」参数。`,
  InvalidParameterPattern: (name: string) => `参数「${name}」不合法`,
  EmptyParameterError: '更新参数不能为空',
  NoSuchTest: '未找到指定的测试活动。',
  NoSuchApp: '未找到指定ID的应用。',
  NoSuchTestCase: '未找到指定的测试用例。',
  NoSuchTestErrorTrace: '未找到指定的测试用例标记。',
  DuplicatedTestErrorTrace: '该测试用例已标记，请勿重复添加',
  NoTestingReport: '测试报告上传失败。',
  NoSuchUser: '用户名称不存在',
  NoSuchApiKey: 'API Key不存在或已过期',
  InValidTokenParameter: 'token参数格式错误',
  InValidExpireTime: '当前token已被标记为废弃，请勿重复标记',
  AuthorizationError: '鉴权认证失败，缺少合法的Authorization请求头',
  OAuthTokenNotMatchedError: '鉴权认证失败，OAuth Token不匹配',
  AuthenticationError: '不合法或已过期的API Key，鉴权认证失败',
  ApiKeyQuotaLimitation: '当前应用可用的API Key已达到配额上限',
  /** =====================调查问卷相关========================= */
  NoSuchAppSurvey: '未找到指定产品的调查问卷。',
  NoSuchSurvey: '未找到指定的调查问卷。',
  NoSuchQuestion: '未找到指定的问题。',
  NoQuestionsInSurvey: '该调查问卷未设置问题',
  DuplicatedSurveySubmission: '您已提交过调查问卷，请勿重复提交',
  InvalidQuestionId: (id: string) => `参数questionId验证错误，未找到ID为「${id}」的问题`,
  InvalidEmptyAnswer: '参数content验证错误，回答不能为空',
  InvalidRatingAnswer: '参数content验证错误，评分回答不合法',
  InvalidChoiceAnswer: '参数content验证错误，回答选项不合法',
  InvalidEssayAnswer: '参数content验证错误，回答内容不能为空',
  InCompleteRequiredAnswer: '参数answers验证错误，存在必填回答为空',
  InvalidEmptyParameterWithName: (name: string) => `参数验证错误，参数「${name}」不能为空。`,
  InvalidParameterMustBeString: (name: string) => `参数验证错误，参数「${name}」必须为字符串。`,
  InvalidParameterMaxLength: (name: string, length: number) => `参数验证错误，参数「${name}」长度必须小于等于${length}。`,
  InvalidParameterNotSupport: (name: string) => `参数「${name}」不合法，暂不支持此种类型的参数。`,
});

