import path from 'path';
export const API_INDEX = 'api-log';
export const PAGE_INDEX = 'page-log';
export const ACTION_INDEX = 'action-log';
export const PERFORMANCE_INDEX = 'performance-log';
export const SATISFY_INDEX = 'satisfy-log';

export const envs = ['consoleOnline', 'consoleOffline'];
export const ticketAction = 'ticket-jump';

export const chartType = ['funnel', 'table', 'trend'];
export const KEYWORD_TYPE = ['name', 'creator', 'updater'];

export const DEFAULT_ROOTDIR_NAME = 'logs';
export const CONSOLE_ROOTDIR_NAME = 'iaas';
export const API_LOG_DIR = path.join(
  process.cwd(),
  `../${CONSOLE_ROOTDIR_NAME}/api`
);
export const PAGE_LOG_DIR = path.join(
  process.cwd(),
  `../${CONSOLE_ROOTDIR_NAME}/page`
);
export const ACTION_LOG_DIR = path.join(
  process.cwd(),
  `../${CONSOLE_ROOTDIR_NAME}/action`
);
export const PERFORMANCE_LOG_DIR = path.join(
  process.cwd(),
  `../${CONSOLE_ROOTDIR_NAME}/performance`
);
export const SATISFY_LOG_DIR = path.join(
  process.cwd(),
  `../${CONSOLE_ROOTDIR_NAME}/satisfy`
);

export const DEFAULT_CLIENT = 'defaultClient';
export const CONSOLE_CLIENT = 'consoleEsClient';

// 切集群的时间
export const SANDBOX_IAAS_CHANGE_TIME = '2024-04-16 20:30';
export const ONLINE_IAAS_CHANGE_TIME = '2024-04-28 20:00';
export const SANDBOX_OTHER_CHANGE_TIME = '2024-08-07 19:00';
export const ONLINE_OTHER_CHANGE_TIME = '2024-08-12 00:00';

interface ServiceIO {
  serviceName: string;
  serviceId: string;
  groupType: string;
  groupName: string;
  baseTimeRange?: number[]; // 参与优化专项的产品需要制定对比周期
  targetErrorPercent?: number | null; // 参与优化专项的产品需要制定优化目标
  targetEnErrorPercent?: number | null; // 参与优化专项的产品需要制定中文报错占比优化目标
  esType?: string; // 指定base数据的es
}
// 计算产品
const BCCLIST: ServiceIO[] = [
  {
    serviceName: '计算-云服务器BCC',
    serviceId: 'bcc',
    groupType: 'IAAS',
    groupName: 'IAAS',
    // 9.23 00:00 ~ 9.29 23:59
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.015 / 100,
    targetEnErrorPercent: 0 / 100
  },
  {
    serviceName: '计算-弹性伸缩AS',
    serviceId: 'as',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.15 / 100,
    targetEnErrorPercent: null
  },
  {
    serviceName: '监控运维-云监控BCM',
    serviceId: 'bcm',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.2 / 100,
    targetEnErrorPercent: 5 / 100
  },
  {
    serviceName: '监控运维-运维编排OOS',
    serviceId: 'oos',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.15 / 100,
    targetEnErrorPercent: null
  },
  {
    serviceName: '计算-云助手',
    serviceId: 'assistant',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.015 / 100,
    targetEnErrorPercent: null
  },
  {
    serviceName: '监控运维-日志服务BLS',
    serviceId: 'bls',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.2 / 100,
    // 制定查base数据的es
    esType: CONSOLE_CLIENT,
    targetEnErrorPercent: 5 / 100
  },
  {
    serviceName: '计算-弹性裸金属服务器BBC',
    serviceId: 'bbc',
    groupType: 'IAAS',
    groupName: 'IAAS'
  },
  {
    serviceName: '计算-轻量应用服务器LS',
    serviceId: 'ls',
    groupType: 'IAAS',
    groupName: 'IAAS'
  }
];
// 网络产品
const NETWORKLIST: ServiceIO[] = [
  {
    serviceName: '网络-私有网络NETWORK',
    serviceId: 'network',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.03 / 100,
    targetEnErrorPercent: 2 / 100
  },
  {
    serviceName: '网络-弹性公网EIP',
    serviceId: 'eip',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.025 / 100,
    targetEnErrorPercent: 0.3 / 100
  },
  {
    serviceName: '网络-负载均衡BLB',
    serviceId: 'blb',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.025 / 100,
    targetEnErrorPercent: 0.6 / 100
  },
  {
    serviceName: '网络-智能云解析DNS',
    serviceId: 'dns',
    groupType: 'IAAS',
    groupName: 'IAAS'
  },
  {
    serviceName: '网络-云智能网CSN',
    serviceId: 'csn',
    groupType: 'IAAS',
    groupName: 'IAAS'
  },
  {
    serviceName: '网络-云防火墙CFW',
    serviceId: 'cfw',
    groupType: 'IAAS',
    groupName: 'IAAS'
  }
];
// 存储产品
const STORAGELIST: ServiceIO[] = [
  {
    serviceName: '存储-对象存储BOS',
    serviceId: 'bos',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.08 / 100,
    targetEnErrorPercent: 0 / 100
  },
  {
    serviceName: '存储-文件存储CFS',
    serviceId: 'cfs',
    groupType: 'IAAS',
    groupName: 'IAAS'
  },
  {
    serviceName: '存储-并行文件存储PFS',
    serviceId: 'pfs',
    groupType: 'IAAS',
    groupName: 'IAAS'
  },
  {
    serviceName: '存储-数据流转平台CLOUDFLOW',
    serviceId: 'cloudflow',
    groupType: 'IAAS',
    groupName: 'IAAS'
  },
  {
    serviceName: '存储-表格存储BTS',
    serviceId: 'bts',
    groupType: 'IAAS',
    groupName: 'IAAS'
  }
];
// 云原生产品
const CCELIST: ServiceIO[] = [
  {
    serviceName: '云原生-容器引擎CCE',
    serviceId: 'cce',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.3 / 100,
    targetEnErrorPercent: 0.8 / 100
  },
  {
    serviceName: '云原生-容器实例BCI',
    serviceId: 'bci',
    groupType: 'IAAS',
    groupName: 'IAAS'
  },
  {
    serviceName: '云原生-容器镜像服务CCR',
    serviceId: 'ccr',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.2 / 100,
    targetEnErrorPercent: 5 / 100
  },
  {
    serviceName: '云原生-Prometheus监控服务CPROM',
    serviceId: 'cprom',
    groupType: 'IAAS',
    groupName: 'IAAS',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.2 / 100,
    targetEnErrorPercent: 5 / 100
  }
];
// 媒体云产品
const MEDIA: ServiceIO[] = [
  {
    serviceName: '视频云平台-音视频处理',
    serviceId: 'mct',
    groupType: 'media',
    groupName: '视频云',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  },
  {
    serviceName: '企业服务-简单消息服务',
    serviceId: 'smsv3',
    groupType: 'media',
    groupName: '视频云'
  },
  {
    serviceName: '视频云平台-智能视联网平台',
    serviceId: 'evs',
    groupType: 'media',
    groupName: '视频云'
  },
  {
    serviceName: '视频云平台-视频创作分发平台',
    serviceId: 'videoworks',
    groupType: 'media',
    groupName: '视频云'
  },
  {
    serviceName: '视频云平台-智能直播应用平台',
    serviceId: 'lcps',
    groupType: 'media',
    groupName: '视频云'
  },
  {
    serviceName: '视频云平台-音视频直播',
    serviceId: 'lss',
    groupType: 'media',
    groupName: '视频云'
  },
  {
    serviceName: '视频云平台-智能短视频',
    serviceId: 'bvc',
    groupType: 'media',
    groupName: '视频云'
  },
  {
    serviceName: '视频云平台-实时音视频直播',
    serviceId: 'rtc',
    groupType: 'media',
    groupName: '视频云'
  }
];
const CDN: ServiceIO[] = [
  {
    serviceName: 'CDN与边缘-内容分发网络',
    serviceId: 'cdn',
    groupType: 'CDN',
    groupName: 'CDN',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.2 / 100,
    targetEnErrorPercent: 5 / 100
  },
  {
    serviceName: 'CDN与边缘-海外CDN',
    serviceId: 'cdn_aboad',
    groupType: 'CDN',
    groupName: 'CDN',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  }
];
// 数据库产品
const RDSLIST: ServiceIO[] = [
  {
    serviceName: '云数据库-SCS for Redis',
    serviceId: 'scs',
    groupType: 'database',
    groupName: '数据库',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.3 / 100,
    targetEnErrorPercent: 0.6 / 100
  },
  {
    serviceName: '云数据库-数据传输服务',
    serviceId: 'dts',
    groupType: 'database',
    groupName: '数据库',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.1 / 100,
    targetEnErrorPercent: 6 / 100
  },
  {
    serviceName: '云数据库-RDS',
    serviceId: 'rds',
    groupType: 'database',
    groupName: '数据库',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.36 / 100,
    targetEnErrorPercent: 2.57 / 100
  },
  {
    serviceName: '云原生数据库-GaiaDB',
    serviceId: 'gaiadb',
    groupType: 'database',
    groupName: '数据库',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.4 / 100,
    targetEnErrorPercent: 0 / 100
  },
  {
    serviceName: '云数据库-HBase',
    serviceId: 'hbase',
    groupType: 'database',
    groupName: '数据库',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  },
  {
    serviceName: '云数据库-DocDB for MongoDB版',
    serviceId: 'mongodb',
    groupType: 'database',
    groupName: '数据库',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  },
  {
    serviceName: '云数据库-专属集群',
    serviceId: 'ddc',
    groupType: 'database',
    groupName: '云数据库'
  }
];
// 企业服务产品

const BCDLIST: ServiceIO[] = [
  {
    serviceName: '企业服务-域名服务',
    serviceId: 'bcd',
    groupType: 'qifu',
    groupName: '企业服务'
  },
  {
    serviceName: '企业服务-商标服务',
    serviceId: 'tms',
    groupType: 'qifu',
    groupName: '企业服务'
  },
  {
    serviceName: '企业服务-工商财税',
    serviceId: 'cbs',
    groupType: 'qifu',
    groupName: '企业服务'
  },
  {
    serviceName: '管理运维-API网关',
    serviceId: 'apigw',
    groupType: 'qifu',
    groupName: '企业服务'
  },
  {
    serviceName: '管理运维-云管平台',
    serviceId: 'comp',
    groupType: 'qifu',
    groupName: '企业服务'
  }
];

// 边缘服务产品
const BECLIST: ServiceIO[] = [
  {
    serviceName: '边缘服务-边缘计算节点 BEC',
    serviceId: 'bec',
    groupType: 'edgeService',
    groupName: '边缘服务',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: 0.2 / 100,
    targetEnErrorPercent: 5 / 100
  },
  {
    serviceName: '边缘服务-边缘服务器 ECS',
    serviceId: 'ecs',
    groupType: 'edgeService',
    groupName: '边缘服务'
  },
  {
    serviceName: '智算网络算力平台 AICP',
    serviceId: 'aicp',
    groupType: 'edgeService',
    groupName: '边缘服务'
  }
];

// 大数据
const BIGDATALIST: ServiceIO[] = [
  {
    serviceName: '大数据-数据仓库 PALO',
    serviceId: 'palo',
    groupType: 'bigData',
    groupName: '大数据',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  },
  {
    serviceName: '大数据-消息服务 KAFKA',
    serviceId: 'kafka',
    groupType: 'bigData',
    groupName: '大数据',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  },
  {
    serviceName: '大数据-MAP REDUCE',
    serviceId: 'bmr',
    groupType: 'bigData',
    groupName: '大数据',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  },
  {
    serviceName: '大数据-数据湖管理与分析平台EasyDAP',
    serviceId: 'edap',
    groupType: 'bigData',
    groupName: '大数据',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  },
  {
    serviceName: '大数据-Elasticsearch',
    serviceId: 'bes',
    groupType: 'bigData',
    groupName: '大数据',
    baseTimeRange: [1727020800, 1727625599],
    targetErrorPercent: null,
    targetEnErrorPercent: null
  }
];
const QIANFAN: ServiceIO[] = [
  {
    serviceName: '千帆大模型平台-ModelBuilder',
    serviceId: 'qianfan',
    groupType: 'ModelBuilder',
    groupName: 'ModelBuilder',
    // 指定查base数据的es，因为基线时间不是六月份
    esType: CONSOLE_CLIENT,
    // 09.09 00:00 ~ 09.15 23:59
    baseTimeRange: [1725811200, 1726415999],
    targetErrorPercent: 0.15 / 100,
    targetEnErrorPercent: 4 / 100
  }
];
const BAIGE: ServiceIO[] = [
  {
    serviceName: '百舸AIHC',
    serviceId: 'aihc',
    groupType: 'baige',
    groupName: '百舸',
    // 指定查base数据的es，因为基线时间不是六月份
    esType: CONSOLE_CLIENT,
    // 08.25 00:00 ~ 09.01 23:59
    baseTimeRange: [1724601600, 1725206399],
    targetErrorPercent: 0.4 / 100,
    targetEnErrorPercent: 5 / 100
  }
];

export const UUAP_CONFIG = {
  protocol: 'https:',
  hostname: 'uuap.baidu.com',
  port: '443',
  loginMethod: '/login',
  logoutMethod: '/logout',
  sessionValidateUrl: '/session/validate',
  sTokenDecryptMethod: '/sTokenDecrypt',
  appKey: 'uuapclient-6-zgXaWmA27L0mQYmabte3',
  secretKey: 'b2f9e27bc8794dc0b947ad',
  version: 'v2',
  pTokenName: 'UUAP_P_TOKEN',
  sTokenName: 'UUAP_S_TOKEN'
};

// 第一批切换到新ES的产品List
export const newEsServiceListPhase1 = BCCLIST.concat(
  NETWORKLIST,
  STORAGELIST,
  CCELIST,
  MEDIA,
  CDN,
  RDSLIST,
  BCDLIST,
  [
    {
      serviceName: '影流平台',
      serviceId: 'yl',
      groupType: 'IAAS',
      groupName: 'IAAS'
    }
  ]
);
// 已知的接入平台的产品List，可能不全，主要是发邮件的时候用来找serviceName
export const allServiceList = BCCLIST.concat(
  NETWORKLIST,
  STORAGELIST,
  CCELIST,
  MEDIA,
  CDN,
  RDSLIST,
  BCDLIST,
  BECLIST,
  BIGDATALIST,
  QIANFAN,
  BAIGE
);
export const allServiceIdList = allServiceList.map(item => item.serviceId);

// 所有IAAS产品List，iaas邮件需要用到
export const IAASList = [
  ...BCCLIST,
  ...NETWORKLIST,
  ...STORAGELIST,
  ...CCELIST
].map(item => item.serviceId);

// 优化专项产品List
export const specialProjectServiceList = allServiceList.filter(
  (item: any) => item.baseTimeRange
);
export const specialProjectServiceIdList = specialProjectServiceList.map(
  item => item.serviceId
);

export const ticketConfig = {
  IAM_AUTH_ENDPOINT: 'http://iam.bj.bce-internal.baidu.com:80',
  IAM_AUTH_INFO: {
    name: 'console_home',
    password: 'b7vJtrtElBHGIDgH40W9cc8fholpNw0S'
  },
  TICKET_SERVICE_ENDPOINT: 'http://ticket.bj.bce-internal.baidu.com'
};

export const specialEmailReceivers = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL> ',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];
export const iaasEmailReceivers = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];
export const iaasPmEmailReceivers = [
  '<EMAIL>',
  '<EMAIL>'
];

export const testFeReceivers = [
  '<EMAIL>',
  '<EMAIL>'
];

export const mailInfoMap: any = {
  iaas: {
    subject: 'IaaS产品控制台数据周报（控制台页面API报错数据+CTS工单数据）',
    serviceList: IAASList,
    type: 'iaas',
    receivers: iaasEmailReceivers
  },
  iaaspm: {
    subject: 'IaaS产品控制台数据周报（控制台页面API报错数据+CTS工单数据）',
    serviceList: IAASList,
    type: 'iaaspm',
    receivers: iaasPmEmailReceivers
  },
  special: {
    subject:
      '控制台报错优化专项相关产品数据周报（控制台页面API报错数据+CTS工单数据）',
    serviceList: specialProjectServiceIdList,
    type: 'special',
    receivers: specialEmailReceivers
  },
  all: {
    subject: '智能云产品控制台稳定性数据',
    serviceList: allServiceIdList,
    type: 'all'
  }
};

// 报警触发间隔上限，即10分钟内超过x次异常则上报

export const service_alarm_setting: Record<string, ServiceAlarmSetting> = {
  default: {
    errorTimeGap: 10 * 60 * 1000,
    errorAlarmNumber: 3,
    toid: [10099891]
  }
};
[...BCCLIST, ...CCELIST, ...NETWORKLIST, ...STORAGELIST].forEach(item => {
  service_alarm_setting[item.serviceId] = {
    errorTimeGap: 10 * 60 * 1000,
    errorAlarmNumber: 3,
    toid: [10099891]
  };
});
