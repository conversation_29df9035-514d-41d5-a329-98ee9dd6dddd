/**
 * @file packages/aisuda-iam-proxy/src/utils/getCredentials.ts
 * <AUTHOR>
 * @description 获取证书信息的方法
 */

import {readFileSync} from 'fs';
import path from 'path';

export interface ICredentials {
  privateKey: string;
  certificate: string;
}

function getPrivateKey() {
  return readFileSync(
    path.resolve(__dirname, '../../cert/selfsigned.key'),
    'utf-8'
  );
}

function getCertificate() {
  return readFileSync(
    path.resolve(__dirname, '../../cert/selfsigned.crt'),
    'utf-8'
  );
}

export default function getCredentials(): ICredentials {
  return {
    privateKey: getPrivateKey(),
    certificate: getCertificate()
  };
}
