import {Sequelize} from 'sequelize';

const env = process.env;
const mysql_local = {
  host: env.MYSQL_HOST || '************',
  port: Number(env.MYSQL_PORT) || 8306,
  user: env.MYSQL_USER || 'root',
  password: env.MYSQL_PWD || 'monitor-sandbox',
  database: env.MYSQL_DATABASE || 'monitor-action'
};

export const db = new Sequelize(
  mysql_local.database,
  mysql_local.user,
  mysql_local.password,
  {
    host: mysql_local.host,
    port: mysql_local.port,
    dialect: 'mysql',
    timezone: '+08:00'
  }
);

/**
 * 新数据库
 */
const new_mysql_local =
  env.NODE_ENV === 'production'
    ? {
        host: '*************',
        port: 6208,
        user: 'bce_monitor_plat',
        password: 'Ju@!=,:+sp,m_',
        database: 'bce_monitor'
      }
    : {
        host: env.MY<PERSON>QL_HOST || '************',
        port: Number(env.MYSQL_PORT) || 8306,
        user: env.MYSQL_USER || 'root',
        password: env.MYSQL_PWD || 'monitor-sandbox',
        database: env.MYSQL_DATABASE || 'monitor-action'
      };

export const new_db = new Sequelize(
  new_mysql_local.database,
  new_mysql_local.user,
  new_mysql_local.password,
  {
    host: new_mysql_local.host,
    port: new_mysql_local.port,
    dialect: 'mysql',
    timezone: '+08:00'
  }
);
