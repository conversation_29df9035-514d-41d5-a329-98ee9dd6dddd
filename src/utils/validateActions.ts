import {isArray, isObject} from 'lodash';

const checkRequiredProperties = (obj: any, requiredProps: string[]) => {
  for (let i = 0; i < requiredProps.length; i++) {
    const prop = requiredProps[i];
    if (!obj.hasOwnProperty(prop)) {
      return false;
    }
  }
  return true;
};

export const validateActions = (type: string, actions: any[]) => {
  let requireProps = ['actionId'];

  if (['funnel', 'trend'].includes(type)) {
    requireProps = requireProps.concat(['color']);
  }

  if (!isArray(actions)) {
    return false;
  }

  if (actions.length > 10) {
    return false;
  }

  for (let i = 0; i < actions.length; i++) {
    const item = actions[i];
    if (!isObject(item)) {
      return false;
    }
    if (!checkRequiredProperties(item, requireProps)) {
      return false;
    }
  }
  return true;
};
