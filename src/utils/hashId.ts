/**
 * @file hashId.ts
 * @desc 将AutoIncrement的ID转换为HashID，避免暴露DB中的ID
 */

import Hashids from 'hashids';

class HashId {
  worker: Hashids;

  constructor() {
    this.worker = new Hashids('bce-monitor.acg-iaas-fe', 10);
  }

  encode(id: number) {
    return this.worker.encode(id);
  }

  decode(value: string) {
    try {
      const ids = this.worker.decode(value);

      return (ids && ids[0] != null ? ids[0] : 0) as number;
    } catch (e) {
      return 0;
    }
  }
}

export const hashId = new HashId();
