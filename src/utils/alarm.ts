import axios from 'axios';
import moment from 'moment';
import Container from 'typedi';
import {redisClient} from '../client';
import {service_alarm_setting, allServiceList} from './const';
const getAlarmContent = (serviceErrorInfo: ServiceAlarmInstance) => {
  const serviceName =
    allServiceList.find(item => item.serviceId === serviceErrorInfo.serviceId)
      ?.serviceName || serviceErrorInfo.serviceId;
  return `<font color="red">${serviceName}</font> 在<font color="red">${
    serviceErrorInfo?.timeRange
  }</font>内出现了<font color="red">${
    serviceErrorInfo?.log?.length
  }</font>次页面白屏，访问记录如下：
  ${JSON.stringify(serviceErrorInfo.log[0])}
  ${JSON.stringify(serviceErrorInfo.log[1])}
  ${JSON.stringify(serviceErrorInfo.log[2])}
  `;
};

type ErrorType = 'page' | 'api';
interface ServiceErrorInstance {
  serviceId: string;
  errorType: ErrorType;
  startErrorTime: number;
  latestErrorTime: number;
  number: number;
  errorLogList: any[];
}

interface ServiceAlarmInstance {
  serviceId: string;
  timeRange: string;
  log: any[];
  toid: number[];
}

const serviceErrorLogAppend = (
  errorInstance: ServiceErrorInstance,
  errorInfo: any
) => {
  // 报警后会重置数据，但是不会删除实例，所以需要报警后的第一次异常时重新设置开始隐藏时间
  if (errorInstance.number === 0) {
    errorInstance.startErrorTime = new Date().getTime();
  }
  errorInstance.latestErrorTime = new Date().getTime();
  errorInstance.number++;
  errorInstance.errorLogList.push(errorInfo);
};
const checkAlarmStatus = (
  errorInstance: ServiceErrorInstance
): ServiceAlarmInstance | false => {
  const gap = errorInstance.latestErrorTime - errorInstance.startErrorTime;
  const setting =
    service_alarm_setting[errorInstance.serviceId] ||
    service_alarm_setting.default;
  // 如果间隔时间超出阈值,则把最新一条的报错作为起点
  if (gap > setting.errorTimeGap) {
    errorInstance.number = 1;
    errorInstance.startErrorTime = errorInstance.latestErrorTime;
    errorInstance.errorLogList = [errorInstance.errorLogList.pop()];
    return false;
  }

  if (
    errorInstance.number >= setting.errorAlarmNumber &&
    gap <= setting.errorTimeGap
  ) {
    return {
      serviceId: errorInstance.serviceId,
      timeRange: `${moment(errorInstance.startErrorTime).format(
        'YYYY-MM-DD HH:mm:ss'
      )} - ${moment(errorInstance.latestErrorTime).format(
        'YYYY-MM-DD HH:mm:ss'
      )}`,
      log: errorInstance.errorLogList,
      toid: setting.toid
    };
  }
  return false;
};

export const PageErrorHandle = async (baseInfo: BaseInfo) => {
  if (baseInfo.env === 'consoleOffline') {
    return;
  }
  const redisInstance = Container.get(redisClient);
  // const baseInfo = {
  //   signature: '8f4d14cd-bbc9-4000-a5d9-e4b4a1f2c000',
  //   browserWidth: 2133,
  //   _href: 'https://console.bce.baidu.com/bcc/#/bcc/instance/list',
  //   href: 'https://console.bce.baidu.com/bcc/#/bcc/instance/list',
  //   serviceId: 'bcc',
  //   platform: 'Win32',
  //   browserHeight: 1032,
  //   userId: '778f8cb05afa467ba77eb53aeb08978d',
  //   browserVersion: '86',
  //   browser: 'Chrome',
  //   locale: 'zh-CN',
  //   env: 'consoleOnline',
  //   urlName: 'BCC实例列表',
  //   tags: ''
  // };
  const serviceType = baseInfo.serviceId;
  const errorInfo = {
    href: baseInfo.href,
    platform: baseInfo.platform,
    serviceId: baseInfo.serviceId,
    userId: baseInfo.userId,
    browser: baseInfo.browser + '/' + baseInfo.browserVersion,
    timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
  };
  const redisKey = `${serviceType}PageErrorInfo`;
  try {
    const servicePageErrorString = await redisInstance?.get(redisKey);
    let servicePageErrorInstance: ServiceErrorInstance;
    if (!servicePageErrorString) {
      servicePageErrorInstance = {
        errorType: 'page',
        serviceId: serviceType,
        startErrorTime: new Date().getTime(),
        latestErrorTime: new Date().getTime(),
        number: 0,
        errorLogList: []
      };
    } else {
      servicePageErrorInstance = JSON.parse(servicePageErrorString);
    }
    serviceErrorLogAppend(servicePageErrorInstance, errorInfo);

    const checkResult = checkAlarmStatus(servicePageErrorInstance);

    if (checkResult) {
      alarmAction(checkResult);
      redisInstance?.delete(redisKey);
    } else {
      redisInstance?.set(redisKey, JSON.stringify(servicePageErrorInstance));
    }
    console.log(
      '-----servicePageErrorInstance-----:',
      servicePageErrorInstance
    );
    console.log('-----checkResult-----:', checkResult);
  } catch (error) {
    console.log('报警模块处理异常数据失败：', error);
  }
};
const alarmAction = async (serviceErrorInfo: ServiceAlarmInstance) => {
  axios.post(
    'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=de455bed87ed90a86ea53bb7d1bed1588',
    {
      message: {
        header: {
          toid: serviceErrorInfo.toid
        },
        body: [
          {
            content: getAlarmContent(serviceErrorInfo),
            type: 'MD'
          }
        ]
      }
    }
  );
};
