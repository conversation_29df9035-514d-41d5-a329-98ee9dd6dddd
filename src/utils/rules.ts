import type {Rules} from 'async-validator';
import crypto from 'crypto';
import Container from 'typedi';

import {redisClient} from '../client';

export const loginRules: Rules = {
  title: {
    type: 'string',
    required: true
  },
  score: {
    type: 'number',
    min: 1,
    max: 5,
    required: true
  },
  reason: {
    type: 'string'
  },
  resourceId: {
    type: 'string'
  },
  reasonTags: [
    {
      type: 'array'
    },
    {
      validator: (rule: any, value: any, callback: any, source: any) => {
        if (source.score <= 3 && (!value || !value.length)) {
          callback('reasonTags is required');
        }
        callback();
      }
    }
  ]
};

// 遍历下当前的mapping,格式化一下传过来的数据, 过滤掉不需要的字段。
export const filterMappingData = (mapping: any, data: any) => {
  let result: any = {};
  if (mapping.properties) {
    const properties = mapping.properties || {};
    Object.keys(properties).forEach(key => {
      const prop = properties[key];

      if (data[key] !== undefined) {
        result[key] = filterMappingData(prop, data[key]);
      }
    });
  } else {
    const type = mapping.type;
    if (['text', 'keyword'].includes(type)) {
      result = typeof data !== 'string' ? JSON.stringify(data) : data;
    } else if ('date' === type) {
      result =
        typeof data === 'number' || typeof data === 'string'
          ? data
          : JSON.stringify(data);
    } else if (['long', 'float', 'double', 'int'].includes(type)) {
      result = parseFloat(data);
    } else if ('boolean' === type) {
      result = !!data;
    }
  }
  return result;
};

// 辅助函数，用于生成MD5哈希值
function generateMD5Hash(string: string) {
  return crypto.createHash('md5').update(string).digest('hex');
}
const TIME_WINDOW = 60 * 1000; // 60秒

export const validateAuthToken = async (
  id: string,
  serviceId: string,
  signature: string,
  authToken: string
): Promise<{valid: boolean; error?: string}> => {
  try {
    // 检查请求是否有效
    if (!id || !signature || !authToken) {
      throw new Error('无效的参数');
    }

    // 在服务器端重新计算authToken
    const serverAuthToken = generateMD5Hash(
      `token=id${id}&serviceId${serviceId}&signature${signature}`
    );

    // 将接收到的authToken与服务器生成的authToken进行比较
    if (authToken !== serverAuthToken) {
      throw new Error('无效的authToken');
    }

    const redisInstance = Container.get(redisClient);

    // 检查authToken是否已存在于Redis中
    const existingToken = await redisInstance.exist(authToken);
    if (existingToken) {
      throw new Error('authToken已存在');
    }
    // 将authToken存储到Redis并设置过期时间为10分钟
    await redisInstance.set(authToken, '1', 600);

    // 如果所有检查都通过，则返回验证通过
    return {valid: true};
  } catch (error) {
    return {valid: false, error: (error as Error).message};
  }
};
