/**
 * @file controller/test-case.ts
 * @desc 测试用例表相关接口
 */

import {type Context, type Next} from 'koa';
import omit from 'lodash/omit';
import {Controller, Post} from '../../decorators';
import {TestCase} from '../../model';
import {ErrorMsg} from '../../utils/error';
import {hashId} from '../../utils/hashId';

interface BaseTestCaseReq {
  id: string;
}

interface GetTestCaseListReq {
  testId: string;
  page?: number;
  perPage?: number;
}

@Controller('/testcase/v1')
export class TestCaseController {
  /** TestCase转化为前端使用的数据 */
  static normalizeTestCase2FE(
    testCase: TestCase,
    options?: {withFK: boolean; withErrorTrace?: boolean}
  ) {
    const withFK = options?.withFK ?? false;
    const withErrorTrace = options?.withErrorTrace ?? false;

    return {
      ...omit(testCase.get(), [
        'testIdid',
        'filePath',
        'testId',
        'createdAt',
        'updatedAt',
        'steps',
        '_testSteps',
        'TestErrorTrace'
      ]),
      id: hashId.encode(testCase.id),
      ...(withFK ? {testId: hashId.encode(testCase.testId)} : {}),
      steps: testCase.get('_testSteps'),
      ...(withErrorTrace
        ? {
            errorTrace: testCase.TestErrorTrace
              ? {
                  ...omit(testCase.TestErrorTrace.get(), ['id']),
                  ...(testCase.TestErrorTrace.id
                    ? {id: hashId.encode(testCase.TestErrorTrace.id)}
                    : {})
                }
              : null
          }
        : {})
    };
  }

  @Post('/get')
  static async getTestCase(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseTestCaseReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const testCase = await TestCase.findByPk(id);

    if (!testCase) {
      ctx.throw(404, `获取失败：${ErrorMsg.NoSuchTestCase}`);
    }

    ctx.status = 200;
    ctx.body = {
      msg: '获取成功',
      success: true,
      data: TestCaseController.normalizeTestCase2FE(testCase, {withFK: true})
    };

    next();
  }

  @Post('/list')
  static async listTestCase(ctx: Context, next: Next) {
    const payload = ctx.request.body as GetTestCaseListReq;
    const testId = hashId.decode(payload.testId);

    if (testId == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('testId'));
    }

    const page = payload.page ?? 1;
    const perPage = payload.perPage ?? 10;

    try {
      const {rows, count} = await TestCase.findAndCountAll({
        where: {
          testId: testId
        },
        order: [['created_at', 'desc']],
        offset: (page - 1) * perPage,
        limit: perPage
      });

      ctx.status = 200;
      ctx.body = {
        msg: '列表获取成功',
        success: true,
        data: {
          items: rows.map(testCase =>
            TestCaseController.normalizeTestCase2FE(testCase, {withFK: true})
          ),
          total: count
        }
      };
    } catch (error) {
      ctx.throw(400, `列表获取失败：${(error as Error).message}`);
    }

    next();
  }
}
