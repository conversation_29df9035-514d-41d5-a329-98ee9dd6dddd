/**
 * @file controller/testFuncov.ts
 * @desc 自动化测试-功能覆盖率相关接口
 */

import {type Context, type Next} from 'koa';
import {
  new_db as db,
  TreeUtil,
  genAutoTestFuncovStatisic,
  genTestCaseId,
  genTestGroupId,
  TestNodeType,
  TestCaseStatus,
  xssFilter
} from '../../utils';
import {Controller, Post, Put} from '../../decorators';
import {
  AppManage,
  TestFunctionCoverage,
  TestFunctionStatistic
} from '../../model';
import {ErrorMsg} from '../../utils/error';
import {Op} from 'sequelize';
import moment, {Moment} from 'moment';
import isEmpty from 'lodash/isEmpty';
import pick from 'lodash/pick';

const trees = new TreeUtil({
  id: 'id',
  parentId: 'groupId',
  children: 'features'
});

interface GetTestFuncovListReq {
  appId: string;
}

interface TestFuncovGroupReq {
  appId: string;
  id: string;
  groupId: string;
  name: string;
  type: TestNodeType.GROUP;
  path: string;
  description: string;
  doc: string;
  isRootGroup: boolean;
  features: Array<TestFuncovGroupReq | TestFuncovCaseReq>;
}

interface TestFuncovCaseReq {
  appId: string;
  id: string;
  groupId: string;
  caseId: string;
  name: string;
  type: TestNodeType.CASE;
  level: number;
  status: TestCaseStatus;
  description: string;
  doc: string;
}

interface TestFuncovImportReq {
  appId: string;
  datasource: [];
}

interface TestFuncovTrendReq {
  appId: string;
  dateRange: [];
}

@Controller('/funcov/v1')
export class TestFuncCovController {
  /** 功能覆盖率树列表 */
  @Post('/list')
  static async listFuncov(ctx: Context, next: Next) {
    const payload = ctx.request.body as GetTestFuncovListReq;
    const appId = payload.appId;
    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    try {
      const cov = await TestFunctionCoverage.findOne({
        where: {
          appId
        }
      });

      ctx.status = 200;
      ctx.body = {
        msg: '请求成功',
        success: true,
        data: {
          source: cov ? cov : {}
        }
      };
    } catch (error) {
      ctx.throw(400, `功能覆盖率列表获取失败：${(error as Error).message}`);
    }

    next();
  }

  /** 创建用例组 */
  @Post('/group/create')
  static async createFuncovGroup(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovGroupReq;
    const appId = payload.appId;
    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    const testGroup = Object.assign(
      {},
      {
        id: genTestGroupId(appId),
        name: xssFilter(payload.name),
        description: payload.description ? xssFilter(payload.description) : '',
        doc: payload.doc ? xssFilter(payload.doc) : '',
        path: payload.path ? xssFilter(payload.path) : '',
        type: payload.type,
        isRootGroup: payload.isRootGroup,
        features: []
      }
    );

    // 校验参数合法性
    for (const key in testGroup) {
      if (
        Object.prototype.hasOwnProperty.call(
          TestFuncCovController.testOptionsValidators,
          key
        )
      ) {
        const val = testGroup[key as keyof typeof testGroup];
        const errorString =
          TestFuncCovController.testOptionsValidators[key](val);
        if (errorString) {
          ctx.throw(400, errorString);
        }
      }
    }

    try {
      const result = await db.transaction(async t => {
        const cov = await TestFunctionCoverage.findOne({
          where: {
            appId: appId
          }
        });

        if (!isEmpty(cov)) {
          const funcov = cov?.functionCoverage;
          if (payload?.isRootGroup) {
            // 新建根用例组
            cov.functionCoverage = [...funcov, testGroup];
            await cov?.update(cov);
            await cov?.save();
            const covStatisticData = genAutoTestFuncovStatisic(
              cov?.functionCoverage
            );
            await TestFuncCovController.updateFuncovStatisticDS(
              covStatisticData,
              appId
            );
          } else {
            // 新建子用例组
            trees.insertNode(funcov, testGroup, payload.groupId);
            cov.functionCoverage = funcov;
            await cov?.update(cov);
            await cov?.save();
            const covStatisticData = genAutoTestFuncovStatisic(
              cov?.functionCoverage
            );
            await TestFuncCovController.updateFuncovStatisticDS(
              covStatisticData,
              appId
            );
          }
        } else {
          // 初次创建根用例组
          const datasource = [testGroup];
          const funcovData = genAutoTestFuncovStatisic(datasource);
          await TestFunctionCoverage.create({
            appId,
            functionCoverage: datasource,
            testEnv: 'sandbox',
            ...funcovData
          });
          await TestFuncCovController.updateFuncovStatisticDS(
            funcovData,
            appId
          );
        }
        return cov;
      });

      const res = {
        msg: '创建用例组成功',
        success: true
      };

      ctx.status = 200;
      ctx.body = {
        data: res
      };
    } catch (error) {
      ctx.throw(400, `创建用例组失败：${(error as Error).message}`);
    }

    next();
  }

  /** 编辑用例组 */
  @Put('/group/edit')
  static async editFuncovGroup(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovGroupReq;
    const appId = payload.appId;
    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    const editedGroup = Object.assign(
      {},
      {
        id: payload.id,
        name: xssFilter(payload.name),
        description: payload.description ? xssFilter(payload.description) : '',
        doc: payload.doc ? xssFilter(payload.doc) : '',
        path: payload.path ? xssFilter(payload.path) : '',
        type: payload.type,
        isRootGroup: payload.isRootGroup
      }
    );

    // 校验参数合法性
    for (const key in editedGroup) {
      if (
        Object.prototype.hasOwnProperty.call(
          TestFuncCovController.testOptionsValidators,
          key
        )
      ) {
        const val = editedGroup[key as keyof typeof editedGroup];
        const errorString =
          TestFuncCovController.testOptionsValidators[key](val);
        if (errorString) {
          ctx.throw(400, errorString);
        }
      }
    }

    try {
      const result = await db.transaction(async t => {
        const cov = await TestFunctionCoverage.findOne({
          where: {
            appId: appId
          }
        });

        if (!isEmpty(cov)) {
          const funcov = cov?.functionCoverage;
          trees.updateNode(funcov, editedGroup);
          cov.functionCoverage = funcov;
          // 更新覆盖率列表数据
          await cov?.update(cov);
          await cov?.save();
          // 更新覆盖率统计数据
          const covStatisticData = genAutoTestFuncovStatisic(funcov);
          await TestFuncCovController.updateFuncovStatisticDS(
            covStatisticData,
            appId
          );
        }
        return cov;
      });

      const res = {
        msg: '更新成功',
        success: true
      };
      ctx.status = 200;
      ctx.body = res;
    } catch (error) {
      ctx.throw(400, `更新用例组失败：${(error as Error).message}`);
    }

    next();
  }

  /** 删除用例组 */
  @Post('/group/delete')
  static async delFuncovGroup(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovGroupReq;
    const appId = payload.appId;
    const groupId = payload.groupId;

    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    // 校验groupId
    if (!groupId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('groupId'));
    }

    try {
      const result = await db.transaction(async t => {
        const cov = await TestFunctionCoverage.findOne({
          where: {
            appId: appId
          }
        });

        if (!isEmpty(cov)) {
          const funcov = cov?.functionCoverage;
          trees.removeNode(funcov, groupId);
          cov.functionCoverage = funcov;
          await cov?.update(cov);
          await cov?.save();
          // 更新统计数据
          const covStatisticData = genAutoTestFuncovStatisic(funcov);
          await TestFuncCovController.updateFuncovStatisticDS(
            covStatisticData,
            appId
          );
        }

        return cov;
      });

      const res = {
        msg: '删除成功',
        success: true
      };
      ctx.status = 200;
      ctx.body = res;
    } catch (error) {
      ctx.throw(400, `删除用例组失败：${(error as Error).message}`);
    }

    next();
  }

  /** 创建用例 */
  @Post('/case/create')
  static async createFuncovCase(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovCaseReq;
    const appId = payload.appId;

    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    const testCase = {
      id: genTestCaseId(appId),
      status: TestCaseStatus.UNCOVERED,
      name: payload.name ? xssFilter(payload.name) : '',
      level: payload.level ?? 0,
      type: payload.type,
      description: payload.description ? xssFilter(payload.description) : '',
      doc: payload.doc ? xssFilter(payload.doc) : ''
    };

    // 校验参数合法性
    for (const key in testCase) {
      if (
        Object.prototype.hasOwnProperty.call(
          TestFuncCovController.testOptionsValidators,
          key
        )
      ) {
        const val = testCase[key as keyof typeof testCase];
        const errorString =
          TestFuncCovController.testOptionsValidators[key](val);
        if (errorString) {
          ctx.throw(400, errorString);
        }
      }
    }

    try {
      const result = await db.transaction(async t => {
        const cov = await TestFunctionCoverage.findOne({
          where: {
            appId: appId
          }
        });

        const funcov = cov?.functionCoverage;

        if (!isEmpty(cov)) {
          // 新建子用例组
          trees.insertNode(funcov, testCase, payload.groupId);
          cov.functionCoverage = funcov;
          await cov?.update(cov);
          await cov?.save();
          // 更新统计数据
          const covStatisticData = genAutoTestFuncovStatisic(
            cov?.functionCoverage
          );
          await TestFuncCovController.updateFuncovStatisticDS(
            covStatisticData,
            appId
          );
        }

        return cov;
      });

      const res = {
        msg: '创建用例成功',
        success: true
      };
      ctx.status = 200;
      ctx.body = res;
    } catch (error) {
      ctx.throw(400, `创建用例失败：${(error as Error).message}`);
    }

    next();
  }

  /** 编辑用例 */
  @Put('/case/edit')
  static async editFuncovCase(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovCaseReq;
    const appId = payload.appId;

    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    const editedCase = Object.assign(
      {},
      {
        id: payload.id,
        status: payload.status,
        name: payload.name ? xssFilter(payload.name) : '',
        level: payload.level ?? 0,
        type: payload.type,
        description: payload.description ? xssFilter(payload.description) : '',
        doc: payload.doc ? xssFilter(payload.doc) : ''
      }
    );

    // 校验参数合法性
    for (const key in editedCase) {
      if (
        Object.prototype.hasOwnProperty.call(
          TestFuncCovController.testOptionsValidators,
          key
        )
      ) {
        const val = editedCase[key as keyof typeof editedCase];
        const errorString =
          TestFuncCovController.testOptionsValidators[key](val);
        if (errorString) {
          ctx.throw(400, errorString);
        }
      }
    }

    try {
      const result = await db.transaction(async t => {
        const cov = await TestFunctionCoverage.findOne({
          where: {
            appId: appId
          }
        });

        if (!isEmpty(cov)) {
          const funcov = cov?.functionCoverage;
          trees.updateNode(funcov, editedCase);
          cov.functionCoverage = funcov;
          await cov?.update(cov);
          await cov?.save();
          // 更新统计数据
          const covStatisticData = genAutoTestFuncovStatisic(
            cov?.functionCoverage
          );
          await TestFuncCovController.updateFuncovStatisticDS(
            covStatisticData,
            appId
          );
        }

        return cov;
      });

      const res = {
        msg: '更新成功',
        success: true
      };
      ctx.status = 200;
      ctx.body = res;
    } catch (error) {
      ctx.throw(400, `更新用例失败：${(error as Error).message}`);
    }

    next();
  }

  /** 删除用例 */
  @Post('/case/delete')
  static async delFuncovCase(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovCaseReq;
    const appId = payload.appId;
    const caseId = payload.caseId;

    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    // 校验caseId
    if (!caseId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('caseId'));
    }

    try {
      const result = await db.transaction(async t => {
        const cov = await TestFunctionCoverage.findOne({
          where: {
            appId: appId
          }
        });

        if (!isEmpty(cov)) {
          const funcov = cov?.functionCoverage;
          trees.removeNode(funcov, caseId);
          cov.functionCoverage = funcov;
          await cov?.update(cov);
          await cov?.save();
          // 更新统计数据
          const covStatisticData = genAutoTestFuncovStatisic(
            cov?.functionCoverage
          );
          await TestFuncCovController.updateFuncovStatisticDS(
            covStatisticData,
            appId
          );
        }

        return cov;
      });

      const res = {
        msg: '删除用例成功',
        success: true
      };
      ctx.status = 200;
      ctx.body = res;
    } catch (error) {
      ctx.throw(400, `删除用例失败：${(error as Error).message}`);
    }

    next();
  }

  /** 导入用例列表 */
  @Post('/import')
  static async importFuncovList(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovImportReq;
    const appId = payload.appId;
    const datasource = payload.datasource;

    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    try {
      const result = await db.transaction(async t => {
        const cov = await TestFunctionCoverage.findOne({
          where: {
            appId: appId
          }
        });

        if (!isEmpty(cov)) {
          // 已导入的覆盖旧数据
          cov.functionCoverage = datasource;
          await cov?.update(cov);
          await cov?.save();
          // 更新统计数据
          const covStatisticData = genAutoTestFuncovStatisic(
            cov?.functionCoverage
          );
          await TestFuncCovController.updateFuncovStatisticDS(
            covStatisticData,
            appId
          );
        } else {
          // 未导入的创建新数据
          const funcovData = genAutoTestFuncovStatisic(datasource);
          await TestFunctionCoverage.create({
            appId,
            functionCoverage: datasource,
            testEnv: 'sandbox',
            ...funcovData
          });
          await TestFuncCovController.updateFuncovStatisticDS(
            funcovData,
            appId
          );
        }

        return cov;
      });

      const res = {
        msg: '导入成功',
        success: true
      };
      ctx.status = 200;
      ctx.body = res;
    } catch (error) {
      ctx.throw(400, `导入覆盖率列表失败：${(error as Error).message}`);
    }

    next();
  }

  /** 覆盖率统计历史趋势数据 */
  @Post('/stat/trend')
  static async getFuncovStatHistory(ctx: Context, next: Next) {
    const payload = ctx.request.body as TestFuncovTrendReq;
    const appId = payload.appId;
    let dateRange: any = payload.dateRange
      ? payload.dateRange.map(time => moment(time).toDate())
      : [moment().subtract(7, 'days').startOf('day'), moment()];

    // 校验appId
    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }
    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });
    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    const historyData = await TestFunctionStatistic.findAll({
      attributes: [
        'id',
        ['test_env', 'testEnv'],
        ['total_count', 'totalCount'],
        ['root_group_count', 'rootGroupCount'],
        ['covered_count', 'coveredCount'],
        ['uncovered_count', 'uncoveredCount'],
        ['app_id', 'appId'],
        ['created_at', 'createdAt']
      ],
      where: {
        appId,
        createdAt: {
          [Op.between]: dateRange
        }
      },
      order: [['createdAt', 'ASC']]
    });

    const res = {
      msg: '请求成功',
      data: {
        source: historyData
      },
      success: true
    };

    try {
      ctx.status = 200;
      ctx.body = res;
    } catch (error) {
      ctx.throw(400, `覆盖率趋势数据查询失败：${(error as Error).message}`);
    }

    next();
  }

  /** 更新功能覆盖率统计表 */
  static async updateFuncovStatisticDS(datasource: any, appId: string) {
    await TestFunctionStatistic.create({
      appId,
      ...datasource
    });
  }

  /** 参数校验参数 */
  static testOptionsValidators: Record<string, any> = {
    name: (val: string) => {
      if (!val) {
        return ErrorMsg.InvalidEmptyParameterWithName('名称name');
      }
      if (val && typeof val !== 'string') {
        return ErrorMsg.InvalidParameterMustBeString('名称name');
      }
      if (val.length > 255) {
        return ErrorMsg.InvalidParameterMaxLength('名称name', 255);
      }
    },
    doc: (val: string) => {
      if (val && typeof val !== 'string') {
        return ErrorMsg.InvalidParameterMustBeString('文档链接doc');
      }
      if (val && val.length > 1024) {
        return ErrorMsg.InvalidParameterMaxLength('文档链接doc', 1024);
      }
    },
    description: (val: string) => {
      if (val && typeof val !== 'string') {
        return ErrorMsg.InvalidParameterMustBeString('描述description');
      }
      if (val && val.length > 1024) {
        return ErrorMsg.InvalidParameterMaxLength('描述description', 1024);
      }
    },
    type: (val: string) => {
      if (!val) {
        return ErrorMsg.InvalidEmptyParameterWithName('类型type');
      }
      if (val && typeof val !== 'string') {
        return ErrorMsg.InvalidParameterMustBeString('类型type');
      }
      if (
        val &&
        ![TestNodeType.CASE, TestNodeType.GROUP].includes(val as TestNodeType)
      ) {
        return ErrorMsg.InvalidParameterNotSupport('类型type');
      }
    }
  };
}
