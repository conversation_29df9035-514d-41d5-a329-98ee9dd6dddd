/**
 * @file controller/test.ts
 * @desc 测试活动表相关接口
 */

import fs from 'node:fs';
import {Sequelize, Op, literal, fn, col, type InferAttributes} from 'sequelize';
import {type Context, type Next, type Request} from 'koa';
import moment from 'moment';
import zip from 'lodash/zip';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import omit from 'lodash/omit';
import uniq from 'lodash/uniq';
import {new_db as db} from '../../utils';
import {Controller, Post, PostUpload} from '../../decorators';
import {upload} from '../../decorators/file-upload';
import {AppManage, Testing, TestCase, TestErrorTrace} from '../../model';
import {ErrorMsg} from '../../utils/error';
import {
  convertTimestampToDate,
  omitNil,
  calculatePercentage,
  calculatePercentageChange,
  getJSONFromText
} from '../../utils/helper';
import {hashId} from '../../utils/hashId';
import {TestCaseController} from './test-case';
import TokenGenerator from '../../utils/tokenGen';

import type {
  BrowserType,
  CTRFTool,
  CTRFEnvironment,
  CTRFSummary,
  CoverageReport,
  IPipeContext,
  CTRFReport,
  CTRFTest
} from '@baidu/cbt-report';

interface ImportTestingReq {
  /** 应用ID */
  appId: string;
}

/** 新增测试任务请求体 */
interface InsertTestingReq {
  appId: string;
  platform: CTRFEnvironment['osPlatform'];
  arch: CTRFEnvironment['osArch'];
  runtimeEnv: CTRFEnvironment['runtimeEnv'];
  testEnv: CTRFEnvironment['testEnv'];
  branch: CTRFEnvironment['branch'];
  status: CTRFSummary['status'];
  startAt: CTRFSummary['start'];
  endAt: CTRFSummary['stop'];
  suitesCount: CTRFSummary['suites'];
  totalCount: CTRFSummary['tests'];
  passedCount: CTRFSummary['passed'];
  failedCount: CTRFSummary['failed'];
  skippedCount: CTRFSummary['skipped'];
  pendingCount: CTRFSummary['pending'];
  otherCount: CTRFSummary['other'];
  /** FIXME: 这里为了省事，放宽类型限制 */
  browsers?: string[];
  toolName?: CTRFTool['name'];
  toolVersion?: CTRFTool['version'];
  coverage?: CoverageReport;
  pipeline?: IPipeContext;
  tests?: CTRFTest[];
}

interface BaseTestingReq {
  id: string;
}

interface UpdateTestingReq extends Omit<InsertTestingReq, 'appId' | 'tests'> {
  id: string;
  falsePositiveCount?: number;
}

interface GetTestingListReq {
  appId: number;
  page?: number;
  perPage?: number;
  filters?: {
    startAt?: [string, string];
    branch?: string;
    status?: CTRFSummary['status'][];
    runtimeEnv?: CTRFEnvironment['runtimeEnv'][];
  };
}

interface QueryTestingChartReq {
  appId: string;
  dimension: 'count-date' | 'case-count-stack-sequence';
  dateRange?: [string, string];
}

interface QueryTestingStatisticsReq {
  appId: string;
  dimension: 'count' | 'avg-duration' | 'passed-ratio' | 'avg-case-count';
  dateRange?: [string, string];
}

type StatsCycle = 'DoD' | 'WoW' | 'MoM' | 'MYoY';

interface QueryTestingOverviewDSReq {
  period: [string, string];
  cycle: StatsCycle;
}

interface QueryTestingOverviewDataReq {
  period: [string, string];
  cycle: StatsCycle;
  dimension: 'group-list';
}

@Controller('/testing/v1')
export class TestingController {
  static normalizeCTRFReport(json: CTRFReport) {
    const result = {
      toolName: get(json, 'results.tool.name'),
      toolVersion: get(json, 'results.tool.version'),
      platform: get(json, 'results.environment.osPlatform', 'linux'),
      arch: get(json, 'results.environment.osArch', 'x64'),
      runtimeEnv: get(json, 'results.environment.runtimeEnv', 'local'),
      testEnv: get(json, 'results.environment.testEnv', 'sandbox'),
      /** 优先从iPipe环境变量中获取，更准确 */
      branch:
        get(json, 'results.iPipe.compileBranch') ??
        get(json, 'results.environment.branch', 'master'),
      status: get(json, 'results.summary.status', 'initial'),
      startAt: convertTimestampToDate(get(json, 'results.summary.start')),
      endAt: convertTimestampToDate(get(json, 'results.summary.stop')),
      suitesCount: get(json, 'results.summary.suites', 0),
      totalCount: get(json, 'results.summary.tests', 0),
      passedCount: get(json, 'results.summary.passed', 0),
      failedCount: get(json, 'results.summary.failed', 0),
      skippedCount: get(json, 'results.summary.skipped', 0),
      pendingCount: get(json, 'results.summary.pending', 0),
      otherCount: get(json, 'results.summary.other', 0),
      browsers: get(json, 'results.summary.browsers', [])
        .filter((i: any) => i != null && i !== 'undefined')
        .join(','),
      coverage: get(json, 'results.coverage'),
      pipeline: get(json, 'results.iPipe')
    };

    return result;
  }

  static normalizeTestCaseReport(json: CTRFTest) {
    const result = {
      ctrfId: json.id,
      name: json.name,
      browser: (json?.browser?.name ?? 'chromium') as BrowserType,
      browserVersion: json?.browser?.version,
      status: json.status ?? 'failed',
      startAt: convertTimestampToDate(json.start ?? 0),
      endAt: convertTimestampToDate(json.stop ?? 0),
      duration: json.duration ?? 0,
      retries: json.retries ?? 0,
      flaky: json.flaky ?? false,
      tags: json.tags?.join(','),
      suite: json.suite,
      message: json.message,
      trace: json.trace,
      filePath: json.filePath,
      screenshot: json.screenshot,
      steps: json.steps
    };

    return result;
  }

  static cleanupTempFile(filePath: string) {
    if (!filePath) {
      return;
    }

    if (!fs.existsSync(filePath)) {
      return;
    }

    try {
      fs.unlinkSync(filePath);
    } catch (error) {}
  }

  /**
   * 上报测试活动-解析JSON文件报告
   */
  @PostUpload('/import', upload.single('file'))
  static async importTest(ctx: Context, next: Next) {
    const payload = ctx.request.body as ImportTestingReq;
    const appId = payload.appId;
    const file = ctx.request.file;

    if (!appId) {
      TestingController.cleanupTempFile(file?.path);
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    if (!file) {
      ctx.throw(400, ErrorMsg.NoTestingReport);
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      TestingController.cleanupTempFile(file?.path);
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    // 中间件已经验证了OAuth Token的合法性，这里主要是验证Token是否属于当前应用
    const authHeader = ctx.headers['authorization']!;
    const extractedAppId = TokenGenerator.extractPrefix(
      authHeader?.replace(/^Bearer /, '')
    );

    if (extractedAppId !== app.getDataValue('service_id')) {
      TestingController.cleanupTempFile(file?.path);
      ctx.throw(404, ErrorMsg.OAuthTokenNotMatchedError);
    }

    let jsonData: CTRFReport;

    try {
      const fileContent = fs.readFileSync(file.path, 'utf8');
      jsonData = JSON.parse(fileContent);
    } catch (error) {
      TestingController.cleanupTempFile(file?.path);
      ctx.throw(500, `测试报告读取失败：${(error as Error).message}`);
    }

    try {
      const result = await db.transaction(async t => {
        const testingData = TestingController.normalizeCTRFReport(jsonData);
        const test = await Testing.create(
          {...testingData, appId: payload.appId},
          {transaction: t}
        );

        const testCasesData = get(jsonData, 'results.tests', []).map(
          TestingController.normalizeTestCaseReport
        );
        const testCases = await TestCase.bulkCreate(testCasesData, {
          transaction: t
        });
        // 绑定关系
        await test.addTestCases(testCases, {transaction: t});

        return test;
      });

      ctx.status = 200;
      ctx.body = {
        msg: '测试活动创建成功',
        success: true,
        data: {
          id: hashId.encode(result.id)
        }
      };
    } catch (error) {
      TestingController.cleanupTempFile(file?.path);
      ctx.throw(400, `创建失败：${(error as Error).message}`);
    }

    await next();
  }

  static requiredCreationParams: (keyof InsertTestingReq)[] = [
    'appId',
    'platform',
    'arch',
    'runtimeEnv',
    'testEnv',
    'status',
    'startAt',
    'endAt',
    'suitesCount',
    'totalCount',
    'passedCount',
    'failedCount',
    'skippedCount',
    'pendingCount',
    'otherCount',
    'browsers'
  ];

  /**
   * 新增测试活动
   */
  @Post('/insert')
  static async insertTest(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as InsertTestingReq;

    // 必填参数验证
    for (const key of TestingController.requiredCreationParams) {
      if (payload[key] == null) {
        ctx.throw(400, ErrorMsg.InvalidParameter(key));
      }
    }

    try {
      const result = await db.transaction(async t => {
        const pipelineContext = payload?.pipeline;
        const test = await Testing.create(
          {
            appId: payload.appId,
            toolName: payload.toolName,
            toolVersion: payload.toolVersion,
            platform: payload.platform ?? 'linux',
            arch: payload.arch ?? 'x64',
            runtimeEnv: payload.runtimeEnv,
            testEnv: payload.testEnv,
            branch:
              pipelineContext?.compileBranch ?? payload.branch ?? 'master',
            status: payload.status,
            suitesCount: payload.suitesCount ?? 0,
            totalCount: payload.totalCount ?? 0,
            passedCount: payload.passedCount ?? 0,
            failedCount: payload.failedCount ?? 0,
            skippedCount: payload.skippedCount ?? 0,
            pendingCount: payload.pendingCount ?? 0,
            otherCount: payload.otherCount ?? 0,
            browsers: (payload.browsers ?? [])
              .filter((i: any) => i != null && i !== 'undefined')
              .join(','),
            coverage: payload.coverage,
            pipeline: pipelineContext,
            startAt: convertTimestampToDate(payload.startAt),
            endAt: convertTimestampToDate(payload.endAt)
          },
          {transaction: t, user}
        );

        const testCasesData = get(payload, 'tests', []).map(
          TestingController.normalizeTestCaseReport
        );

        // 批量添加测试用例
        const testCases = await TestCase.bulkCreate(testCasesData, {
          transaction: t
        });
        // 绑定关系
        await test.addTestCases(testCases, {transaction: t});

        return test;
      });

      ctx.status = 200;
      ctx.body = {
        msg: '创建成功',
        success: true,
        data: {
          id: hashId.encode(result.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `创建失败：${(error as Error).message}`);
    }

    next();
  }

  /**
   * 删除测试活动
   */
  @Post('/delete')
  static async deleteTest(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseTestingReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const test = await Testing.findByPk(id);

    if (!test) {
      ctx.throw(404, `删除失败：${ErrorMsg.NoSuchTest}`);
    }

    await test.destroy();

    ctx.status = 200;
    ctx.body = {
      msg: '删除成功',
      success: true,
      data: {
        id: payload.id
      }
    };

    next();
  }

  /**
   * 更新测试活动
   */
  @Post('/update')
  static async updateTest(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as UpdateTestingReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const test = await Testing.findByPk(id);

    if (!test) {
      ctx.throw(404, `更新失败：${ErrorMsg.NoSuchTest}`);
    }

    try {
      const patchData = omitNil({
        platform: payload.platform,
        arch: payload.arch,
        runtimeEnv: payload.runtimeEnv,
        testEnv: payload.testEnv,
        branch: payload.branch,
        status: payload.status,
        startAt: payload.startAt
          ? convertTimestampToDate(payload.startAt)
          : undefined,
        endAt: payload.endAt
          ? convertTimestampToDate(payload.endAt)
          : undefined,
        suitesCount: payload.suitesCount,
        totalCount: payload.totalCount,
        passedCount: payload.passedCount,
        failedCount: payload.failedCount,
        skippedCount: payload.skippedCount,
        pendingCount: payload.pendingCount,
        otherCount: payload.otherCount,
        falsePositiveCount: payload.falsePositiveCount,
        browsers:
          Array.isArray(payload?.browsers) && payload.browsers.length > 0
            ? payload.browsers
                .filter((i: any) => i != null && i !== 'undefined')
                .join(',')
            : undefined,
        toolName: payload.toolName,
        toolVersion: payload.toolVersion,
        coverage: payload.coverage,
        pipeline: payload.pipeline
      });

      if (isEmpty(patchData)) {
        ctx.throw(400, ErrorMsg.EmptyParameterError);
      }

      await test.update(patchData, {user} as any);
      await test.save();

      ctx.status = 200;
      ctx.body = {
        msg: '更新成功',
        success: true,
        data: {
          id: hashId.encode(test.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `更新失败：${(error as Error).message}`);
    }

    next();
  }

  /** Testing转化为前端使用的数据 */
  static normalizeTesting2FE(
    testing: Testing,
    omitKeys?: string[],
    withErrorTrace?: boolean
  ) {
    const test = testing.get();
    const iPipeContext = testing.get('_iPipeContext');
    const coverage = testing.get('_coverageReport');
    const defaultOmitPropsList = [
      'id',
      'TestCases',
      'updatedBy',
      'updatedAt',
      'coverage',
      '_coverageReport',
      'pipeline',
      '_iPipeContext'
    ];
    const omitList = uniq([
      ...defaultOmitPropsList,
      ...(Array.isArray(omitKeys) ? omitKeys.filter(Boolean) : [])
    ]);

    // 从IPipeContext中解析出commit message
    let commitMsg = '';
    try {
      const arr = JSON.parse(iPipeContext?.comments ?? '');
      // 取最后一条最为commit message
      commitMsg = arr[arr.length - 1]?.comment
        ?.replace(/\n/g, '')
        ?.replace(/Change-Id: [\w-]+/g, '');
    } catch (err) {}

    return test?.TestCases && Array.isArray(test.TestCases)
      ? {
          ...omit(test, omitList),
          id: hashId.encode(test.id),
          caseCount: test.TestCases.length,
          cases: test.TestCases.map(i =>
            TestCaseController.normalizeTestCase2FE(i, {
              withFK: false,
              withErrorTrace
            })
          ),
          pipeline: {...iPipeContext, commitMsg},
          coverage: coverage
        }
      : {
          ...omit(test, omitList),
          id: hashId.encode(test.id),
          pipeline: {...iPipeContext, commitMsg},
          coverage: coverage
        };
  }

  /**
   * 获取指定ID的测试活动
   */
  @Post('/get')
  static async getTest(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseTestingReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const testWithCases = await Testing.findByPk(id, {
      attributes: [
        'id',
        'uuid',
        ['tool_name', 'toolName'],
        ['tool_version', 'toolVersion'],
        'platform',
        'arch',
        ['runtime_env', 'runtimeEnv'],
        ['test_env', 'testEnv'],
        'branch',
        'status',
        ['start_at', 'startAt'],
        ['end_at', 'endAt'],
        ['total_count', 'totalCount'],
        ['passed_count', 'passedCount'],
        ['failed_count', 'failedCount'],
        ['skipped_count', 'skippedCount'],
        ['pending_count', 'pendingCount'],
        ['other_count', 'otherCount'],
        ['false_positive_count', 'falsePositiveCount'],
        'browsers',
        'coverage',
        'pipeline',
        'createdBy',
        ['created_by', 'createdBy']
      ],
      include: [
        {
          model: TestCase,
          as: 'TestCases',
          include: [
            {
              model: TestErrorTrace,
              attributes: [
                'id',
                ['is_false_positive', 'isFalsePositive'],
                ['icafe_id', 'icafeId'],
                ['icafe_space', 'icafeSpace']
              ]
            }
          ]
        }
      ]
    });

    if (!testWithCases) {
      ctx.throw(404, `获取失败：${ErrorMsg.NoSuchTest}`);
    }

    ctx.status = 200;
    ctx.body = {
      msg: '获取成功',
      success: true,
      data: TestingController.normalizeTesting2FE(testWithCases, [], true)
    };

    next();
  }

  /**
   * 获取测试活动列表
   */
  @Post('/list')
  static async listTest(ctx: Context, next: Next) {
    const payload = ctx.request.body as GetTestingListReq;
    const appId = payload.appId;

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const page = payload.page ?? 1;
    const perPage = payload.perPage ?? 10;
    const filterBranchName = payload?.filters?.branch ?? '';
    const filterStatus = payload?.filters?.status ?? [];
    const filterDateRange = payload?.filters?.startAt;
    const filterRuntimeEnv = payload?.filters?.runtimeEnv ?? [];

    try {
      const {rows, count} = await Testing.findAndCountAll({
        attributes: [
          'id',
          'uuid',
          ['tool_name', 'toolName'],
          ['tool_version', 'toolVersion'],
          'platform',
          'arch',
          ['runtime_env', 'runtimeEnv'],
          ['test_env', 'testEnv'],
          'branch',
          'status',
          ['start_at', 'startAt'],
          ['end_at', 'endAt'],
          ['total_count', 'totalCount'],
          ['passed_count', 'passedCount'],
          ['failed_count', 'failedCount'],
          ['skipped_count', 'skippedCount'],
          ['pending_count', 'pendingCount'],
          ['other_count', 'otherCount'],
          ['false_positive_count', 'falsePositiveCount'],
          'browsers',
          'coverage',
          'pipeline',
          ['created_by', 'createdBy']
        ],
        where: {
          appId: appId,
          ...(filterBranchName ? {branch: filterBranchName} : {}),
          ...(filterRuntimeEnv.length > 0
            ? {runtimeEnv: filterRuntimeEnv}
            : {}),
          ...(filterStatus.length > 0 ? {status: filterStatus} : {}),
          ...(filterDateRange && filterDateRange[0] && filterDateRange[1]
            ? {
                startAt: {
                  [Op.gte]: moment(filterDateRange[0]).toDate(),
                  [Op.lte]: moment(filterDateRange[1]).toDate()
                }
              }
            : {})
        },
        order: [[Sequelize.col('start_at'), 'DESC']],
        offset: (page - 1) * perPage,
        limit: perPage
      });

      ctx.status = 200;
      ctx.body = {
        msg: '列表获取成功',
        success: true,
        data: {
          items: rows.map((item, idx) => {
            const coverage = item.get('_coverageReport');

            return {
              ...TestingController.normalizeTesting2FE(item),
              /** 列表页仅需要综合覆盖率 */
              coverage: coverage?.total,
              orderIndex: (page - 1) * perPage + idx + 1
            };
          }),
          total: count,
          page,
          perPage,
          isTruncated: page * perPage >= count
        }
      };
    } catch (error) {
      ctx.throw(400, `列表获取失败：${(error as Error).message}`);
    }

    next();
  }

  /**
   * 测试任务数量-日期统计图
   */
  static async getCountDateDataset(appId: string, dateRange: [Date, Date]) {
    const result = await Testing.findAll<Testing>({
      attributes: [
        [Sequelize.fn('DATE', Sequelize.col('start_at')), 'date'],
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      where: {
        appId: appId,
        startAt: {
          [Op.gte]: dateRange[0],
          [Op.lte]: dateRange[1]
        }
      },
      group: [Sequelize.fn('DATE', Sequelize.col('start_at'))],
      order: [[Sequelize.fn('DATE', Sequelize.col('start_at')), 'ASC']]
    });

    // [`date`, `testing-count`]
    return zip(
      result.map(i => i.getDataValue('date')),
      result.map(i => i.getDataValue('count'))
    );
  }

  /**
   * 用例数量（按照状态堆积）-日期统计图
   */
  static async getCaseCountStackSeqDataset(
    appId: string,
    dateRange: [Date, Date]
  ) {
    const result = await Testing.findAll<Testing>({
      attributes: [
        ['total_count', 'totalCount'],
        ['passed_count', 'passedCount'],
        ['failed_count', 'failedCount'],
        ['skipped_count', 'skippedCount'],
        ['pending_count', 'pendingCount'],
        ['other_count', 'otherCount']
      ],
      where: {
        appId: appId,
        startAt: {
          [Op.gte]: dateRange[0],
          [Op.lte]: dateRange[1]
        }
      },
      order: [[Sequelize.fn('DATE', Sequelize.col('start_at')), 'ASC']]
    });

    // [`OrderNumber`, `passedCount`, `failedCount`, `skippedCount`, `pendingCount`, `otherCount`]
    return zip<string | number>(
      result.map((i, index) => `#${index + 1}`),
      result.map(i => i.getDataValue('passedCount')),
      result.map(i => i.getDataValue('failedCount')),
      result.map(i => i.getDataValue('skippedCount')),
      result.map(i => i.getDataValue('pendingCount')),
      result.map(i => {
        /** 这里兜底处理一下，如果预期总数 < 报告中记录的总数，则多出来的部分归属到other状态中 */
        const expectedTotalCount =
          i.getDataValue('passedCount') +
          i.getDataValue('failedCount') +
          i.getDataValue('skippedCount') +
          i.getDataValue('passedCount') +
          i.getDataValue('pendingCount');
        const otherCount = i.getDataValue('otherCount');
        const totalCount = i.getDataValue('totalCount');

        return expectedTotalCount < totalCount
          ? otherCount + totalCount - expectedTotalCount
          : otherCount;
      })
    );
  }

  /** 应用维度图表数据相关接口 */
  @Post('/stats/dataset')
  static async queryTestingData(ctx: Context, next: Next) {
    const payload = ctx.request.body as QueryTestingChartReq;
    const appId = payload.appId;
    const dimension = payload.dimension;
    /** 默认查询本周的数据 */
    const dateRange: [Date, Date] =
      payload.dateRange && payload.dateRange[0] && payload.dateRange[1]
        ? [
            moment(payload.dateRange[0]).toDate(),
            moment(payload.dateRange[1]).toDate()
          ]
        : [moment().startOf('week').toDate(), moment().toDate()];

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    if (!dimension) {
      ctx.throw(400, ErrorMsg.InvalidParameter('dimension'));
    }

    try {
      let datasetSource: (string | number | undefined)[][] = [];

      switch (dimension) {
        case 'count-date':
          datasetSource = await TestingController.getCountDateDataset(
            appId,
            dateRange
          );
          break;
        case 'case-count-stack-sequence':
          datasetSource = await TestingController.getCaseCountStackSeqDataset(
            appId,
            dateRange
          );
          break;
      }

      ctx.status = 200;
      ctx.body = {
        msg: '图表数据请求成功',
        success: true,
        data: {
          source: datasetSource
        }
      };
    } catch (error) {
      console.log(error);
      ctx.throw(400, `图表数据请求失败：${(error as Error).message}`);
    }

    next();
  }

  static async getTestingCount(appId: string, dateRange: [Date, Date]) {
    const result = await Testing.findAll({
      attributes: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'count']],
      where: {
        appId: appId,
        startAt: {
          [Op.gte]: dateRange[0],
          [Op.lte]: dateRange[1]
        }
      }
    });

    return result[0].getDataValue('count');
  }

  static async getAverageDuration(appId: string, dateRange: [Date, Date]) {
    const result = await Testing.findOne({
      attributes: [
        [
          Sequelize.fn(
            'AVG',
            Sequelize.literal('TIMESTAMPDIFF(SECOND, start_at, end_at)')
          ),
          'avgDurationInSecond'
        ]
      ],
      where: {
        appId: appId,
        startAt: {
          [Op.gte]: dateRange[0],
          [Op.lte]: dateRange[1]
        }
      }
    });

    return result?.getDataValue('avgDurationInSecond');
  }

  static async getPassedRatio(appId: string, dateRange: [Date, Date]) {
    const result = await Testing.findOne({
      attributes: [
        [
          Sequelize.literal(
            `ROUND((COUNT(CASE WHEN status = 'passed' THEN 1 END) / COUNT(*)) * 100, 2)`
          ),
          'passedRatio'
        ]
      ],
      where: {
        appId: appId,
        startAt: {
          [Op.gte]: dateRange[0],
          [Op.lte]: dateRange[1]
        }
      }
    });

    return result?.getDataValue('passedRatio');
  }

  static async getAvgTestCaseCount(appId: string, dateRange: [Date, Date]) {
    const result = await Testing.findOne({
      attributes: [
        [Sequelize.literal(`ROUND(AVG(total_count), 0)`), 'avgTestCaseCount']
      ],
      where: {
        appId: appId,
        startAt: {
          [Op.gte]: dateRange[0],
          [Op.lte]: dateRange[1]
        }
      }
    });

    return result?.getDataValue('avgTestCaseCount');
  }

  /** 应用维度统计数据相关接口 */
  @Post('/stats/data')
  static async queryTestingStatisticsData(ctx: Context, next: Next) {
    const payload = ctx.request.body as QueryTestingStatisticsReq;
    const appId = payload.appId;
    const dimension = payload.dimension;
    const dateRange: [Date, Date] =
      payload.dateRange && payload.dateRange[0] && payload.dateRange[1]
        ? [
            moment(payload.dateRange[0]).toDate(),
            moment(payload.dateRange[1]).toDate()
          ]
        : [moment().startOf('week').toDate(), moment().toDate()];

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    if (!dimension) {
      ctx.throw(400, ErrorMsg.InvalidParameter('dimension'));
    }

    try {
      let data: string | number | undefined;

      switch (dimension) {
        case 'count':
          data = await TestingController.getTestingCount(appId, dateRange);
          break;
        case 'avg-duration':
          data = await TestingController.getAverageDuration(appId, dateRange);
          break;
        case 'passed-ratio':
          data = await TestingController.getPassedRatio(appId, dateRange);
          break;
        case 'avg-case-count':
          data = await TestingController.getAvgTestCaseCount(appId, dateRange);
          break;
      }

      ctx.status = 200;
      ctx.body = {
        msg: '请求成功',
        success: true,
        data: {
          source: data
        }
      };
    } catch (error) {
      console.log(error);
      ctx.throw(400, `请求失败：${(error as Error).message}`);
    }

    next();
  }

  static getPeriodTimeRange(period: [string, string], cycle: StatsCycle) {
    // 当前周期开始结束时间
    const startTime = moment(period[0]).toDate();
    const endTime = moment(period[1]).toDate();
    // 上一周期开始结束时间
    let prevStartTime;
    let prevEndTime;

    switch (cycle) {
      case 'DoD':
        prevStartTime = moment(startTime).subtract(1, 'days').toDate();
        prevEndTime = moment(endTime).subtract(1, 'days').toDate();
        break;
      case 'WoW':
        prevStartTime = moment(startTime).subtract(1, 'weeks').toDate();
        prevEndTime = moment(endTime).subtract(1, 'weeks').toDate();
        break;
      case 'MoM':
        prevStartTime = moment(startTime).subtract(1, 'months').toDate();
        prevEndTime = moment(endTime).subtract(1, 'months').toDate();
        break;
      case 'MYoY':
        prevStartTime = moment(startTime).subtract(1, 'years').toDate();
        prevEndTime = moment(endTime).subtract(1, 'years').toDate();
        break;
    }

    return {
      startTime,
      endTime,
      prevStartTime,
      prevEndTime
    };
  }

  /** 全局维度图表数据相关接口, 暂未实现，预留占位 */
  // @Post('/stats/overview/dataset')
  // static async queryTestingOverviewDataset(ctx: Context, next: Next) {
  //   const payload = ctx.request.body as QueryTestingOverviewDSReq;

  //   const period = payload.period;
  //   const cycle = payload.cycle ?? 'WoW';

  //   if (!period || !Array.isArray(period) || period.length !== 2) {
  //     ctx.throw(400, ErrorMsg.InvalidParameter('period'));
  //   }

  //   const {startTime, endTime, prevStartTime, prevEndTime} =
  //     TestingController.getPeriodTimeRange(period, cycle);

  //   try {
  //     ctx.status = 200;
  //     ctx.body = {
  //       msg: '请求成功',
  //       success: true,
  //       data: {
  //         source: []
  //       }
  //     };
  //   } catch (error) {
  //     ctx.throw(400, `请求失败：${(error as Error).message}`);
  //   }

  //   next();
  // }

  static async getLatestAppTestingsByTime(startTime: Date, endTime: Date) {
    // 子查询，先找出每个app在时间范围内的最晚记录
    const subQuery = await Testing.findAll({
      attributes: [
        ['app_id', 'appId'],
        [fn('MAX', col('start_at')), 'latestStartAt']
      ],
      where: {
        startAt: {
          [Op.between]: [startTime, endTime]
        }
      },
      group: [col('app_id')],
      raw: true
    });

    const results = await Testing.findAll({
      attributes: [
        'id',
        ['start_at', 'startAt'],
        ['total_count', 'totalCount'],
        'coverage',
        'branch',
        'browsers',
        ['app_id', 'appId']
      ],
      include: [
        {
          model: AppManage
        }
      ],
      where: {
        [Op.or]: subQuery.map(({appId, latestStartAt}) => ({
          appId,
          startAt: latestStartAt // 匹配子查询中最晚的 start_at
        }))
      },
      // 按照用例数量降序排列
      order: [[col('total_count'), 'DESC']]
    });

    const testings = results.map(item => {
      const coverage = item.coverage as CoverageReport;

      return {
        id: item.id,
        startAt: item.startAt,
        totalCount: item.totalCount,
        coverage: coverage?.total,
        branch: item.branch,
        browsers: item.browsers,
        appId: item?.app_manage?.get?.('service_id') as string,
        appName: item?.app_manage?.get?.('service_name') as string
      };
    });

    return testings;
  }

  static async getTestingOverviewGroupList(
    startTime: Date,
    endTime: Date,
    prevStartTime: Date,
    prevEndTime: Date
  ) {
    // 本周期内产品测试合集
    const currentDatasource =
      await TestingController.getLatestAppTestingsByTime(startTime, endTime);
    // 上一周期内产品测试合集
    const prevDatasource = await TestingController.getLatestAppTestingsByTime(
      prevStartTime,
      prevEndTime
    );
    const prevDict = prevDatasource.reduce(
      (acc, item) => {
        const appId = item.appId;

        acc[appId] = {
          appName: item.appName,
          appId: appId,
          totalCount: item.totalCount,
          coverage: item.coverage,
          branch: item.branch,
          browsers: item.browsers
        };

        return acc;
      },
      {} as Record<
        string,
        {
          appName: string;
          appId: string;
          totalCount: number;
          coverage?: CoverageReport['total'];
          branch: string;
          browsers: string;
        }
      >
    );

    // 计算环比值
    const result = currentDatasource.map(item => {
      const prevDetail = prevDict[item.appId];
      const currentTotalCount = item.totalCount;
      const currentLineCount = item?.coverage?.lines?.total;
      const currentLineCoverage = item?.coverage?.lines?.pct;

      return {
        appId: item.appId,
        appName: item.appName,
        totalCount: currentTotalCount,
        totalCountChainRatio: calculatePercentageChange(
          currentTotalCount,
          prevDetail?.totalCount
        ),
        lineCount: currentLineCount,
        lineCountChainRatio: calculatePercentageChange(
          currentLineCount,
          prevDetail?.coverage?.lines?.total
        ),
        lineCoverage: currentLineCoverage,
        lineCoverageChainRatio: calculatePercentageChange(
          currentLineCoverage,
          prevDetail?.coverage?.lines?.pct
        ),
        branch: item.branch,
        browsers: item.browsers.split(',')
      };
    });

    return result;
  }

  /** 接入产品数量，接入率，测试数量，测试用例数量 */
  @Post('/stats/overview/count')
  static async getTestingOverviewStatsData(ctx: Context, next: Next) {
    try {
      const appTotalCount = await AppManage.findAll({
        attributes: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'count']]
      });

      const appsWithTesting = await AppManage.findAll({
        include: [{model: Testing, required: true, attributes: []}]
      });

      const testingCount = await Testing.findAll({
        attributes: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'count']]
      });

      const testCaseCount = await TestCase.findAll({
        attributes: [[Sequelize.fn('COUNT', Sequelize.col('id')), 'count']]
      });

      const appCount = appsWithTesting.length ?? 0;

      ctx.status = 200;
      ctx.body = {
        msg: '请求成功',
        success: true,
        data: {
          source: {
            appCount,
            appRatio:
              calculatePercentage(
                appCount,
                appTotalCount?.[0]?.getDataValue?.('count')
              ) ?? 0,
            testingCount: testingCount?.[0]?.getDataValue?.('count') ?? 0,
            testCaseCount: testCaseCount?.[0]?.getDataValue?.('count') ?? 0
          }
        }
      };
    } catch (error) {
      ctx.throw(400, `请求失败：${(error as Error).message}`);
    }

    next();
  }

  /** 全局维度数据相关接口 */
  @Post('/stats/overview/data')
  static async queryTestingOverviewData(ctx: Context, next: Next) {
    const payload = ctx.request.body as QueryTestingOverviewDataReq;
    const dimension = payload.dimension;
    const period = payload.period;
    const cycle = payload.cycle ?? 'WoW';

    if (!period || !Array.isArray(period) || period.length !== 2) {
      ctx.throw(400, ErrorMsg.InvalidParameter('period'));
    }

    if (!dimension) {
      ctx.throw(400, ErrorMsg.InvalidParameter('dimension'));
    }

    const {startTime, endTime, prevStartTime, prevEndTime} =
      TestingController.getPeriodTimeRange(period, cycle);

    try {
      let source: any;

      switch (dimension) {
        case 'group-list':
          source = await TestingController.getTestingOverviewGroupList(
            startTime,
            endTime,
            prevStartTime,
            prevEndTime
          );
          break;
      }

      ctx.status = 200;
      ctx.body = {
        msg: '请求成功',
        success: true,
        data: {
          source
        }
      };
    } catch (error) {
      ctx.throw(400, `请求失败：${(error as Error).message}`);
    }

    next();
  }
}
