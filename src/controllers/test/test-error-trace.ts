/**
 * @file controller/test-error-trace.ts
 * @desc 自动化测试-用例错误追踪相关接口
 */

import {Sequelize, Op} from 'sequelize';
import {type Context, type Next} from 'koa';
import axios from 'axios';
import moment from 'moment';
import isEmpty from 'lodash/isEmpty';
import omit from 'lodash/omit';
import uniq from 'lodash/uniq';
import cloneDeep from 'lodash/cloneDeep';
import {Controller, Post} from '../../decorators';
import {TestErrorTrace, Testing, TestCase} from '../../model';
import {new_db as db} from '../../utils';
import {ErrorMsg} from '../../utils/error';
import {hashId} from '../../utils/hashId';
import {omitNil, xssFilter} from '../../utils/helper';

interface BaseTestErrorTraceReq {
  id: string;
}

/** 新增用例错误追踪请求体 */
interface CreateTesErrorTraceReq {
  isFalsePositive: boolean;
  appId: string;
  testCaseId: string;
  icafeSpace?: string;
  // 新建用例标记时，icafe卡片来源：新建卡片或者绑定已存在的卡片
  icafeId?: string;
  icafe?: {
    icafeTitle: string;
    icafeContent?: string;
    icafeResponsiblePeople?: string;
  };
}

/** 编辑用例错误追踪请求体 */
interface UpdateTesErrorTraceReq {
  id: string;
  isFalsePositive?: boolean;
  appId?: string;
  testCaseId?: string;
  icafeSpace?: string;
  icafeId?: string;
  icafe?: {
    icafeTitle: string;
    icafeContent?: string;
    icafeResponsiblePeople?: string;
  };
}

/** 用例错误追踪列表请求体 */
interface GetTestErrorTraceListReq {
  appId: string;
  page?: number;
  perPage?: number;
  filters?: {
    isFalsePositive?: boolean;
    hasCard?: boolean;
    createdBy?: string;
    createdAt?: [string, string];
  };
}

interface icafeCardItem {
  title: string;
  type: string;
  detail?: string;
  creator?: string;
  fields?: {
    负责人?: string;
  };
}

/** 新建iCafe卡片请求体 */
interface NewIcafeCardReq {
  issues: icafeCardItem[];
}

const {ICAFE_AUTH_NAME = '', ICAFE_AUTH_PWD = ''} = process.env;

@Controller('/testing/trace/v1')
export class TestingTraceController {
  /** TestErrorTrace转化为前端使用的数据 */
  static normalizeTestErrorTrace2FE(
    testErrorTrace: TestErrorTrace,
    omitKeys?: string[]
  ) {
    const defaultOmitPropsList = ['id', 'TestCase'];
    const omitList = uniq([
      ...defaultOmitPropsList,
      ...(Array.isArray(omitKeys) ? omitKeys.filter(Boolean) : [])
    ]);

    // 从IPipeContext中解析出commit message
    const {testId, Test, name, ctrfId} = testErrorTrace.TestCase || {};
    let commitMsg = '';
    try {
      const arr = JSON.parse(Test?.pipeline?.comments ?? '');
      // 取最后一条最为commit message
      commitMsg = arr[arr.length - 1]?.comment
        ?.replace(/\n/g, '')
        ?.replace(/Change-Id: [\w-]+/g, '');
    } catch (err) {}

    // 获取所属测试任务信息
    const testing = {
      testId: testId ? hashId.encode(testId) : '',
      pipeline: {
        commitMsg,
        testReportUrl: Test?.pipeline?.testReportUrl
      }
    };

    return {
      ...omit(testErrorTrace.get(), omitList),
      id: hashId.encode(testErrorTrace.id),
      testCaseName: name,
      ctrfId,
      testing
    };
  }

  /** 组装新建卡片的参数 */
  static async normalizeCreateIcafeParams(params: any) {
    const {
      icafeTitle,
      icafeContent,
      icafeResponsiblePeople,
      username,
      testCaseId,
      appId
    } = params || {};

    // 卡片标题为空时使用测试用例名称作为标题
    let title = xssFilter(icafeTitle);
    if (!title) {
      const testCase = await TestCase.findByPk(testCaseId);
      title = testCase?.name || '';
    }

    return {
      issues: icafeTitle
        ? [
            {
              ...(username ? {creator: username} : {}),
              title,
              detail: icafeContent,
              type: '自动化测试巡检问题',
              fields: {
                负责人: icafeResponsiblePeople,
                云产品方向: appId
              }
            }
          ]
        : []
    };
  }

  /**
   * 新建iCafe卡片
   */
  static async createICafeCard(spaceId: string, params: NewIcafeCardReq) {
    if (!spaceId) {
      throw new Error('请检查iCafe空间');
    }
    if (
      !Array.isArray(params?.issues) ||
      !params?.issues[0]?.title ||
      !params?.issues[0]?.type
    ) {
      throw new Error('请检查iCafe参数');
    }

    const result: any = await axios({
      url: `http://icafeapi.baidu-int.com/api/v2/space/${spaceId}/issue/new`,
      method: 'post',
      data: {
        username: ICAFE_AUTH_NAME,
        password: ICAFE_AUTH_PWD,
        ...params
      }
    });

    if (!Array.isArray(result.data?.issueSequences)) {
      throw new Error(result.data?.message ?? '新建卡片失败');
    }

    return result.data?.issueSequences?.[0];
  }

  static requiredCreationParams: (keyof CreateTesErrorTraceReq)[] = [
    'isFalsePositive',
    'appId',
    'testCaseId'
  ];

  /**
   * 添加测试用例标记
   */
  @Post('/create')
  static async createTestErrorTrace(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as CreateTesErrorTraceReq;
    const testCaseId = hashId.decode(payload.testCaseId);
    let {isFalsePositive, icafeId, icafe, icafeSpace, appId} = payload;

    // 必填参数验证
    for (const key of TestingTraceController.requiredCreationParams) {
      if (payload[key] == null) {
        ctx.throw(400, ErrorMsg.InvalidParameter(key));
      }
    }

    // 查询数据库是否存在相同testCaseId
    const action = await TestErrorTrace.findOne({
      where: {
        testCaseId: testCaseId
      }
    });

    if (action) {
      ctx.throw(400, ErrorMsg.DuplicatedTestErrorTrace);
    }

    try {
      // !iCafe编号 && iCafe空间 && iCafe =》新建iCafe卡片
      if (!icafeId && icafeSpace && icafe) {
        const icafeParams =
          await TestingTraceController.normalizeCreateIcafeParams({
            ...icafe,
            username: user?.username,
            testCaseId,
            appId
          });
        icafeId = await TestingTraceController.createICafeCard(
          icafeSpace,
          icafeParams
        );
      }

      const result = await db.transaction(async t => {
        const testCaseItem = await TestCase.findByPk(testCaseId);

        const testErrorTrace = await TestErrorTrace.create(
          {
            isFalsePositive,
            ...(!!icafeId && !!icafeSpace ? {icafeId, icafeSpace} : {}),
            testCaseId,
            testId: testCaseItem?.testId,
            appId,
            createdBy: user?.username ?? 'system'
          },
          {transaction: t}
        );

        // 绑定关联关系
        await testErrorTrace.setTestCase(testCaseId, {transaction: t});

        if (isFalsePositive) {
          const originFailedCount = await TestCase.count({
            where: {
              status: 'failed',
              testId: testErrorTrace.testId
            }
          });
          const falsePositiveCount = await TestErrorTrace.count({
            where: {
              isFalsePositive: 1,
              testId: testErrorTrace.testId
            }
          });

          await Testing.update(
            {
              falsePositiveCount: falsePositiveCount + 1,
              failedCount:
                originFailedCount - falsePositiveCount <= 1
                  ? 0
                  : originFailedCount - falsePositiveCount - 1
            },
            {
              transaction: t,
              where: {id: testErrorTrace.testId}
            }
          );
        }

        return testErrorTrace;
      });

      ctx.status = 200;
      ctx.body = {
        msg: '创建成功',
        success: true,
        data: {
          id: hashId.encode(result.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `添加失败：${(error as Error)?.message}`);
    }

    next();
  }

  /**
   * 删除测试用例标记
   */
  @Post('/delete')
  static async deleteTestErrorTrace(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseTestErrorTraceReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const testErrorTrace = await TestErrorTrace.findByPk(id);

    if (!testErrorTrace) {
      ctx.throw(404, `删除失败：${ErrorMsg.NoSuchTestErrorTrace}`);
    }

    await testErrorTrace.destroy();

    if (testErrorTrace.isFalsePositive) {
      const originFailedCount = await TestCase.count({
        where: {
          status: 'failed',
          testId: testErrorTrace.testId
        }
      });
      const falsePositiveCount = await TestErrorTrace.count({
        where: {
          isFalsePositive: 1,
          testId: testErrorTrace.testId
        }
      });
      await Testing.update(
        {
          falsePositiveCount,
          failedCount: originFailedCount - falsePositiveCount
        },
        {where: {id: testErrorTrace.testId}}
      );
    }

    ctx.status = 200;
    ctx.body = {
      msg: '删除成功',
      success: true,
      data: {
        id: payload.id
      }
    };

    next();
  }

  /**
   * 更新测试用例标记
   */
  @Post('/update')
  static async updateTestErrorTrace(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as UpdateTesErrorTraceReq;
    const id = hashId.decode(payload?.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const testErrorTrace = await TestErrorTrace.findByPk(id);

    if (!testErrorTrace) {
      ctx.throw(404, `更新失败：${ErrorMsg.NoSuchTestErrorTrace}`);
    }

    try {
      let icafeId = payload.icafeId;
      if (!icafeId && payload.icafeSpace && payload.icafe) {
        const icafeParams =
          await TestingTraceController.normalizeCreateIcafeParams({
            ...payload.icafe,
            username: user?.username,
            testCaseId: testErrorTrace.testCaseId,
            appId: testErrorTrace.appId
          });
        icafeId = await TestingTraceController.createICafeCard(
          payload.icafeSpace,
          icafeParams
        );
      }
      const patchData = omitNil({
        isFalsePositive: payload?.isFalsePositive,
        icafeId,
        icafeSpace: payload?.icafeSpace
      });

      if (isEmpty(patchData)) {
        ctx.throw(400, ErrorMsg.EmptyParameterError);
      }

      // 校验iCafe卡片是否存在
      if (!!payload.icafeSpace && !!payload.icafeId) {
        try {
          await axios.get(
            `http://icafeapi.baidu-int.com/api/spaces/${patchData.icafeSpace}/cards/${patchData.icafeId}`,
            {params: {u: ICAFE_AUTH_NAME, pw: ICAFE_AUTH_PWD}}
          );
        } catch (error: any) {
          ctx.throw(error.response?.status, error.response?.data.message);
        }
      }

      const originFalsePositive = cloneDeep(testErrorTrace.isFalsePositive);
      await testErrorTrace.update({
        ...patchData,
        updatedBy: user?.username ?? 'system'
      });
      await testErrorTrace.save();

      if (
        typeof patchData.isFalsePositive === 'boolean' &&
        patchData.isFalsePositive !== originFalsePositive
      ) {
        const originFailedCount = await TestCase.count({
          where: {
            status: 'failed',
            testId: testErrorTrace.testId
          }
        });
        const falsePositiveCount = await TestErrorTrace.count({
          where: {
            isFalsePositive: 1,
            testId: testErrorTrace.testId
          }
        });

        const param = {
          falsePositiveCount,
          failedCount: originFailedCount - falsePositiveCount
        };
        await Testing.update(param, {where: {id: testErrorTrace.testId}});
      }

      ctx.status = 200;
      ctx.body = {
        msg: '更新成功',
        success: true,
        data: {
          id: hashId.encode(id)
        }
      };
    } catch (error) {
      ctx.throw(400, `更新失败：${(error as Error).message}`);
    }

    next();
  }

  /**
   * 获取指定ID的测试用例标记记录
   */
  @Post('/get')
  static async getTestErrorTrace(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseTestErrorTraceReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const testErrorTrace = await TestErrorTrace.findByPk(id);

    if (!testErrorTrace) {
      ctx.throw(404, `获取失败：${ErrorMsg.NoSuchTestErrorTrace}`);
    }

    // 获取iCafe卡片链接
    const {icafeSpace, icafeId} = testErrorTrace;
    const icafeUrl =
      !!icafeSpace && !!icafeId
        ? `https://console.cloud.baidu-int.com/devops/icafe/issue/${icafeSpace}-${icafeId}/show`
        : '';

    ctx.status = 200;
    ctx.body = {
      msg: '获取成功',
      success: true,
      data: {
        ...TestingTraceController.normalizeTestErrorTrace2FE(testErrorTrace),
        ...(icafeUrl ? {icafeUrl} : {})
      }
    };

    next();
  }

  /**
   * 获取测试用例标记列表
   */
  @Post('/list')
  static async listTestErrorTrace(ctx: Context, next: Next) {
    const payload = ctx.request.body as GetTestErrorTraceListReq;
    const appId = payload.appId;

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const page = payload.page ?? 1;
    const perPage = payload.perPage ?? 10;
    const filterFalsePositive = payload?.filters?.isFalsePositive;
    const filterHasCard = payload?.filters?.hasCard;
    const filterCreateBy = payload?.filters?.createdBy;
    const filterDateRange = payload?.filters?.createdAt;

    try {
      const {rows, count} = await TestErrorTrace.findAndCountAll({
        where: {
          appId: appId,
          ...(filterCreateBy ? {createdBy: filterCreateBy} : {}),
          ...(typeof filterFalsePositive === 'boolean'
            ? {isFalsePositive: filterFalsePositive}
            : {}),
          ...(typeof filterHasCard === 'boolean'
            ? {
                ['icafe_Id']: filterHasCard
                  ? {[Op.regexp]: '^[0-9]+$'}
                  : {[Op.or]: [null, '']}
              }
            : {}),
          ...(filterDateRange && filterDateRange[0] && filterDateRange[1]
            ? {
                createdAt: {
                  [Op.gte]: moment(filterDateRange[0]).toDate(),
                  [Op.lte]: moment(filterDateRange[1]).toDate()
                }
              }
            : {})
        },
        include: [
          {
            model: TestCase,
            attributes: [
              'testId',
              'name',
              'ctrfId',
              [Sequelize.col('created_at'), 'createdAt']
            ],
            include: [
              {
                model: Testing,
                as: 'Test',
                attributes: ['pipeline']
              }
            ]
          }
        ],
        distinct: true,
        order: [[Sequelize.literal('TestErrorTrace.created_at'), 'DESC']],
        offset: (page - 1) * perPage,
        limit: perPage
      });

      ctx.status = 200;
      ctx.body = {
        msg: '列表获取成功',
        success: true,
        data: {
          items: rows.map(item => {
            return TestingTraceController.normalizeTestErrorTrace2FE(item);
          }),
          total: count,
          page,
          perPage
        }
      };
    } catch (error) {
      ctx.throw(400, `列表获取失败：${(error as Error).message}`);
    }

    next();
  }
}
