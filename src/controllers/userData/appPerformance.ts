import {type Context, type Next} from 'koa';
import {Controller, Get} from '../../decorators';
import {AppPerformance} from '../../service';

@Controller('/app_performance')
export class AppPerformanceController {
  // 获取api索引
  @Get('/sys/mon/mapping')
  static async getMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getMapping(esType);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  // api索引重设
  @Get('/sys/mon/generateMapping')
  static async generateMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await AppPerformance.setMapping(esType);
    ctx.response.body = {
      result,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【页面性能】应用访问记录list接口
   */
  @Get('/query')
  static async getPerformanceInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getPerformanceInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【API性能】API访问记录list接口
   */
  @Get('/api/aggs/query')
  static async getApiPerformanceInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getApiPerformanceInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【页面性能】性能分层
   */
  @Get('/lamination/daily')
  static async getPerformanceInfoLaminationDaily(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getPerformanceInfoLaminationDaily(
      esType,
      query
    );
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【页面性能】性能分布
   */
  @Get('/distribution/daily')
  static async getPerformanceInfoDistributionDaily(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getPerformanceInfoDistributionDaily(
      esType,
      query
    );
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【页面性能】性能聚合信
  */
  @Get('/aggs/query')
  static async getPerformanceAggsInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getPerformanceAggsInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    应用启动次数、超过3s启动次数、超过3s占比
  */
  @Get('/baseinfo/overview')
  static async getPageInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getPagePerformanceInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    应用启动次数、超过3s启动次数、超过3s占比趋势
  */
  @Get('/baseinfo/overview/daily')
  static async getDailyPageInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getDailyPagePerformanceInfo(
      esType,
      query
    );
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/baseinfo/overview/all')
  static async getPageInfoAll(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const baseInfo = await AppPerformance.getPagePerformanceInfoGap(
      esType,
      query
    );
    const baseInfoDaily = await AppPerformance.getDailyPagePerformanceInfo(
      esType,
      query
    );
    ctx.response.body = {
      data: {
        ...baseInfo,
        ...baseInfoDaily
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【API性能】api平均响应时间
  */
  @Get('/api/baseinfo/overview')
  static async getApiInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getApiBaseInfoPerformanceInfo(
      esType,
      query
    );
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【API性能】api天粒度平均响应时间
  */
  @Get('/api/baseinfo/overview/daily')
  static async getDailyApiInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getDailyApiBaseInfoPerformanceInfo(
      esType,
      query
    );
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/api/baseinfo/overview/all')
  static async getApiInfoAll(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const baseInfo = await AppPerformance.getApiBaseInfoPerformanceInfoGap(
      esType,
      query
    );
    const baseInfoDaily =
      await AppPerformance.getDailyApiBaseInfoPerformanceInfo(esType, query);
    ctx.response.body = {
      data: {
        ...baseInfo,
        ...baseInfoDaily
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【页面性能】首屏时间中位数请求资源list
  */
  @Get('/median/resource/list')
  static async getMedianResourceList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await AppPerformance.getMedianResourceList(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    【页面性能】首屏时间top10页面列表
  */
  @Get('/fmp/top')
  static async getPerformanceTop(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await AppPerformance.getPerformanceTop(esType, query);
    ctx.response.body = {
      ...result,
      success: true,
      msg: '获取成功'
    };
    next();
  }
}
