import {type Context, type Next} from 'koa';
import {Controller, Get, Post, Delete} from '../../decorators';
import {Action} from '../../service';

@Controller('/action')
export class ActionController {
  // 获取Action索引
  @Get('/sys/mon/mapping')
  static async getMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Action.getMapping(esType);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  // Action索引重设
  @Get('/sys/mon/generateMapping')
  static async generateMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Action.setMapping(esType);
    ctx.response.body = {
      result,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  // Action日志条件查询
  @Get('/query')
  static async getData(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const query = ctx.request.query as any;
    const data = await Action.getData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // Action日志条件查询
  @Get('/agg/query')
  static async getAggData(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const query = ctx.request.query as any;
    const data = await Action.getAggData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Post('/manage/add')
  static async addAction(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      creator: ctx.headers['user-name']
    };
    const {msg, success} = await Action.addAction(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Post('/manage/edit')
  static async editAction(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      updater: ctx.headers['user-name']!
    };
    const {msg, success} = await Action.editAction(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Delete('/manage/delete')
  static async deleteAction(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const {msg, success} = await Action.deleteAction(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Get('/manage/list')
  static async getActionList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {data, msg, success} = await Action.getActionList(query);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  @Get('/trackId/list')
  static async geTrackIDList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Action.geTrackIDList(esType, query);
    ctx.response.body = {
      data,
      msg: '获取列表成功',
      success: true
    };
    next();
  }

  @Post('/chart/add')
  static async addChart(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      creator: ctx.headers['user-name']
    };
    const {msg, success} = await Action.addChart(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Get('/chart/list')
  static async getChartList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {data, msg, success} = await Action.getChartList(query);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  @Delete('/chart/delete')
  static async deleteChart(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const {msg, success} = await Action.deleteChart(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Get('/chart/detail')
  static async detailChart(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {data, msg, success} = await Action.detailChart(query);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  @Post('/chart/edit')
  static async editChart(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      updater: ctx.headers['user-name']
    };
    const {msg, success} = await Action.editChart(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Post('/chart/preview')
  static async previewChart(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const {data, msg, success} = await Action.previewChart(esType, body);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  @Get('/preview/list')
  static async previewListChart(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const {data, msg, success} = await Action.previewListChart(esType, query);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }
}
