import {Controller, Delete, Get, Post} from '../../decorators';
import {type Context, type Next} from 'koa';
import {Page} from '../../service';

@Controller('/page')
export class PageData {
  // 获取page索引的mapping
  @Get('/sys/mon/mapping')
  static async getMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getMapping(esType);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  // page索引重设
  @Get('/sys/mon/generateMapping')
  static async generateMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.setMapping(esType);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/query')
  static async getData(ctx: Context, next: Next) {
    const query = ctx.request.query as PageQuery;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/query/detail')
  static async getDataDetail(ctx: Context, next: Next) {
    const query = ctx.request.query as PageDetailQuery;
    const esType = ctx.request.header['ES-Type'] as string;
    if (!query.id) {
      ctx.response.body = {
        success: false,
        msg: '日志id为空'
      };
    } else {
      const data = await Page.getDataDetail(esType, query);
      ctx.response.body = {
        data,
        success: true,
        msg: '获取成功'
      };
    }
    next();
  }

  /**
   * 页面日志聚合查询
   */
  @Get('/aggs/query')
  static async getAggsData(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getAggsData(esType, query);

    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    页面访问量、异常量、报错率、页面报错弹窗数
   */
  @Get('/baseinfo/overview')
  static async getPageInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getPageInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    页面天粒度的访问量、报错量、报错率
   */
  @Get('/baseinfo/overview/daily')
  static async getDailyApiInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getDailyPageInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/baseinfo/overview/all')
  static async getPageInfoAll(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const baseInfo = await Page.getPageInfoGap(esType, query);
    const baseInfoDaily = await Page.getDailyPageInfo(esType, query);
    ctx.response.body = {
      data: {
        ...baseInfo,
        ...baseInfoDaily
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }
  /**
   * 页面趋势查询
   */
  @Post('/trend/query')
  static async getTrendData(ctx: Context, next: Next) {
    const query = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const [items, info] = await Promise.all([
      Page.getTrendDailyData(esType, query),
      Page.getTrendInfoData(esType, query)
    ]);
    ctx.response.body = {
      data: {
        items,
        info
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }
  /**
   * 用户维度页面分析
   */
  @Post('/user/trend/query')
  static async getUserTrendData(ctx: Context, next: Next) {
    const query = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getUserTrendDailyData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  /**
   * 子用户维度页面分析
   */
  @Post('/subuser/trend/query')
  static async getSubUserTrendData(ctx: Context, next: Next) {
    const query = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getSubUserTrendDailyData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  /**
   * 下级页面统计分析
   */
  @Post('/child/trend/query')
  static async getChildPageTrendData(ctx: Context, next: Next) {
    const query = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getChildPageTrendData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /**
   * 下级页面统计分析, 按tag聚合
   */
  @Post('/child/trend/query/bytag')
  static async getPageTrendDataByTag(ctx: Context, next: Next) {
    const query = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getPageTrendDataByTag(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /**
   * 页面来源统计分析
   */
  @Post('/source/trend/query')
  static async getSourcePageTrendData(ctx: Context, next: Next) {
    const query = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getSourcePageTrendData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /**
   * 页面去向统计分析
   */
  @Post('/direction/trend/query')
  static async getDirectionPageTrendData(ctx: Context, next: Next) {
    const query = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Page.getDirectionPageTrendData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Post('/url-map/add')
  static async addUrlMapping(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      creator: ctx.headers['user-name']
    };
    const {msg, success} = await Page.addUrlMapping(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Post('/url-map/edit')
  static async editUrlMapping(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      updater: ctx.headers['user-name']!
    };
    const {msg, success} = await Page.editUrlMapping(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Delete('/url-map/delete')
  static async deleteUrlMapping(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const {msg, success} = await Page.deleteUrlMapping(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  @Get('/url-map/list')
  static async getUrlMappingList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {data, msg, success} = await Page.getUrlMappingList(query);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  @Get('/url-map/tag/list')
  static async getPageTagList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {data, msg, success} = await Page.queryPageTagList(query);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  @Post('/url-map/tag/add')
  static async addPageTag(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      updater: ctx.headers['user-name']!
    };
    const {msg, success} = await Page.addPageTag(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  // 今日全产品访问统计
  @Get('/dashboard/view')
  static async getDashboardView(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Page.getPageInfoTrend(esType, query);
    const totalData = await Page.getPageInfoTrendTotal(esType, query);
    ctx.response.body = {
      ...result,
      totalData,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // Page Top
  @Get('/dashboard/top')
  static async getDashboardTop(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Page.getPageTop(esType, query);
    ctx.response.body = {
      ...result,
      success: true,
      msg: '获取成功'
    };
    next();
  }
}
