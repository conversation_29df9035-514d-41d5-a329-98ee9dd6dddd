import {type Context, type Next} from 'koa';
import {Controller, Delete, Get, Post} from '../../decorators';
import {Api} from '../../service';
import { RequiresPermission } from '../../decorators/permission';

@Controller('/api')
export class ApiData {
  // 获取api索引
  @Get('/sys/mon/mapping')
  static async getMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getMapping(esType);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  // api索引重设
  @Get('/sys/mon/generateMapping')
  static async generateMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Api.setMapping(esType);
    ctx.response.body = {
      result,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // api日志条件查询
  @Get('/query')
  static async getData(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // api日志条件查询
  @Get('/detail/query')
  static async getDetailData(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getApiErrorAggsData(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    api日志聚合查询
  */
  @Get('/aggs/query')
  static async getAggsData(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getAggsData(esType, query);

    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    api日志聚合查询
  */
  @Get('/query/aggs/daily')
  static async getApiMessageTrend(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getApiTrend(esType, query);

    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
      api错误信息详情
    */
  @Get('/query/trend/info')
  static async getApiMessageInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getMessageApiInfoData(esType, query);

    ctx.response.body = {
      items: data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
      api调用量、报错量、报错率
    */
  @Get('/baseinfo/overview')
  static async getApiInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getApiInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
      api天粒度的调用量、报错量、报错率
    */
  @Get('/baseinfo/overview/daily')
  static async getDailyApiInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getDailyApiInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    api接口报错信息 不同语言环境下的报错数、不合理报错数、不合理报错率
  */
  @Get('/baseinfo/locale/overview')
  static async getApiLocaleInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getApiLocaleInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  /*
    api接口报错信息 不同语言环境下的天粒度的报错数、不合理报错数、不合理报错率
  */
  @Get('/baseinfo/locale/overview/daily')
  static async getDailyApiLocaleInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Api.getDailyApiLocaleInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/baseinfo/overview/all')
  static async getApiInfoAll(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const baseInfo = await Api.getApiInfoGap(esType, query);
    const baseInfoDaily = await Api.getDailyApiInfo(esType, query);
    ctx.response.body = {
      data: {
        ...baseInfo,
        ...baseInfoDaily
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/baseinfo/locale/overview/all')
  static async getApiLocaleInfoAll(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const baseInfo = await Api.getApiLocaleInfoGap(esType, query);
    const baseInfoDaily = await Api.getDailyApiLocaleInfo(esType, query);
    ctx.response.body = {
      data: {
        ...baseInfo,
        ...baseInfoDaily
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Post('/statistics/baseinfo/overview/all')
  static async getStatisticsInfo(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const items = await Api.getStatisticsInfo(esType, body);
    ctx.response.body = {
      data: {
        ...items
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/exemption/rules')
  static async getApiExemptionsRules(ctx: Context, next: Next) {
    const query = ctx.request.query;

    if (!query.env || !query.serviceId) {
      ctx.response.body = {
        success: false,
        msg: 'env和service参数为必填'
      };
    } else {
      const data = await Api.getApiExemptionRules(query);
      ctx.response.body = {
        data,
        success: true,
        msg: '获取成功'
      };
    }
    next();
  }

  @Post('/exemption/rules')
  @RequiresPermission(['exemption_write'])
  static async addExemptionsRules(ctx: Context, next: Next) {
    let body: any = ctx.request.body;
    body.createdBy = ctx.headers['user-name'] as string;
    const data = await Api.addApiExemptionRule(body);
    ctx.response.body = data;
    next();
  }

  @Delete('/exemption/rules')
  @RequiresPermission(['exemption_write'])
  static async removeExemptionsRule(ctx: Context, next: Next) {
    const {id}: any = ctx.request.body;
    const deletedBy: string = ctx.headers['user-name'] as string;

    if (!id) {
      ctx.response.body = {
        success: false,
        msg: '规则id为必填'
      };
    } else {
      const data = await Api.removeApiExemptionRule(id, deletedBy);
      ctx.response.body = data;
    }
    next();
  }

  @Get('/error/compare/detail')
  static async getErrorMessageCompareDetail(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;

    const {
      serviceId,
      env,
      startTime,
      endTime,
      compareStartTime,
      compareEndTime
    } = query;
    if (
      !serviceId ||
      !env ||
      !startTime ||
      !endTime ||
      !compareStartTime ||
      !compareEndTime
    ) {
      ctx.response.body = {
        success: false,
        msg: '请检查必填参数'
      };
    } else {
      const data = await Api.getErrorMessageCompareDetail(esType, query);
      ctx.response.body = {
        data,
        success: true,
        msg: '获取成功'
      };
    }
    next();
  }
  // 今日全产品访问统计
  @Get('/dashboard/today')
  static async getDashboardToday(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Api.getApiTodayInfo(esType, query);
    ctx.response.body = {
      ...result,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 今日全产品访问统计
  @Get('/dashboard/api/trend')
  static async getDashboardView(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Api.getApiInfoTrend(esType, query);
    const totalData = await Api.getApiInfoTrendTotal(esType, query);
    ctx.response.body = {
      ...result,
      totalData,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // Uv Top
  @Get('/dashboard/top')
  static async getDashboardPvTop(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Api.getApiTop(esType, query);
    ctx.response.body = {
      ...result,
      success: true,
      msg: '获取成功'
    };
    next();
  }
}
