import {type Context, type Next} from 'koa';
import Schema from 'async-validator';
import isEmpty from 'lodash/isEmpty';
import isPlainObject from 'lodash/isPlainObject';
import {Controller, Get, Post} from '../../decorators';
import {new_db as db} from '../../utils';
import {Base, BaiduMail} from '../../service';
import {loginRules} from '../../utils/rules';
import {
  CONSOLE_ROOTDIR_NAME,
  DEFAULT_ROOTDIR_NAME,
  allServiceIdList,
  IAASList,
  specialProjectServiceIdList,
  // 不要删除，下面可能用到
  specialEmailReceivers,
  iaasEmailReceivers,
  iaasPmEmailReceivers,
  mailInfoMap
} from '../../utils';
import {ErrorMsg} from '../../utils/error';
import {hashId} from '../../utils/hashId';
import {xssFilter} from '../../utils/helper';
import {PageErrorHandle} from '../../utils/alarm';

import {
  SubmissionModel,
  QuestionModel,
  SurveyModel,
  AppManage
} from '../../model';
import {
  SurveyService,
  type GetSurveyContentReq,
  type SubmitSurveyReq,
  type ValidateSurveyReq
} from '../../service';
const env = process.env;

@Controller('/api')
export class ApiData {
  @Post('/sys/mon/save')
  static async save(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const rootDirName = esType ? CONSOLE_ROOTDIR_NAME : DEFAULT_ROOTDIR_NAME;
    const body = ctx.request.body;
    try {
      await Base.save(esType, rootDirName, body);
      ctx.response.body = {
        success: true,
        msg: '上报成功'
      };
    } catch (error) {
      ctx.response.body = {
        success: false,
        msg: '上报失败'
      };
    }
    next();
  }
  @Get('/baseinfo/snapshotLog')
  static async getSnapshotLogs(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {success, data} = await Base.getSnapshotLogs(query);
    if (success) {
      ctx.response.body = {
        data: {
          events: data
        },
        success: true,
        msg: '获取成功'
      };
    } else {
      ctx.response.body = {
        success: false,
        msg: data
      };
    }
    next();
  }

  @Get('/baseinfo')
  static async getBaseInfo(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Base.getBaseInfo(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  @Get('/baseinfo/daily')
  static async getBaseInfoDaily(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Base.getBaseInfoDaily(esType, query);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Get('/baseinfo/all')
  static async getBaseInfoAll(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const baseInfo = await Base.getBaseInfoGap(esType, query);
    const baseInfoDaily = await Base.getBaseInfoDaily(esType, query);
    ctx.response.body = {
      data: {
        ...baseInfo,
        ...baseInfoDaily
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Post('/performance/statistics')
  static async getStatisticsInfo(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const items = await Base.getPerformanceStatistics(esType, body);
    ctx.response.body = {
      data: {
        ...items
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 数据总览接口
  @Post('/overview/list')
  static async getOverviewList(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Base.getOverviewList(esType, {...body, ...query});
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 数据对比接口
  @Post('/overview/compare')
  static async getOverviewData(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const comparEsType = ctx.request.header['Compare-Time-ES-Type'] as string;
    const data = await Base.getOverviewCompareData(esType, comparEsType, {
      ...body,
      ...query
    });
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  @Post('/sys/feedback/save')
  static async feedbackSave(ctx: Context, next: Next) {
    const body = ctx.request.body;
    const feedback = typeof body === 'string' ? JSON.parse(body) : body;

    const satisfyInfo = feedback['feedback-satisfy-info'] as SatisfyInfo;
    const validator = new Schema(loginRules);
    const esType = ctx.request.header['ES-Type'] as string;
    const rootDirName = esType ? CONSOLE_ROOTDIR_NAME : DEFAULT_ROOTDIR_NAME;
    const {error} = await validator.validate(satisfyInfo).catch(err => {
      return {
        error: err.errors[0].message
      };
    });

    if (error) {
      return (ctx.response.body = {
        success: false,
        msg: error
      });
    }

    await Base.feedbackSave(rootDirName, body);
    ctx.response.body = {
      success: true,
      msg: '上报成功'
    };
    next();
  }
  @Get('/send/email')
  static async sendEmail(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {to, type} = query;
    if (!to) {
      return (ctx.response.body = {
        success: false,
        msg: '缺少收件人地址'
      });
    }
    const scene = mailInfoMap[type] ? type : 'iaas';
    await BaiduMail.sendEmail(
      {
        // IAAS每周五发送邮件
        // to: iaasEmailReceivers.join(','),
        // IAASPM每周四发送邮件
        // to: iaasPmEmailReceivers.join(','),
        // 报错体验优化专项周一发送邮件
        // to: specialEmailReceivers.join(','),
        // query指定收件人地址
        to,
        subject: mailInfoMap[scene].subject
      },
      mailInfoMap[scene].serviceList,
      scene
    );
    ctx.response.body = {
      success: true,
      msg: '上报成功'
    };
    next();
  }

  @Get('/send/trend/email')
  static async sendTrendEmail(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {type} = query;
    await BaiduMail.genTrendReport(
      type === 'iaas' ? IAASList : specialProjectServiceIdList,
      type
    );
    await BaiduMail.sendFileToBos(type);
    ctx.response.body = {
      success: true,
      msg: '上报成功'
    };
    next();
  }

  @Get('/get/process/env')
  static async getEnvInfo(ctx: Context, next: Next) {
    console.log(env);
    ctx.response.body = {
      success: true,
      data: env,
      msg: '获取成功'
    };
    next();
  }
  /**
   * ==================== * 调查问卷相关接口 * ====================
   */

  /**
   * 获取调查问卷内容（Hub侧）
   */
  @Post('/sys/survey/get')
  static async getSurveyContent(ctx: Context, next: Next) {
    const payload = ctx.request.body as GetSurveyContentReq;
    const appId = payload.appId;
    const id = hashId.decode(payload.id);

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchAppSurvey);
    }

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const survey = await SurveyModel.findOne({
      attributes: ['id', 'title', 'images', 'description'],
      where: {
        id,
        appId
      },
      include: {
        model: QuestionModel,
        as: 'Questions'
      }
    });

    if (!survey) {
      ctx.throw(404, ErrorMsg.NoSuchSurvey);
    }

    ctx.status = 200;
    ctx.body = {
      msg: '获取成功',
      success: true,
      data: SurveyService.normalizeSurvey2FE(survey)
    };

    next();
  }

  /**
   * 验证是否存在当前应用的调查问卷，以及当前账号是否已经填写过指定的调查问卷
   * 填写过则返回true，否则返回false
   */
  @Post('/sys/survey/validate')
  static async validateSurvey(ctx: Context, next: Next) {
    const payload = ctx.request.body as ValidateSurveyReq;

    if (isEmpty(payload)) {
      return [400, ErrorMsg.InvalidEmptyParameter];
    }

    if (!isPlainObject(payload)) {
      return [400, ErrorMsg.InvalidNonObjectParameter];
    }

    const appId = payload?.appId;
    const surveyId = hashId.decode(payload?.id);
    const accountId = payload.accountId;
    const userId = payload?.userId;

    if (!appId || typeof appId !== 'string') {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    if (surveyId == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    if (!accountId || typeof accountId !== 'string') {
      ctx.throw(400, ErrorMsg.InvalidParameter('accountId'));
    }

    /** userId只验证格式的合法性 */
    if (userId && typeof userId !== 'string') {
      ctx.throw(400, ErrorMsg.InvalidParameter('userId'));
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      return [404, ErrorMsg.NoSuchAppSurvey];
    }

    const survey = await SurveyModel.findByPk(surveyId);

    if (!survey) {
      ctx.throw(404, ErrorMsg.NoSuchSurvey);
    }

    try {
      /** 传入了2个ID，即认为是子用户 */
      const isSubUser = !!accountId && !!userId;
      /** 如果是主账户用户，则没有userId，如果是子用户，则accountId和userId都存在 */
      const submission = await SubmissionModel.findOne({
        where: {
          accountId,
          userId: isSubUser ? userId : null,
          surveyId
        }
      });

      ctx.status = 200;
      ctx.body = {
        msg: '',
        success: true,
        data: {
          submitted: submission ? true : false
        }
      };
    } catch (error: any) {
      ctx.throw(500, `调查问卷内容检查失败: ${error.message}`);
    }

    next();
  }

  /**
   * 提交调查问卷（Hub侧）
   */
  @Post('/sys/survey/submit')
  static async submitSurvey(ctx: Context, next: Next) {
    const payload = ctx.request.body as SubmitSurveyReq;
    /** 参数验证 */
    const [status, errorMsg] =
      await SurveyService.validateCreationSubmissionParams(payload);

    if (status !== 200) {
      ctx.throw(status, errorMsg);
    }

    const surveyId = hashId.decode(payload.id);
    const answers = payload.answers;

    /** 问题和答案参数验证 */
    const [status2, errorMsg2] = await SurveyService.validateQuestionAndAnswers(
      answers,
      surveyId
    );

    if (status2 !== 200) {
      ctx.throw(status2, errorMsg2);
    }

    try {
      const result = await db.transaction(async t => {
        const submission = await SubmissionModel.create(
          {
            surveyId,
            accountId: payload.context.accountId,
            userId: payload.context.userId,
            browser: payload.context.browser,
            browserVersion: payload.context.browserVersion,
            platform: payload.context.platform,
            resolution: payload.context.resolution,
            browserResolution: payload.context.browserResolution,
            language: payload.context.language,
            referer: payload.context.referer,
            name: xssFilter(payload.context.name)
              ? xssFilter(payload.context.name)
              : null,
            phone: payload.context.phone,
            answers: payload.answers.map(item => {
              return {
                content: xssFilter(item.content)
                  ? xssFilter(item.content)
                  : undefined,
                questionId: hashId.decode(item.questionId)
              };
            })
          },
          {transaction: t}
        );

        return submission;
      });

      ctx.status = 200;
      ctx.body = {
        msg: '提交成功',
        success: true,
        data: {
          id: hashId.encode(result.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `提交失败：${(error as Error).message}`);
    }
  }

  @Get('/get/email/list')
  static async getEmailList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {type} = query;
    let {data, success} = await BaiduMail.getEmailList(type);
    ctx.response.body = {
      data: success ? data : [],
      success: success,
      msg: success ? '获取邮件列表成功' : '获取邮件列表失败'
    };
    next();
  }

  @Get('/get/email/receivers')
  static async getEmailReceivers(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {type} = query;
    ctx.response.body = {
      data: mailInfoMap[type]?.receivers || [],
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 通过平台发送邮件
  @Post('/send/email/app')
  static async sendEmailByApp(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const user = ctx.userInfo as UUAPUserInfo;
    const {to, id, type} = body;
    if (!to) {
      return (ctx.response.body = {
        success: false,
        msg: '缺少收件人地址'
      });
    }
    const {success, msg} = await BaiduMail.sendEmailByAPP(
      id,
      to,
      type,
      user.username
    );
    ctx.response.body = {
      success: success,
      msg: msg || '发送邮件失败'
    };
    next();
  }
  // 通过ID获取邮件内容
  @Get('/get/email/detail')
  static async getEmailDetailById(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const {id} = query;
    if (!id) {
      return (ctx.response.body = {
        success: false,
        msg: '缺少邮件ID'
      });
    }
    const {success, data} = await BaiduMail.getEmailDetailById(id);
    ctx.response.body = {
      success: success,
      data: data
    };
    next();
  }
  // 通过平台更新邮件
  @Post('/update/email/app')
  static async updateEmailByApp(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const user = ctx.userInfo as UUAPUserInfo;
    const {id, data} = body;
    const {success, msg} = await BaiduMail.updateEmailByAPP(
      id,
      data,
      user.username
    );
    ctx.response.body = {
      success: success,
      msg: msg || '发送邮件失败'
    };
    next();
  }
}
