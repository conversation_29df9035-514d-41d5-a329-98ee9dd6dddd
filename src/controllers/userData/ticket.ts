import {getTicketCount} from '../../service';
import {Controller, Get} from '../../decorators';
import {type Context, type Next} from 'koa';
import {Base} from '../../service';

@Controller('/api')
export class TicketController {
  // api接口 刷语言环境数据
  @Get('/ticket_count')
  static async resetLocale(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const {timeRange, env, serviceId} = query;
    // 工单数据
    const result = await getTicketCount({
      timeRange,
      env,
      serviceId
    });
    // 发起总览数据请求
    const baseInfo = await Base.getBaseInfoGap(esType, query);
    const baseInfoDaily = await Base.getBaseInfoDaily(esType, query);
    const baseResult = {
      data: {
        ...baseInfo,
        ...baseInfoDaily
      },
      success: true,
      msg: '获取成功'
    };

    if (baseResult.success && baseResult.data?.items?.length) {
      baseResult.data.items.forEach((item: any) => {
        let ticketInfo = result?.dayCounts?.find(
          (info: any) => info.day === item.time
        );
        item.tickets = ticketInfo?.count || 0;
      });
    }

    ctx.response.body = {
      status: 0,
      msg: '',
      data: {success: baseResult?.success, ...baseResult.data, ...result}
    };
    next();
  }
}
