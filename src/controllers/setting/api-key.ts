/**
 * @file controller/api-key.ts
 * @desc Api Key相关接口
 */

require('dotenv').config();

import moment from 'moment';
import {Sequelize, Op} from 'sequelize';
import {type Context, type Next} from 'koa';
import pick from 'lodash/pick';
import {Controller, Post} from '../../decorators';
import {ApiKey, AppManage} from '../../model';
import {new_db as db} from '../../utils';
import {ErrorMsg} from '../../utils/error';
import TokenGenerator from '../../utils/tokenGen';
import {hashId} from '../../utils/hashId';

/** 每个应用可创建API Key的配额，通常来讲一个就够了，带有子产品的可能需要多个 */
const MAX_API_KEY_QUOTA_BY_APP = 3;

interface BaseAPIKeyReq {
  appId: string;
}

interface GenerateAPIKeyReq extends BaseAPIKeyReq {}

interface RevokeAPIKeyReq extends BaseAPIKeyReq {
  id: string;
  token: string;
}

interface ListAPIKeyReq extends BaseAPIKeyReq {}

@Controller('/apikey/v1')
export class ApiKeyController {
  @Post('/generate')
  static async generateApiKey(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;

    if (!user || !user.username) {
      ctx.throw(400, ErrorMsg.NoSuchUser);
    }

    const payload = ctx.request.body as GenerateAPIKeyReq;
    const appId = payload.appId;

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    try {
      const result = await db.transaction(async t => {
        // 查找当前APP下所有可用的ApiKey
        const {count} = await ApiKey.findAndCountAll({
          where: {
            appId: appId,
            expiredAt: {
              [Op.gte]: new Date()
            }
          },
          transaction: t
        });

        if (count >= MAX_API_KEY_QUOTA_BY_APP) {
          throw new Error(ErrorMsg.ApiKeyQuotaLimitation);
        }

        const tokenGen = new TokenGenerator({keyPrefix: appId});
        const tokenCmpt = await tokenGen.getToken();
        // 过期时间使用默认值（1年）
        const apiKey = await ApiKey.create(
          {
            token: tokenCmpt.shortToken,
            hash: tokenCmpt.longTokenHash,
            createdBy: user.username,
            appId
          },
          {transaction: t}
        );

        return {
          /** 该Token不会入库，需要用户妥善保管 */
          token: tokenCmpt.token,
          expiredAt: moment(apiKey.expiredAt).format('YYYY-MM-DD HH:mm:ss')
        };
      });

      ctx.status = 200;
      ctx.body = {
        msg: 'API Key创建成功，平台不会储存Token，丢失的Token无法查询，请注意保管',
        success: true,
        data: result
      };
    } catch (error) {
      ctx.throw(400, `API Key创建失败：${(error as Error).message}`);
    }

    next();
  }

  @Post('/revoke')
  static async revokeApiKey(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;

    if (!user || !user.username) {
      ctx.throw(400, ErrorMsg.NoSuchUser);
    }

    const payload = ctx.request.body as RevokeAPIKeyReq;
    const appId = payload.appId;
    const token = payload.token;
    const id = payload.id;

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    if (!token) {
      ctx.throw(400, ErrorMsg.InvalidParameter('token'));
    }

    if (!id) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    if (typeof token !== 'string') {
      ctx.throw(400, ErrorMsg.InValidTokenParameter);
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      ctx.throw(404, ErrorMsg.NoSuchApp);
    }

    try {
      const apiKey = await ApiKey.findOne({
        where: {
          appId,
          token: token,
          id: hashId.decode(id)
        }
      });

      if (!apiKey) {
        ctx.throw(404, ErrorMsg.NoSuchApiKey);
      }

      // 废弃的Key直接干掉，没什么保留的必要了
      await apiKey.destroy();

      ctx.status = 200;
      ctx.body = {
        msg: 'API Key删除成功',
        success: true,
        data: {
          token: token,
          expiredAt: moment(apiKey.expiredAt).format('YYYY-MM-DD HH:mm:ss')
        }
      };
    } catch (error) {
      ctx.throw(400, `API Key删除失败：${(error as Error).message}`);
    }

    next();
  }

  @Post('/list')
  static async getApiKeyList(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;

    if (!user || !user.username) {
      ctx.throw(400, ErrorMsg.NoSuchUser);
    }

    const payload = ctx.request.body as ListAPIKeyReq;
    const appId = payload.appId;

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    try {
      const {rows, count} = await ApiKey.findAndCountAll({
        attributes: [
          'id',
          'token',
          ['created_by', 'createdBy'],
          ['created_at', 'createdAt'],
          ['expired_at', 'expiredAt'],
          ['last_used_at', 'lastUsedAt']
        ],
        where: {
          appId,
          expiredAt: {
            [Op.gte]: new Date()
          }
        },
        limit: MAX_API_KEY_QUOTA_BY_APP,
        order: [[Sequelize.fn('DATE', Sequelize.col('created_at')), 'DESC']]
      });

      ctx.status = 200;
      ctx.body = {
        msg: '获取成功',
        success: true,
        data: {
          items: rows.map(item => {
            const itemData = item.get();

            return {
              ...pick(itemData, [
                'token',
                'createdBy',
                'createdAt',
                'expiredAt',
                'lastUsedAt'
              ]),
              id: hashId.encode(itemData.id)
            };
          }),
          total: count
        }
      };
    } catch (error) {
      ctx.throw(400, `获取失败：${(error as Error).message}`);
    }

    next();
  }
}
