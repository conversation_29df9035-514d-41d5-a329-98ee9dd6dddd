import {Controller, Post} from '../../decorators';
import {type Context, type Next} from 'koa';
import {Tools} from '../../service';

@Controller('/tool')
export class UtilsController {
  // api接口 url解析为通配符格式
  @Post('/reset/urlToRegUrl')
  static async resetUrlToRegUrl(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    Tools.resetUrlToRegUrl(esType, body);
    ctx.response.body = {
      success: true,
      msg: '后台任务开启成功'
    };
    next();
  }

  // api接口 刷语言环境数据
  @Post('/reset/locale')
  static async resetLocale(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const esType = ctx.request.header['ES-Type'] as string;
    Tools.resetLocale(esType, body);
    ctx.response.body = {
      success: true,
      msg: '后台任务开启成功'
    };
    next();
  }

  // 检测一下redis数据
  @Post('/redis/check')
  static async checkRedis(ctx: Context, next: Next) {
    const body = ctx.request.body as any;
    const data = await Tools.checkRedis(body);
    ctx.response.body = {
      success: true,
      data,
      msg: 'redis服务正常运行中'
    };
    next();
  }
}
