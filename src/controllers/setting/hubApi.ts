import {type Context, type Next} from 'koa';
import {Controller, Get, Post, Delete} from '../../decorators';
import {<PERSON>b<PERSON><PERSON>} from '../../service';

@Controller('/hub')
export class HubApiData {
  @Post('/sync')
  static async sync(ctx: Context, next: Next) {
    ctx.response.body = {
      success: true,
      data: await HubApi.syncHubInterface(ctx)
    };

    next();
  }

  @Get('/interfaces')
  static async getInterfaces(ctx: Context, next: Next) {
    const params: any = ctx.query;
    ctx.response.body = {
      success: true,
      data: await HubApi.getInterfaces(params)
    };

    next();
  }

  // 获取用户的服务列表
  @Get('/service/list')
  static async getInterfacesService(ctx: Context, next: Next) {
    ctx.response.body = {
      success: true,
      data: await HubApi.getServices(ctx)
    };

    next();
  }

  // 创建自定义服务
  @Post('/service/create')
  static async createInterfacesService(ctx: Context, next: Next) {
    const params: any = ctx.request.body;
    ctx.response.body = {
      data: await HubApi.createServices(params)
    };
    next();
  }

  // 创建自定义服务
  @Post('/service/edit')
  static async editInterfacesService(ctx: Context, next: Next) {
    const params: any = ctx.request.body;
    ctx.response.body = {
      data: await HubApi.editServices(params)
    };
    next();
  }

  // 创建自定义服务
  @Delete('/service/delete')
  static async deleteInterfacesService(ctx: Context, next: Next) {
    const params: any = ctx.query;
    ctx.response.body = {
      data: await HubApi.deleteServices(params)
    };
    next();
  }
}
