/**
 * 应用管理相关接口
 */
import {type Context, type Next} from 'koa';
import {Controller, Get, Post, Delete} from '../../decorators';
import {AppManage} from '../../service';

@Controller('/app')
export class AppController {
  // 新建应用
  @Post('/add')
  static async addApp(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      creator: ctx.headers['user-name']
    };
    const {msg, success} = await AppManage.addApp(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  // 编辑应用
  @Post('/edit')
  static async editApp(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      updater: ctx.headers['user-name']
    };
    const {msg, success} = await AppManage.editApp(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  // 列举应用
  @Get('/list')
  static async queryAppList(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const tags = query.tags ? query.tags.split(',') : [];
    const {data, msg, success} = await AppManage.queryAppList({...query, tags});
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  // 删除应用
  @Post('/delete')
  static async deleteApp(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      creator: ctx.headers['user-name']
    };
    const {msg, success} = await AppManage.deleteApp(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  // 列举应用标签
  @Get('/tag/list')
  static async queryAppTagList(ctx: Context, next: Next) {
    const {data, msg, success} = await AppManage.queryAppTagList();
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

  // 新增应用标签
  @Post('/tag/add')
  static async addAppTagList(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      creator: ctx.headers['user-name']
    };
    const {msg, success} = await AppManage.addAppTag(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }

  // 删除应用标签
  @Post('/tag/delete')
  static async deleteAppTag(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
      creator: ctx.headers['user-name']
    };
    const {msg, success} = await AppManage.deleteAppTag(body);
    ctx.response.body = {
      success,
      msg
    };
    next();
  }
}
