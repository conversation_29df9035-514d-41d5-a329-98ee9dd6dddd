/**
 * 权限管理相关接口
 */
import {type Context, type Next} from 'koa';
import {Controller, Get, Post} from '../../decorators';
import {AuthManage} from '../../service';

@Controller('/auth')

export class AppController {
    // 查询全局资源
    @Get('/global/resource/list')
    static async queryGlobalReource(ctx: Context, next: Next) {
      const query = ctx.request.query as any;
      const {data, msg, success} = await AuthManage.queryGlobalReourceMeta();
      ctx.response.body = {
        data,
        success,
        msg
      };
      next();
    }
    // 查询应用下全部角色
    @Get('/global/roles/list')
    static async queryGlobalRoles(ctx: Context, next: Next) {
      const query = ctx.request.query as any;
      const {data, msg, success} = await AuthManage.queryGlobalRoles(query);
      ctx.response.body = {
        data,
        success,
        msg
      };
      next();
    }

  // 查询当前账号下所有角色
  @Post('/user/roles/list')
  static async queryUserRoles(ctx: Context, next: Next) {
    const body = {
      ...(ctx.request.body as any),
    };

    const {data, msg, success} = await AuthManage.queryUserRoles(body);
    ctx.response.body = {
      data,
      success,
      msg
    };
    next();
  }

    // 用户是否具备的角色及权限
    @Post('/user/role_auth/query')
    static async queryUserRoleAndResource(ctx: Context, next?: Next) {
      const body = ctx.request.body as any;
      const userAccount = body?.userAccount || ctx.userInfo.username;
      const {data, msg, success} = await AuthManage.queryUserRoleAndResource(userAccount);
      ctx.response.body = {
        data,
        success,
        msg
      };
      next?.();
    }

    // 用户是否具备某些权限
    @Post('/user/auth/query')
    static async queryUserAuth(ctx: Context, next: Next) {
      const {data, msg, success} = await AuthManage.queryResourceAuth(ctx.request.body as any);
      ctx.response.body = {
        data,
        success,
        msg
      };
      next();
    }
}
