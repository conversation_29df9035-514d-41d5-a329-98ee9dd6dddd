// /**
//  * 产品文案相关接口
//  */
// import {type Context, type Next} from 'koa';
// import {Controller, Get, Post} from '../../decorators';
// import {ServiceContentManage} from '../../service';
// import {DEFAULT_CLIENT} from '../../utils';

// @Controller('/content')
// export class AppController {
//   // 新建/编辑数据结构
//   @Post('/schema/edit')
//   static async editSchema(ctx: Context, next: Next) {
//     const body = {
//       ...(ctx.request.body as any),
//       creator: ctx.headers['user-name'] || DEFAULT_CLIENT,
//       updater: ctx.headers['user-name'] || DEFAULT_CLIENT
//     };
//     const {msg, success} = await ServiceContentManage.editSchema(body);
//     ctx.response.body = {
//       success,
//       msg
//     };
//     next();
//   }

//   // 获取数据结构
//   @Get('/schema/get')
//   static async getSchema(ctx: Context, next: Next) {
//     const query = ctx.request.query as any;
//     const {data, msg, success} = await ServiceContentManage.getSchema({
//       ...query
//     });
//     ctx.response.body = {
//       data,
//       success,
//       msg
//     };
//     next();
//   }

//   // 编辑文案内容
//   @Post('/data/edit')
//   static async editData(ctx: Context, next: Next) {
//     const body = {
//       ...(ctx.request.body as any),
//       updater: ctx.headers['user-name']
//     };
//     const {msg, success} = await ServiceContentManage.editData(body);
//     ctx.response.body = {
//       success,
//       msg
//     };
//     next();
//   }

//   // 获取文案内容
//   @Get('/data/get')
//   static async getData(ctx: Context, next: Next) {
//     const query = ctx.request.query as any;
//     const {data, msg, success} = await ServiceContentManage.getData({...query});
//     ctx.response.body = {
//       data,
//       success,
//       msg
//     };
//     next();
//   }

//   // 发布文案
//   @Post('/publish')
//   static async publishContent(ctx: Context, next: Next) {
//     const body = {
//       ...(ctx.request.body as any),
//       creator: ctx.headers['user-name']
//     };
//     const {msg, success} = await ServiceContentManage.publishContent(body);
//     ctx.response.body = {
//       success,
//       msg
//     };
//     next();
//   }

//   // 上传文案内容
//   @Post('/upload')
//   static async post(ctx: Context, next: Next) {
//     const body = {
//       ...(ctx.request.body as any),
//       creator: ctx.headers['user-name']
//     };
//     const {msg, success} = await ServiceContentManage.upload(body);
//     ctx.response.body = {
//       success,
//       msg
//     };
//     next();
//   }
// }
