/**
 * @file controller/question.ts
 * @desc 调查问卷问题相关接口（平台侧）
 */

import {Sequelize} from 'sequelize';
import isEmpty from 'lodash/isEmpty';
import {new_db as db} from '../../utils';
import {Controller, Post} from '../../decorators';
import {QuestionModel} from '../../model/survey/question';
import {ErrorMsg} from '../../utils/error';
import {hashId} from '../../utils/hashId';
import {
  SurveyService,
  type CreateQuestionReq,
  type BaseSurveyReq,
  type UpdateQuestionReq,
  type GetQuestionListReq,
  type BulkUpdateQuestionReq
} from '../../service';

import type {Context, Next} from 'koa';
import {SurveyModel} from '../../model';

@Controller('/question/v1')
export class QuestioController {
  /**
   * 新增问题
   */
  @Post('/create')
  static async createQuestion(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as CreateQuestionReq;
    const surveyId = hashId.decode(payload.surveyId);
    const [status, errorMsg] = await SurveyService.validateFKSurvey(surveyId);

    if (status !== 200) {
      ctx.throw(status, errorMsg);
    }

    const [status2, errorMsg2] =
      SurveyService.validateCreationQuestionParams(payload);

    if (status2 !== 200) {
      ctx.throw(status2, errorMsg2);
    }

    try {
      const result = await db.transaction(async t => {
        const question = await QuestionModel.create(
          {
            ...SurveyService.normalizeCreateQuestion2SQL(payload, {
              createdBy: user?.username ?? SurveyService.defaultUsername,
              surveyId: surveyId
            })
          },
          {transaction: t}
        );

        // 绑定关联关系
        await question.setSurvey(surveyId, {transaction: t});

        return question;
      });

      ctx.status = 200;
      ctx.body = {
        msg: '创建成功',
        success: true,
        data: {
          id: hashId.encode(result.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `创建失败：${(error as Error).message}`);
    }

    next();
  }

  /**
   * 删除问题
   */
  @Post('/delete')
  static async deleteQuestionById(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseSurveyReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const question = await QuestionModel.findByPk(id);

    if (!question) {
      ctx.throw(404, `删除失败：${ErrorMsg.NoSuchQuestion}`);
    }

    await question.destroy();

    ctx.status = 200;
    ctx.body = {
      msg: '删除成功',
      success: true,
      data: {
        id: payload.id
      }
    };

    next();
  }

  /**
   * 更新问题
   */
  @Post('/update')
  static async updateQuestionById(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as UpdateQuestionReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const question = await QuestionModel.findByPk(id);

    if (!question) {
      ctx.throw(404, `更新失败：${ErrorMsg.NoSuchQuestion}`);
    }

    const [status, errorMsg] =
      SurveyService.validateUpdateQuestionParams(payload);

    if (status !== 200) {
      ctx.throw(status, errorMsg);
    }

    try {
      const updatedData = SurveyService.normalizeUpdateQuestion2SQL(payload);

      if (isEmpty(updatedData)) {
        ctx.throw(400, ErrorMsg.EmptyParameterError);
      }

      await question.update({
        ...updatedData,
        updatedBy: user?.username ?? SurveyService.defaultUsername
      });
      await question.save();

      ctx.status = 200;
      ctx.body = {
        msg: '更新成功',
        success: true,
        data: {
          id: hashId.encode(question.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `更新失败：${(error as Error).message}`);
    }

    next();
  }

  @Post('/bulkUpdate')
  static async bulkUpdateQuestion(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const username = user?.username ?? SurveyService.defaultUsername;
    const payload = ctx.request.body as BulkUpdateQuestionReq;
    const surveyId = hashId.decode(payload.surveyId);
    const toInsert = payload?.toInsert ?? [];
    const toDelete = payload?.toDelete ?? [];
    const toUpdate = payload?.toUpdate ?? [];

    /** 验证待删除元素的ID是否合法 */
    if (
      toDelete != null &&
      (!Array.isArray(toDelete) ||
        toDelete.some(i => !i || typeof i !== 'string'))
    ) {
      ctx.throw(400, ErrorMsg.InvalidParameterPattern('toDelete'));
    }

    if (toUpdate != null && !Array.isArray(toUpdate)) {
      ctx.throw(400, ErrorMsg.InvalidParameterPattern('toUpdate'));
    }

    if (toInsert != null && !Array.isArray(toInsert)) {
      ctx.throw(400, ErrorMsg.InvalidParameterPattern('toInsert'));
    }

    if (toInsert.length < 1 && toUpdate.length < 1 && toDelete.length < 1) {
      ctx.throw(400, ErrorMsg.InvalidEmptyParameter);
    }

    /** 验证待更新元素的请求体是否合法 */
    for (const updateItem of toUpdate) {
      let [status, errorMsg] =
        SurveyService.validateUpdateQuestionParams(updateItem);

      if (status !== 200) {
        ctx.throw(status, errorMsg);
      }
    }

    /** 验证待插入元素的请求体是否合法 */
    for (const insertedItem of toInsert) {
      let [status2, errorMsg2] =
        SurveyService.validateCreationQuestionParams(insertedItem);

      if (status2 !== 200) {
        ctx.throw(status2, errorMsg2);
      }
    }

    if (!surveyId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('surveyId'));
    }

    const survey = await SurveyModel.findByPk(surveyId);

    if (!survey) {
      ctx.throw(404, `获取失败：${ErrorMsg.NoSuchSurvey}`);
    }

    try {
      const results = await db.transaction(async t => {
        const ids: Record<'toInsert' | 'toDelete' | 'toUpdate', string[]> = {
          toInsert: [],
          toDelete: [],
          toUpdate: []
        };

        /** 处理新增的元素 */
        if (toInsert.length > 0) {
          const inserted = await QuestionModel.bulkCreate(
            toInsert.map(i =>
              SurveyService.normalizeCreateQuestion2SQL(i, {
                surveyId,
                createdBy: username
              })
            ),
            {transaction: t}
          );
          ids.toInsert = inserted.map(i => hashId.encode(i.id));
        }

        /** 处理删除的元素 */
        if (toDelete.length > 0) {
          await QuestionModel.destroy({
            transaction: t,
            where: {id: toDelete.map(i => hashId.decode(i))}
          });
          ids.toDelete = toDelete;
        }

        /** 处理更新的元素 */
        if (toUpdate.length > 0) {
          for (const updatedItem of toUpdate) {
            await QuestionModel.update(
              {
                ...SurveyService.normalizeUpdateQuestion2SQL(updatedItem),
                updatedBy: username
              },
              {
                transaction: t,
                where: {id: hashId.decode(updatedItem.id)}
              }
            );
          }
          ids.toUpdate = toUpdate.map(i => i.id);
        }

        return ids;
      });

      ctx.status = 200;
      ctx.body = {
        msg: '批量更新成功',
        success: true,
        data: {
          ids: results
        }
      };
    } catch (error) {
      ctx.throw(400, `批量更新失败：${(error as Error).message}`);
    }

    next();
  }

  /**
   * 获取指定ID的问题
   */
  @Post('/get')
  static async getQuestionById(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseSurveyReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const question = await QuestionModel.findByPk(id, {
      attributes: [
        'id',
        'type',
        'title',
        'images',
        'description',
        'required',
        'isSubQuestion',
        'options',
        'maxLength',
        ['created_by', 'createdBy'],
        ['created_at', 'createdAt'],
        ['created_by', 'createdBy'],
        ['updated_at', 'updatedAt']
      ]
    });

    if (!question) {
      ctx.throw(404, `获取失败：${ErrorMsg.NoSuchQuestion}`);
    }

    ctx.status = 200;
    ctx.body = {
      msg: '获取成功',
      success: true,
      data: SurveyService.normalizeSurveyQuestion2FE(question)
    };

    next();
  }

  /**
   * 获取指定调查问卷问题列表
   */
  @Post('/list')
  static async listQuestion(ctx: Context, next: Next) {
    const payload = ctx.request.body as GetQuestionListReq;
    const surveyId = hashId.decode(payload.surveyId);

    if (!surveyId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('surveyId'));
    }

    const survey = await SurveyModel.findByPk(surveyId);

    if (!survey) {
      ctx.throw(404, `获取失败：${ErrorMsg.NoSuchSurvey}`);
    }

    const page = payload.page ?? 1;
    const perPage = payload.perPage ?? 100;

    try {
      const {rows, count} = await QuestionModel.findAndCountAll({
        attributes: [
          'id',
          'order',
          'type',
          'title',
          'images',
          'description',
          'required',
          'isSubQuestion',
          'options',
          'maxLength',
          ['created_by', 'createdBy'],
          ['created_at', 'createdAt'],
          ['created_by', 'createdBy'],
          ['updated_at', 'updatedAt']
        ],
        order: [[Sequelize.col('order'), 'ASC']],
        offset: (page - 1) * perPage,
        limit: perPage,
        where: {
          surveyId: payload.surveyId
        }
      });

      ctx.status = 200;
      ctx.body = {
        msg: '列表获取成功',
        success: true,
        data: {
          items: rows.map((item, idx) => {
            return {
              ...SurveyService.normalizeSurveyQuestion2FE(item, {withFK: false})
            };
          }),
          total: count,
          page,
          perPage
        }
      };
    } catch (error) {
      ctx.throw(400, `列表获取失败：${(error as Error).message}`);
    }

    next();
  }
}
