/**
 * @file controller/survey/survey.ts
 * @desc 调查问卷相关接口（平台侧）
 */

import moment from 'moment';
import {Sequelize} from 'sequelize';
import isEmpty from 'lodash/isEmpty';
import orderBy from 'lodash/orderBy';
import groupBy from 'lodash/groupBy';
import XLSX from 'xlsx-js-style';
import {new_db as db} from '../../utils';
import {Controller, Get, Post} from '../../decorators';
import {
  SurveyModel,
  QuestionModel,
  SubmissionModel,
  AppManage
} from '../../model';
import {ErrorMsg} from '../../utils/error';
import {omitNil, xssFilter} from '../../utils/helper';
import {hashId} from '../../utils/hashId';
import {
  SurveyService,
  type CreateSurveyReq,
  type BaseSurveyReq,
  type UpdateSurveyReq,
  type GetSurveyListReq,
  type DownloadStatisticsQuery
} from '../../service';

import type {Context, Next} from 'koa';
import type {SubmissionAnswerItem} from '../../model/survey/submission';

@Controller('/survey/v1')
export class SurveyController {
  /**
   * 新增调查问卷
   */
  @Post('/create')
  static async createSurvey(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as CreateSurveyReq;
    const appId = payload?.appId;

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      return [404, ErrorMsg.NoSuchAppSurvey];
    }

    const [status, errorMsg] =
      SurveyService.validateCreationSurveyParams(payload);

    if (status !== 200) {
      ctx.throw(status, errorMsg);
    }

    /** 如果携带了问题，一起验证 */
    const questionCollection = payload.questions;
    if (questionCollection && !isEmpty(questionCollection)) {
      for (const question of questionCollection) {
        const [status2, errorMsg2] =
          SurveyService.validateCreationQuestionParams(question);

        if (status2 !== 200) {
          ctx.throw(status2, errorMsg2);
        }
      }
    }

    try {
      const result = await db.transaction(async t => {
        const creator = user?.username ?? SurveyService.defaultUsername;

        const survey = await SurveyModel.create(
          {
            appId: payload.appId,
            title: xssFilter(payload.title),
            ...(xssFilter(payload.description)
              ? {description: xssFilter(payload.description)}
              : {}),
            ...(payload.images ? {images: payload.images} : {}),
            createdBy: creator
          },
          {transaction: t}
        );

        if (questionCollection && !isEmpty(questionCollection)) {
          const questions = await QuestionModel.bulkCreate(
            questionCollection.map(q => {
              return {
                ...SurveyService.normalizeCreateQuestion2SQL(q, {
                  createdBy: creator,
                  surveyId: survey.id
                })
              };
            }),
            {transaction: t}
          );

          // 绑定关联关系
          await survey.addQuestions(questions, {transaction: t});
        }

        return survey;
      });

      ctx.status = 200;
      ctx.body = {
        msg: '创建成功',
        success: true,
        data: {
          id: hashId.encode(result.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `创建失败：${(error as Error).message}`);
    }

    next();
  }

  /**
   * 删除调查问卷
   */
  @Post('/delete')
  static async deleteSurvey(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseSurveyReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const survey = await SurveyModel.findByPk(id);

    if (!survey) {
      ctx.throw(404, `删除失败：${ErrorMsg.NoSuchSurvey}`);
    }

    await survey.destroy();

    ctx.status = 200;
    ctx.body = {
      msg: '删除成功',
      success: true,
      data: {
        id: payload.id
      }
    };

    next();
  }

  /**
   * 更新调查问卷
   */
  @Post('/update')
  static async updateSurvey(ctx: Context, next: Next) {
    const user = ctx.userInfo as UUAPUserInfo;
    const payload = ctx.request.body as UpdateSurveyReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const survey = await SurveyModel.findByPk(id);

    if (!survey) {
      ctx.throw(404, `更新失败：${ErrorMsg.NoSuchSurvey}`);
    }

    const [status, errorMsg] =
      SurveyService.validateUpdateSurveyParams(payload);

    if (status !== 200) {
      ctx.throw(status, errorMsg);
    }

    try {
      const patchData = omitNil({
        title: xssFilter(payload.title) ? xssFilter(payload.title) : undefined,
        description: xssFilter(payload.description)
          ? xssFilter(payload.description)
          : undefined,
        images: payload.images
      });

      if (isEmpty(patchData)) {
        ctx.throw(400, ErrorMsg.EmptyParameterError);
      }

      await survey.update({
        ...patchData,
        updatedBy: user?.username ?? SurveyService.defaultUsername
      });
      await survey.save();

      ctx.status = 200;
      ctx.body = {
        msg: '更新成功',
        success: true,
        data: {
          id: hashId.encode(survey.id)
        }
      };
    } catch (error) {
      ctx.throw(400, `更新失败：${(error as Error).message}`);
    }

    next();
  }

  /**
   * 获取指定ID的调查问卷信息
   */
  @Post('/get')
  static async getSurvey(ctx: Context, next: Next) {
    const payload = ctx.request.body as BaseSurveyReq;
    const id = hashId.decode(payload.id);

    if (id == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const surveyWithQuestions = await SurveyModel.findByPk(id, {
      attributes: [
        'id',
        'title',
        'description',
        'images',
        ['created_by', 'createdBy'],
        ['updated_at', 'updatedAt'],
        ['created_by', 'createdBy'],
        ['updated_at', 'updatedAt']
      ],
      include: {model: QuestionModel, as: 'Questions'}
    });

    if (!surveyWithQuestions) {
      ctx.throw(404, `获取失败：${ErrorMsg.NoSuchSurvey}`);
    }

    ctx.status = 200;
    ctx.body = {
      msg: '获取成功',
      success: true,
      data: SurveyService.normalizeSurvey2FE(surveyWithQuestions)
    };

    next();
  }

  /**
   * 获取调查问卷列表
   */
  @Post('/list')
  static async listSurvey(ctx: Context, next: Next) {
    const payload = ctx.request.body as GetSurveyListReq;
    const appId = payload.appId;

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const page = payload.page ?? 1;
    const perPage = payload.perPage ?? 10;

    try {
      const {rows, count} = await SurveyModel.findAndCountAll({
        attributes: [
          'id',
          'title',
          'description',
          'images',
          ['created_by', 'createdBy'],
          ['created_at', 'createdAt'],
          ['created_by', 'createdBy'],
          ['updated_at', 'updatedAt']
        ],
        include: {model: QuestionModel, as: 'Questions'},
        distinct: true,
        where: {appId},
        order: [[Sequelize.col('created_at'), 'ASC']],
        offset: (page - 1) * perPage,
        limit: perPage
      });

      ctx.status = 200;
      ctx.body = {
        msg: '列表获取成功',
        success: true,
        data: {
          items: rows.map(item => {
            return {...SurveyService.normalizeSurvey2FE(item)};
          }),
          total: count,
          page,
          perPage
        }
      };
    } catch (error) {
      ctx.throw(400, `列表获取失败：${(error as Error).message}`);
    }

    next();
  }

  @Get('/download')
  static async downloadStatistics(ctx: Context, next: Next) {
    const query = ctx.request.query as unknown as DownloadStatisticsQuery;
    const appId = query?.appId;
    const surveyId = hashId.decode(query?.id);

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      return [404, ErrorMsg.NoSuchAppSurvey];
    }

    if (surveyId == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const survey = await SurveyModel.findByPk(surveyId, {
      include: {
        model: QuestionModel,
        as: 'Questions'
      }
    });

    if (!survey) {
      ctx.throw(404, `下载失败：${ErrorMsg.NoSuchSurvey}`);
    }

    const submissions = await SubmissionModel.findAll({
      attributes: [
        'id',
        ['account_id', 'accountId'],
        ['user_id', 'userId'],
        'name',
        'phone',
        'browser',
        ['browser_version', 'browserVersion'],
        'platform',
        'resolution',
        ['browser_resolution', 'browserResolution'],
        'language',
        'referer',
        ['created_at', 'createdAt'],
        'answers'
      ],
      where: {
        surveyId: surveyId
      }
    });

    const rawData = await Promise.all(
      submissions.map(async submission => {
        const row = submission.toJSON();
        const record: Record<string, any> = {
          ID: hashId.encode(row.id),
          账户ID: row.accountId,
          用户ID: row.userId,
          是否子用户: !!row.userId ? '是' : '否',
          姓名: row.name,
          联系方式: row.phone,
          浏览器信息: row.browser,
          浏览器版本: row.browserVersion,
          设备信息: row.platform,
          屏幕分辨率: row.resolution,
          浏览器分辨率: row.browserResolution,
          语言: row.language,
          创建时间: moment(row.createdAt).format('YYYY-MM-DD HH:mm:ss'),
          来源地址: row.referer ?? '无'
        };

        if (!survey.Questions) {
          return record;
        }

        const userAnswers: SubmissionAnswerItem[] = row.answers;
        /** 问题元数据 */
        const questions = orderBy(
          survey.Questions,
          ['order', 'isSubQuestion', 'createdAt', 'updatedAt'],
          ['asc', 'asc', 'asc', 'asc']
        );
        /** 作答数据 */
        questions?.forEach(q => {
          const qId = q.id;
          const qTitle = q.title;
          const answserItem = userAnswers.find(i => i.questionId === qId);

          record[qTitle] = answserItem?.content ?? '未作答';
        });

        return record;
      })
    );

    const workSheet = XLSX.utils.json_to_sheet(rawData);

    /** 表头 */
    const headers = Object.keys(rawData[0]) as string[];

    /** 添加表头样式 */
    headers.forEach((header, index) => {
      const cellAddress = XLSX.utils.encode_cell({r: 0, c: index});
      workSheet[cellAddress].s = {
        font: {
          name: '微软雅黑',
          bold: true,
          sz: 12,
          color: {rgb: '000000'}
        },
        fill: {
          fgColor: {rgb: 'F2F2F2'}
        },
        alignment: {
          horizontal: 'center',
          vertical: 'center'
        },
        border: {
          top: {style: 'thin', color: {rgb: '000000'}},
          bottom: {style: 'thin', color: {rgb: '000000'}},
          left: {style: 'thin', color: {rgb: '000000'}},
          right: {style: 'thin', color: {rgb: '000000'}}
        }
      };
    });
    /** 添加单元格样式样式 */
    rawData.forEach((rowData, rowIndex) => {
      headers.forEach((col, colIndex) => {
        const cell = XLSX.utils.encode_cell({r: rowIndex + 1, c: colIndex});
        workSheet[cell].s = {
          font: {
            name: '微软雅黑',
            sz: 11,
            color: {rgb: '000000'}
          }
        };
      });
    });

    /** 设置列宽，单位像素 */
    workSheet['!cols'] = headers.map(title => {
      if (title === 'ID') {
        return {wpx: 70};
      } else if (title === '账户ID' || title === '用户ID') {
        return {wpx: 200};
      } else if (title === '创建时间') {
        return {wpx: 125};
      } else if (
        [
          '是否子用户',
          '姓名',
          '联系方式',
          '浏览器信息',
          '浏览器版本',
          '设备信息',
          '屏幕分辨率',
          '浏览器分辨率',
          '语言',
          '来源地址'
        ].includes(title)
      ) {
        return {wpx: 80};
      } else {
        /** 问题题目，拉长一点 */
        return {wpx: 200};
      }
    });

    const workBook = XLSX.utils.book_new();
    const fileName = `${survey.title}.xlsx`;

    XLSX.utils.book_append_sheet(workBook, workSheet, appId);

    const buffer = XLSX.write(workBook, {type: 'buffer', bookType: 'xlsx'});

    /** 设置媒体类型 & 下载头 */
    ctx.set(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    ctx.set(
      'Content-Disposition',
      `attachment; filename="${encodeURIComponent(fileName)}"`
    );
    ctx.body = buffer;

    next();
  }

  /**
   * 获取调查问卷统计信息，包括信息：
   * - 回收量
   * - 回收趋势
   * - 设备、系统、浏览器、语言分布
   *
   */
  @Post('/stat/data')
  static async getSurveyStatData(ctx: Context, next: Next) {
    const payload = ctx.request.body as DownloadStatisticsQuery;
    const appId = payload?.appId;
    const surveyId = hashId.decode(payload?.id);

    if (!appId) {
      ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      return [404, ErrorMsg.NoSuchAppSurvey];
    }

    if (surveyId == null) {
      ctx.throw(400, ErrorMsg.InvalidParameter('id'));
    }

    const survey = await SurveyModel.findByPk(surveyId, {
      include: {
        model: QuestionModel,
        as: 'Questions'
      }
    });

    if (!survey) {
      ctx.throw(404, `下载失败：${ErrorMsg.NoSuchSurvey}`);
    }

    try {
      const submissions = (
        await SubmissionModel.findAll({
          attributes: [
            'id',
            ['account_id', 'accountId'],
            ['user_id', 'userId'],
            'name',
            'phone',
            'browser',
            ['browser_version', 'browserVersion'],
            'platform',
            'resolution',
            ['browser_resolution', 'browserResolution'],
            'language',
            'referer',
            ['created_at', 'createdAt'],
            'answers'
          ],
          where: {
            surveyId: surveyId
          },
          order: [[Sequelize.fn('DATE', Sequelize.col('created_at')), 'ASC']]
        })
      ).map(i => SurveyService.normalizeSubmission2FE(i, survey.Questions));

      /* 按日期 (YYYY-MM-DD) 聚合数据 */
      const groupedByDate = groupBy(submissions, item =>
        moment(item.createdAt).format('YYYY-MM-DD')
      );

      const groupedOS = groupBy(submissions, item => item.os);
      const groupedDevice = groupBy(submissions, item => item.device);
      const groupedBrowser = groupBy(submissions, item => item.browser);
      const groupedLang = groupBy(submissions, item => item.language);

      ctx.status = 200;
      ctx.body = {
        msg: '查询成功',
        success: true,
        data: {
          /** 回收数量 */
          submissionCount: submissions.length ?? 0,
          /** 数量-日期趋势图数据 */
          submissionDataset: Object.entries(groupedByDate).map(i => [
            i[0],
            i[1].length
          ]),
          osRatio: Object.entries(groupedOS).map(i => [i[0], i[1].length]),
          deviceRatio: Object.entries(groupedDevice).map(i => [
            i[0],
            i[1].length
          ]),
          browserRatio: Object.entries(groupedBrowser).map(i => [
            i[0],
            i[1].length
          ]),
          languageRatio: Object.entries(groupedLang).map(i => [
            i[0],
            i[1].length
          ])
        }
      };
    } catch (error) {
      ctx.throw(400, `查询失败：${(error as Error).message}`);
    }

    next();
  }
}
