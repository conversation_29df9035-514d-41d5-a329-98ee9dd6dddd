import {type Context, type Next} from 'koa';
import {Controller, Get} from '../../decorators';
import {Satisfy} from '../../service';

@Controller('/satisfy')
export class SatisfyInfo {
  // 获取satisfy索引
  @Get('/sys/mon/mapping')
  static async getMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Satisfy.getMapping(esType);
    ctx.response.body = {
      data,
      success: true,
      msg: '获取成功'
    };
    next();
  }
  // satisfy索引重设
  @Get('/sys/mon/generateMapping')
  static async generateMapping(ctx: Context, next: Next) {
    const esType = ctx.request.header['ES-Type'] as string;
    const result = await Satisfy.setMapping(esType);
    ctx.response.body = {
      result,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 获取满意度数据分类
  @Get('/baseinfo/title')
  static async getSatisfyTitle(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const items = await Satisfy.getSatisfyInfoTitle(esType, query);
    ctx.response.body = {
      data: {
        items
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 获取满意度组件概览数据
  @Get('/baseinfo/overview')
  static async getSatisfyOverview(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const {satisfyInfoRatio, total, initTotal} =
      await Satisfy.getSatisfyInfoRatio(esType, query);
    const {scoreRatio, scoreAverage, scoreCount} =
      await Satisfy.getSatisfyScoreRatio(esType, query, total);
    ctx.response.body = {
      data: {
        satisfyInfoRatio,
        total,
        initTotal,
        scoreRatio,
        scoreAverage,
        scoreCount
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 不满意原因统计
  @Get('/aggs/reasonTags')
  static async getSatisfyReasonTags(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const reasonTags = await Satisfy.getSatisfyReasonTags(esType, query);
    ctx.response.body = {
      data: reasonTags,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 其他原因详情
  @Get('/aggs/reason')
  static async getSatisfyReason(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const reasonTags = await Satisfy.getSatisfyReasons(esType, query);
    ctx.response.body = {
      data: reasonTags,
      success: true,
      msg: '获取成功'
    };
    next();
  }

  // 下载所有满意度调查记录
  @Get('/download')
  static async downloadSatisfyDate(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const data = await Satisfy.downloadSatisfyDate(esType, query);
    ctx.response.set({
      'Content-Type': 'application/octet-stream; charset=UTF-8',
      'Content-Disposition': `attachment; filename*=UTF-8; filename=satisfy.xlsx`
    });
    ctx.response.body = data;
    next();
  }

  // 获取扩展资源数据
  @Get('/baseinfo/resourceId')
  static async getSatisfyResourceId(ctx: Context, next: Next) {
    const query = ctx.request.query as any;
    const esType = ctx.request.header['ES-Type'] as string;
    const items = await Satisfy.getSatisfyInfoResourceId(esType, query);
    ctx.response.body = {
      data: {
        items
      },
      success: true,
      msg: '获取成功'
    };
    next();
  }
}
