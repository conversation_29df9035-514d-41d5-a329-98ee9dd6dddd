/**
 * @file middleware/OAuth.ts
 * @desc API Key鉴权验证，适用于OpenAPI
 */

import {type Context, type Next} from 'koa';
import {Op} from 'sequelize';
import {ApiKey, AppManage} from '../model';
import {ErrorMsg} from '../utils/error';
import TokenGenerator from '../utils/tokenGen';

// 中间件：API Key 鉴权
export default async function OAuth(ctx: Context, next: Next) {
  const authToken = ctx.headers['authorization'];

  if (!authToken || !authToken.startsWith('Bearer ')) {
    ctx.throw(401, ErrorMsg.AuthorizationError);
  }

  const token = authToken.replace(/^Bearer /, '');
  const appId = TokenGenerator.extractPrefix(token);
  const shortToken = TokenGenerator.extractShortToken(token);

  if (!appId) {
    ctx.throw(400, ErrorMsg.InvalidParameter('appId'));
  }

  const app = await AppManage.findOne({
    where: {
      service_id: appId
    }
  });

  if (!app) {
    ctx.throw(404, ErrorMsg.NoSuchApp);
  }

  // 查找指定应用的API Key，且过期时间 >= 当前时间的key
  const apiKey = await ApiKey.findOne({
    where: {
      appId: appId,
      token: shortToken,
      expiredAt: {
        [Op.gte]: new Date()
      }
    }
  });

  if (!apiKey) {
    ctx.throw(403, ErrorMsg.NoSuchApiKey);
  }

  if (!TokenGenerator.validateToken(token, apiKey.hash)) {
    ctx.throw(403, ErrorMsg.AuthenticationError);
  }

  /** 记录当前Key最近使用时间 */
  try {
    apiKey.update({lastUsedAt: new Date()});
    apiKey.save();
  } catch (error) {}

  await next();
}
