import {type Context, type Next} from 'koa';

const originWhiteList = [
  'https://qasandbox.bcetest.baidu.com',
  'https://console.bce.baidu.com',
  'https://localhost.qasandbox.bcetest.baidu.com',
  'http://localhost.pagemaker.baidu-int.com:8089',
  'https://pagemaker.baidu-int.com',
  'http://qasandbox.pagemaker.baidu-int.com',
  'http://yunkun.cloud.baidu-int.com',
  'http://yunkun.cloud.test.baidu-int.com'
];
export default async (ctx: Context, next: Next) => {
  const origin = ctx.request.header.origin as string;
  if (originWhiteList.includes(origin)) {
    ctx.response.set({
      'Access-Control-Allow-Origin': origin,
      'Access-Control-Allow-Methods': 'GET,POST,OPTIONS',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Headers': 'x-isuda-app-locale,x-requested-with'
    });
  }
  await next();
};
