import {type Context, type Next} from 'koa';
import intersection from 'lodash/intersection';
import {
  newEsServiceListPhase1,
  CONSOLE_CLIENT,
  SANDBOX_IAAS_CHANGE_TIME,
  ONLINE_IAAS_CHANGE_TIME,
  SANDBOX_OTHER_CHANGE_TIME,
  ONLINE_OTHER_CHANGE_TIME
} from '../utils/const';

export default async (ctx: Context, next: Next) => {
  const query = ctx.request.query as any;
  const body = ctx.request.body as any;
  const bodyData = (typeof body === 'string' ? JSON.parse(body) : body) as any;
  const newEsServiceIdListPhase1 = newEsServiceListPhase1.map(
    item => item.serviceId
  );

  const serviceId =
    query.serviceId ||
    bodyData.serviceId ||
    bodyData['monitor-base-info']?.serviceId ||
    bodyData['feedback-base-info']?.serviceId;
  const env =
    query.env ||
    bodyData.env ||
    bodyData['monitor-base-info']?.env ||
    bodyData['feedback-base-info']?.env;
  // 批量查询场景
  let serviceIds = bodyData.apps || bodyData.appIds;
  typeof serviceIds === 'string' &&
    (serviceIds = (serviceIds as string).split(','));

  const serviceInfo = newEsServiceListPhase1.find(
    item => item.serviceId === serviceId
  );
  const iaasServiceIdList = intersection(serviceIds, newEsServiceIdListPhase1);
  const otherServiceIdList =
    serviceIds?.filter(
      (item: string) => !newEsServiceIdListPhase1.includes(item)
    ) || [];
  // 当前产品属于一期切换ES产品列表或者批量查询中有部分产品属于一期切换ES产品列表
  const iaasChangeES = serviceInfo || iaasServiceIdList.length > 0;
  // 当前产品不属于一期切换ES产品列表或者批量查询中有部分产品不属于一期切换ES产品列表，则都是二期切换ES
  const otherChangeES =
    (serviceId && !serviceInfo) || otherServiceIdList.length > 0;
  // iaas产品
  // 沙盒 2024-04-16 20:30点后 之后为新es集群服务
  // 线上 2024-04-28 20:00点后 之后为新es集群服务
  // 其他产品
  // 沙盒 2024-08-07 19:00点后 之后为新es集群服务
  // 线上 2024-08-12 00:00点后 之后为新es集群服务
  const iaasChangeTime =
    env === 'consoleOffline'
      ? SANDBOX_IAAS_CHANGE_TIME
      : ONLINE_IAAS_CHANGE_TIME;
  const iaasChangeTimeDate = new Date(iaasChangeTime).getTime();
  const otherChangeTime =
    env === 'consoleOffline'
      ? SANDBOX_OTHER_CHANGE_TIME
      : ONLINE_OTHER_CHANGE_TIME;
  const otherChangeTimeDate = new Date(otherChangeTime).getTime();

  const selectEsFormTimeRange = (
    startTime: number,
    endTime: number,
    headerName: string
  ) => {
    // iaas产品走之前的逻辑
    if (
      iaasChangeES &&
      startTime < iaasChangeTimeDate &&
      endTime > iaasChangeTimeDate
    ) {
      throw new Error(
        `云产品【${
          serviceInfo?.serviceName ||
          (iaasServiceIdList.length > 0 ? iaasServiceIdList : serviceId)
        }】在${iaasChangeTime}进行了集群切换，不可跨集群查看数据，请选择正确的时间范围`
      );
    } else if (
      // 后面其他产品，走新的逻辑
      otherChangeES &&
      startTime < otherChangeTimeDate &&
      endTime > otherChangeTimeDate
    ) {
      throw new Error(
        `云产品【${
          serviceInfo?.serviceName ||
          (otherServiceIdList.length > 0 ? otherServiceIdList : serviceId)
        }】在${otherChangeTime}进行了集群切换，不可跨集群查看数据，请选择正确的时间范围`
      );
    } else {
      ctx.request.header[headerName] = CONSOLE_CLIENT;
    }
  };
  try {
    if (query.startTime && query.endTime) {
      selectEsFormTimeRange(query.startTime, query.endTime, 'ES-Type');
    }
    if (body.startTime && body.endTime) {
      selectEsFormTimeRange(body.startTime, body.endTime, 'ES-Type');
    }
    if (query.compareStartTime && query.compareEndTime) {
      selectEsFormTimeRange(
        query.compareStartTime,
        query.compareEndTime,
        'Compare-Time-ES-Type'
      );
    }
    if (body.compareStartTime && body.compareEndTime) {
      selectEsFormTimeRange(
        body.compareStartTime,
        body.compareEndTime,
        'Compare-Time-ES-Type'
      );
    }
    // 没有时间参数的请求，且是iaas产品，默认是iaas服务
    if (
      !query.startTime &&
      !query.endTime &&
      !body.startTime &&
      !body.endTime &&
      ((iaasChangeES && new Date().getTime() > iaasChangeTimeDate) ||
        (otherChangeES && new Date().getTime() > otherChangeTimeDate))
    ) {
      ctx.request.header['ES-Type'] = CONSOLE_CLIENT;
    }
    await next();
  } catch (error: any) {
    ctx.body = {
      success: false,
      msg: error.message
    };
  }
};
