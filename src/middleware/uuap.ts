import {type Context, type Next} from 'koa';
import crypto from 'crypto';
import url from 'url';
import {v4} from 'uuid';
import qs from 'qs';
import axios from 'axios';
import {UUAP_CONFIG} from '../utils/const';
import {isDev} from '../utils/helper';

const uuapConfig = UUAP_CONFIG;

/**
 *  计算uuap Sign签名
 * @param {Object} payload 请求参数
 * @return {string} 签名字符串
 */
const sha256 = (payload: any) => {
  const beforeSha256 =
    Object.keys(payload)
      .sort((a, b) => a.localeCompare(b))
      .reduce((str, key) => (str += payload[key]), '') + uuapConfig.secretKey;
  return crypto.createHash('sha256').update(beforeSha256).digest('hex');
};

/**
 * HTTPS验证
 * @param ctx   当前请求上下文
 * @param next   当前请求next
 * @param url    请求uuap的url
 * @param postData UUAP请求数据
 * @param callback  uuap验证请求回调
 */
async function validateByHttps(
  ctx: Context,
  next: Next,
  url: string,
  postData: string,
  callback: (ctx: Context, next: Next, uuapRes: any) => void
) {
  const uuapRes = await axios.post(url, postData, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Content-Length': Buffer.byteLength(postData)
    }
  });
  return callback(ctx, next, uuapRes.data);
}

/**
 * 自定义错误类
 * @returns {
 *  msg: 错误提示信息,
 *  code: 错误码
 *  （302 uuap认证失败，重定向)
 * }
 */
export class RedirectException extends Error {
  code: number;
  constructor(msg: string) {
    super(msg);
    this.code = 302;
  }
}

/**
 * 重定向到UUAP认证
 * @param ctx
 * @param next
 */
async function redirectUuapAuthenticate(ctx: any) {
  const service = ctx.header.referer;
  const {protocol, hostname, port, loginMethod} = uuapConfig;
  const redirecturl = url.format({
    protocol: protocol,
    host: hostname,
    port: port,
    pathname: loginMethod,
    query: {
      service,
      appKey: uuapConfig.appKey,
      version: uuapConfig.version
    }
  });
  throw new RedirectException(redirecturl);
}

/**
 * 验证session服务回调
 * @param ctx 当前请求上下文
 * @param next
 * @param uuapRes uuap验证请求response
 */
async function sessionResultValidate(ctx: Context, next: Next, uuapRes: any) {
  try {
    if (200 === uuapRes.code) {
      // 返回状态码200， 认为用户已经登录，将用户信息写入
      ctx.userInfo = uuapRes.result;
      return next();
    } else if (204 === uuapRes.code) {
      // 返回状态码204，认为用户登录信息无效，需重定向到UUAP重新认证
      return redirectUuapAuthenticate(ctx);
    }
    throw Error(
      `uuap session validate failed, errorMessage: ${uuapRes.result}`
    );
  } catch (error) {
    ctx.throw(401, 'no permission allowed');
  }
}

/**
 * 验证ticket回调
 * @param ctx       当前请求上下文
 * @param next
 * @param uuapRes   uuap验证请求response
 */
function decryptSToken(ctx: any, next: Next, uuapRes: any) {
  if (uuapRes.code !== 200) {
    throw Error('invoke uuap sTokenDecrypt interface failed, errorMessage');
  }
  // 返回状态码200，将获得的SToken植入cookie中，并去除ticket参数，自身重定向
  const sToken = uuapRes.result;
  ctx.cookies.set(uuapConfig.sTokenName, sToken, {
    domain: 'yunqiao.baidu-int.com',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    path: '/'
  });
  ctx.cookies.set(uuapConfig.sTokenName, sToken, {
    domain: 'yunqiao-sandbox.baidu-int.com',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    path: '/'
  });
  // 发起自身的重定向，以便去除ticket参数
  const params = url.parse(ctx.header.referer as string, true);
  const query = params.query;
  delete query.ticket;
  const redirecturl = url.format({
    pathname: `${params.protocol}//${params.host}${params.pathname}`,
    query: query
  });
  throw new RedirectException(redirecturl);
}

export default async (ctx: Context, next: Next) => {
  const pToken = ctx.cookies.get(uuapConfig.pTokenName);
  const sToken = ctx.cookies.get(uuapConfig.sTokenName);
  if (
    !(
      ctx.header.referer?.startsWith('https://yunqiao') ||
      ctx.header.referer?.startsWith('http://yunqiao')
    )
  ) {
    // 开发环境，模拟UUAP验证成功
    if (isDev()) {
      ctx.userInfo = {
        username: 'acg-iaas-fe',
        email: '<EMAIL>',
        name: 'IaaS前端研发团队'
      };
    }

    await next();
    return;
  }
  const params = ctx.header.referer
    ? url.parse(ctx.header.referer as string, true)
    : {query: {ticket: null}};
  const query = params.query;
  const {ticket} = query;
  if (ticket) {
    // sToken置换
    const data = {
      appKey: uuapConfig.appKey,
      encryptedSToken: ticket,
      sRandom: v4(),
      timestamp: Math.round(Date.now() / 1000)
    };
    const sign = sha256(data);
    const postData = qs.stringify({
      ...data,
      sign
    });
    const {protocol, hostname, port, sTokenDecryptMethod} = uuapConfig;
    const url = `${protocol}//${hostname}:${port}${sTokenDecryptMethod}`;
    return validateByHttps(ctx, next, url, postData, decryptSToken);
  } else if (pToken && sToken) {
    // session登录校验
    const data = {
      appKey: uuapConfig.appKey,
      pToken,
      sRandom: v4(),
      sToken,
      timestamp: Math.round(Date.now() / 1000)
    };
    const sign = sha256(data);
    const postData = qs.stringify({
      ...data,
      sign
    });
    const {protocol, hostname, port, sessionValidateUrl} = uuapConfig;
    const url = `${protocol}//${hostname}:${port}${sessionValidateUrl}`;
    // 调用session服务验证用户是否正常登录
    return validateByHttps(ctx, next, url, postData, sessionResultValidate);
  }
  return redirectUuapAuthenticate(ctx);
};
