import {type Context, type Next} from 'koa';

export default async (ctx: Context, next: Next) => {
  const query = ctx.request.query as any;
  const body = ctx.request.body as any;
  if (query.timeRange) {
    const startTime = parseInt(query.timeRange.split(',')[0]);
    const endTime =
      parseInt(query.timeRange.split(',')[1]) || new Date().getTime();
    (ctx.query.startTime as any) =
      startTime.toString().length === 10 ? startTime * 1000 : startTime;
    (ctx.query.endTime as any) =
      endTime.toString().length === 10 ? endTime * 1000 : endTime;
  }
  if (body.timeRange) {
    const startTime = parseInt(body.timeRange.split(',')[0]);
    const endTime =
      parseInt(body.timeRange.split(',')[1]) || new Date().getTime();
    delete body.timeRange;
    (body.startTime as any) =
      startTime.toString().length === 10 ? startTime * 1000 : startTime;
    (body.endTime as any) =
      endTime.toString().length === 10 ? endTime * 1000 : endTime;
  }
  // 兼容两种时间比较的场景
  if (body.compareTime) {
    const startTime = parseInt(body.compareTime.split(',')[0]);
    const endTime =
      parseInt(body.compareTime.split(',')[1]) || new Date().getTime();
    delete body.compareTime;
    (body.compareStartTime as any) =
      startTime.toString().length === 10 ? startTime * 1000 : startTime;
    (body.compareEndTime as any) =
      endTime.toString().length === 10 ? endTime * 1000 : endTime;
  }
  if (query.compareTime) {
    const startTime = parseInt(query.compareTime.split(',')[0]);
    const endTime =
      parseInt(query.compareTime.split(',')[1]) || new Date().getTime();
    (ctx.query.compareStartTime as any) =
      startTime.toString().length === 10 ? startTime * 1000 : startTime;
    (ctx.query.compareEndTime as any) =
      endTime.toString().length === 10 ? endTime * 1000 : endTime;
  }
  await next();
};
