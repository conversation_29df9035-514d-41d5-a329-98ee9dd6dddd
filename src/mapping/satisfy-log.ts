export const mapping = {
  properties: {
    '@timestamp': {
      type: 'date'
    },
    'baseInfo': {
      properties: {
        _href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        browser: {
          type: 'keyword',
          ignore_above: 256
        },
        browserHeight: {
          type: 'long',
          index: false
        },
        browserVersion: {
          type: 'keyword',
          index: false
        },
        browserWidth: {
          type: 'long',
          index: false
        },
        env: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        feedbackType: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        locale: {
          type: 'keyword',
          ignore_above: 256
        },
        platform: {
          type: 'keyword',
          ignore_above: 256
        },
        serviceId: {
          type: 'keyword',
          ignore_above: 256
        },
        signature: {
          type: 'keyword'
        },
        userId: {
          type: 'keyword',
          ignore_above: 256
        },
        subUserId: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'reason': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'reasonTags': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'score': {
      type: 'long'
    },
    'timeStamp': {
      type: 'date'
    },
    'title': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'resourceId': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    }
  }
};
