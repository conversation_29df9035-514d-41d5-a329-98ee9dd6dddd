export const mapping = {
  properties: {
    '@timestamp': {
      type: 'date'
    },
    'baseInfo': {
      properties: {
        _href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 1000
            }
          }
        },
        browser: {
          type: 'keyword',
          ignore_above: 256
        },
        browserHeight: {
          type: 'long',
          index: false
        },
        browserVersion: {
          type: 'keyword',
          index: false
        },
        browserWidth: {
          type: 'long',
          index: false
        },
        env: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 1000
            }
          }
        },
        locale: {
          type: 'keyword',
          ignore_above: 256
        },
        platform: {
          type: 'keyword',
          ignore_above: 256
        },
        serviceId: {
          type: 'keyword',
          ignore_above: 256
        },
        signature: {
          type: 'keyword'
        },
        userId: {
          type: 'keyword',
          ignore_above: 256
        },
        subUserId: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'enterTime': {
      type: 'text',
      index: false
    },
    'error': {
      properties: {
        message: {
          type: 'keyword',
          ignore_above: 1000
        },
        stack: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        }
      }
    },
    'errorId': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'from': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 1000
        }
      }
    },
    'href': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 1000
        }
      }
    },
    'leaveTime': {
      type: 'text',
      index: false
    },
    'stayTime': {
      type: 'long'
    },
    'timeStamp': {
      type: 'date'
    },
    'to': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 1000
        }
      }
    },
    'type': {
      type: 'keyword',
      ignore_above: 256
    },
    'uuid': {
      type: 'keyword',
      ignore_above: 256
    }
  }
};
