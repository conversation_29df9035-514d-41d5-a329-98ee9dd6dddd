export const mapping = {
  properties: {
    '@timestamp': {
      type: 'date'
    },
    'baseInfo': {
      properties: {
        _href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 1000
            }
          }
        },
        browser: {
          type: 'keyword',
          ignore_above: 256
        },
        browserHeight: {
          type: 'long',
          index: false
        },
        browserVersion: {
          type: 'keyword',
          index: false
        },
        browserWidth: {
          type: 'long',
          index: false
        },
        env: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 1000
            }
          }
        },
        locale: {
          type: 'keyword',
          ignore_above: 256
        },
        platform: {
          type: 'keyword',
          ignore_above: 256
        },
        serviceId: {
          type: 'keyword',
          ignore_above: 256
        },
        signature: {
          type: 'keyword'
        },
        userId: {
          type: 'keyword',
          ignore_above: 256
        },
        subUserId: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'completeUrl': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 1000
        }
      }
    },
    'isError': {
      type: 'boolean'
    },
    'jumpTicket': {
      type: 'boolean'
    },
    'method': {
      type: 'keyword',
      ignore_above: 256
    },
    'performance': {
      type: 'text',
      index: false
    },
    'protocol': {
      type: 'keyword',
      index: false
    },
    'regUrl': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 1000
        }
      }
    },
    'requestData': {
      type: 'text',
      index: false
    },
    'requestHeader': {
      properties: {
        'X-Bce-Request-id': {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        'X-Region': {
          type: 'keyword',
          ignore_above: 256
        },
        'region': {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'requestHeader_text': {
      type: 'text',
      index: false
    },
    'responseData': {
      type: 'text',
      index: false
    },
    'responseError': {
      properties: {
        code: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        collect: {
          type: 'boolean'
        },
        message: {
          properties: {
            description: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256
                }
              }
            },
            global: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256
                }
              }
            },
            noSession: {
              type: 'text',
              fields: {
                keyword: {
                  type: 'keyword',
                  ignore_above: 256
                }
              }
            }
          }
        }
      }
    },
    'responseHeader': {
      properties: {
        cacheControl: {
          type: 'keyword',
          ignore_above: 256
        },
        compressionPolicy: {
          type: 'keyword',
          ignore_above: 256
        },
        corsPolicy: {
          type: 'keyword',
          ignore_above: 256
        },
        etag: {
          type: 'keyword',
          ignore_above: 256
        },
        expires: {
          type: 'keyword',
          ignore_above: 256
        },
        lastModify: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        requestId: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'responseSize': {
      type: 'long',
      index: false
    },
    'responseTime': {
      type: 'float',
      index: false
    },
    'status': {
      type: 'long'
    },
    'timeStamp': {
      type: 'date'
    },
    'type': {
      type: 'text',
      index: false
    },
    'url': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 1000
        }
      }
    }
  }
};
