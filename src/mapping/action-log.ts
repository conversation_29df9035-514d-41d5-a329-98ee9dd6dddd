export const mapping = {
  properties: {
    '@timestamp': {
      type: 'date'
    },
    'baseInfo': {
      properties: {
        _href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        browser: {
          type: 'keyword',
          index: false
        },
        browserHeight: {
          type: 'long',
          index: false
        },
        browserVersion: {
          type: 'keyword',
          index: false
        },
        browserWidth: {
          type: 'long',
          index: false
        },
        env: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        locale: {
          type: 'keyword',
          ignore_above: 256
        },
        platform: {
          type: 'keyword',
          index: false
        },
        serviceId: {
          type: 'keyword',
          ignore_above: 256
        },
        signature: {
          type: 'keyword'
        },
        userId: {
          type: 'keyword',
          ignore_above: 256
        },
        subUserId: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'eventData': {
      type: 'keyword',
      index: false
    },
    'eventType': {
      type: 'keyword',
      ignore_above: 1000
    },
    'innerText': {
      type: 'keyword',
      index: false
    },
    'timeStamp': {
      type: 'date'
    },
    'trackId': {
      type: 'text',
      fields: {
        keyword: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'trackName': {
      type: 'keyword',
      index: false
    },
    'type': {
      type: 'keyword',
      index: false
    },
    'xpath': {
      type: 'keyword',
      index: false
    }
  }
};
