export const mapping = {
  properties: {
    '@timestamp': {
      type: 'date'
    },
    'baseInfo': {
      properties: {
        _href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 1000
            }
          }
        },
        browser: {
          type: 'keyword',
          ignore_above: 256
        },
        browserHeight: {
          type: 'long',
          index: false
        },
        browserVersion: {
          type: 'keyword',
          index: false
        },
        browserWidth: {
          type: 'long',
          index: false
        },
        env: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 256
            }
          }
        },
        href: {
          type: 'text',
          fields: {
            keyword: {
              type: 'keyword',
              ignore_above: 1000
            }
          }
        },
        locale: {
          type: 'keyword',
          ignore_above: 256
        },
        platform: {
          type: 'keyword',
          ignore_above: 256
        },
        serviceId: {
          type: 'keyword',
          ignore_above: 256
        },
        signature: {
          type: 'keyword'
        },
        userId: {
          type: 'keyword',
          ignore_above: 256
        },
        subUserId: {
          type: 'keyword',
          ignore_above: 256
        }
      }
    },
    'fcp': {
      type: 'double'
    },
    'fid': {
      type: 'double'
    },
    'fmp': {
      type: 'double'
    },
    'fmpIdentifier': {
      type: 'keyword',
      index: false
    },
    'fp': {
      type: 'double'
    },
    'lcp': {
      type: 'double'
    },
    'onload': {
      type: 'double'
    },
    'resourceTiming': {
      type: 'text',
      index: false
    },
    'timeStamp': {
      type: 'date'
    },
    'type': {
      type: 'keyword',
      index: false
    }
  }
};
