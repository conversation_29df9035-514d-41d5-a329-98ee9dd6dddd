/**
 * BOS client methods
 *
 * @file bos-client.js
 * <AUTHOR>
 */
import {Service} from 'typedi';
import BosClient from '@baiducloud/sdk/src/bos_client';

const env = process.env;

interface bosClient {}

const bosConfig: bosConfig = {
  endpoint: env.BOS_ENDPOINT || 'https://bj-bos-sandbox.baidu-int.com',
  ak: env.BOS_AK || 'ALTAKi5fGgfcjkcS9c776PBEj1',
  sk: env.BOS_SK || '1e59b71373d34245b58a8a254ea8f054'
};

interface bosConfig {
  ak: string;
  sk: string;
  endpoint: string;
}
@Service()
export default class Client implements bosClient {
  bosClient: any;
  config: bosConfig;
  constructor(options?: any) {
    if (options) {
      this.config = Object.assign(bosConfig, options);
    } else {
      this.config = bosConfig;
    }
    this.bosClient = new BosClient({
      credentials: {
        ak: this.config.ak,
        sk: this.config.sk
      },
      endpoint: this.config.endpoint
    });
  }

  putObject(...args: any[]) {
    return this.bosClient.putObject(...args);
  }

  putObjectFromString(...args: any[]) {
    return this.bosClient.putObjectFromString(...args);
  }

  putObjectFromFile(...args: any[]) {
    return this.bosClient.putObjectFromFile(...args);
  }

  deleteObject(bucketName: any, objectKey: any) {
    return this.bosClient.deleteObject(bucketName, objectKey);
  }

  listObjects(bucketName: any, ...args: any[]) {
    return this.bosClient.listObjects(bucketName, ...args);
  }

  listAllObjects() {
    return this.bosClient.listObjects();
  }

  copyObject(...args: any[]) {
    return this.bosClient.copyObject(...args);
  }

  getObject(...args: any[]) {
    return this.bosClient.getObject(...args).then((r: {body: any}) => r.body);
  }
}
