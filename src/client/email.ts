import nodemailer from 'nodemailer';
import {Service} from 'typedi';

@Service()
export class MailClient {
  private transporter: any;
  constructor() {
    // 创建邮件发送器
    this.transporter = nodemailer.createTransport({
      host: 'smtp.msgrelay.baidu-int.com',
      port: 80,
      secure: false, // SSL
      // 不用，以token为主
      auth: {
        user: 'xxx',
        pass: 'xxx'
      }
    });
  }
  async sendMail(mailOptions: any): Promise<any> {
    try {
      const from = '<EMAIL>';
      const result = await this.transporter.sendMail({
        ...mailOptions,
        from
      });
      return result;
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
}
