/**
 * @file Elasticsearch 服务调用
 */

import {Client, ClientOptions} from '@elastic/elasticsearch';
import {DEFAULT_CLIENT, CONSOLE_CLIENT} from '../utils';

export interface ESConfig extends ClientOptions {
  isCluster: boolean;
  auth?: {
    username: string;
    password: string;
  };
}

class DefaultClient {
  protected readonly client?: Client;
  constructor() {
    const setting: ESConfig = {
      node: 'http://10.27.218.69:8903',
      isCluster: false,
      auth: {
        username: 'elastic',
        password: 'bceconsole'
      }
    };
    this.client = new Client(setting);
  }

  avaiable() {
    return this.client!!;
  }

  refresh(index: string) {
    return this.client?.indices.refresh({
      index
    });
  }

  // 写入一个数据。
  store(index: string, data: Object, type: string = '_doc') {
    return this.client?.index({
      index,
      type,
      body: {
        ...data
      }
    });
  }

  getMapping(index: string) {
    return this?.client?.indices?.getMapping({index});
  }
  getSetting(index: string) {
    return this?.client?.indices?.getSettings({index});
  }
  updateByQuery(options: any) {
    return this.client?.updateByQuery(options);
  }

  // 搜索数据
  search(
    index: string,
    options: {
      size?: number;
      from?: number;
      body: any;
    },
    type: string = '_doc'
  ) {
    return this.client?.search({
      ...options,
      index,
      type
    });
  }

  // 清空数据
  clear(index: string) {
    return this?.client?.indices?.exists({index}).then(res => {
      if (res?.statusCode === 200) {
        return this.client?.indices.delete({index});
      }
    });
  }
}

// 继承原有ESClient方法，只是更新了node地址
class ConsoleClient extends DefaultClient {
  protected readonly client?: Client;
  constructor() {
    super();
    const setting: ESConfig = {
      node: 'http://10.180.112.70',
      isCluster: false
      // auth: {
      //   username: env.ISUDA_ES_USERNAME!,
      //   password: env.ISUDA_ES_PASSWORD!
      // }
    };
    this.client = new Client(setting);
  }
}

// 两个client并存，ConsoleClient指向新集群地址，ESClient指向旧集群地址
const ESClient: any = {
  [DEFAULT_CLIENT]: new DefaultClient(),
  [CONSOLE_CLIENT]: new ConsoleClient()
};
export default ESClient;
