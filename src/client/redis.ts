/**
 * @file Redis 服务调用
 */

import {Redis} from 'ioredis';
import {Service} from 'typedi';
import {generateRedisClusterHosts} from '../utils/getServiceApi';

const env = process.env;
let redis_port = Number(process.env.REDIS_PORT);
if (isNaN(redis_port)) {
  redis_port = 6379;
}

const redisConfig: redisConfig = {
  port: redis_port,
  host: env.REDIS_HOST || '127.0.0.1',
  username: env.REDIS_USER,
  password: env.REDIS_PWD
};

interface redisConfig {
  host: string;
  port: number;
  password?: string;
  origin?: string; // 类似 127.0.0.1:8080,*********:8081 这样的字符串
  [propName: string]: any;
}

interface redisClient {
  set(key: string, value: any, ex?: number): Promise<string | null | undefined>;
  get(key: string): Promise<string | null | undefined>;
  delete(key: string): Promise<number | null | undefined>;
}

const clientCreate = (config: redisConfig, cb: Function) => {
  let redis: any = null;
  if (config.isCluster) {
    redis = new Redis.Cluster(generateRedisClusterHosts(config.origin!), {
      redisOptions: {
        password: config.password,
        keyPrefix: config.keyPrefix,
        db: config.db,
        enableReadyCheck: config.enablerReadyCheck
      }
    });
  } else if (config.isSentinel) {
    redis = new Redis({
      sentinels: generateRedisClusterHosts(config.origin!),
      name: 'mymaster',
      password: config.password,
      sentinelPassword: config.sentinelPassword
    });
  } else {
    redis = new Redis(config);
  }
  redis.on('connect', () => cb(null, redis));
  redis.on('error', (err: any) => cb(err, null));
};

const redisConnect = (options: redisConfig) => {
  return new Promise((resolve, reject) => {
    clientCreate(options, (err: any, conn: Redis) => {
      if (err) {
        reject(err);
      }
      resolve(conn);
    });
  });
};

@Service()
export default class RedisClient implements redisClient {
  redis: Redis | null;
  config: redisConfig;
  constructor(options?: any) {
    this.redis = null;
    if (options) {
      this.config = Object.assign(redisConfig, options);
    } else {
      this.config = redisConfig;
    }
  }

  connect(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.redis) {
        resolve(true);
      } else {
        redisConnect(this.config)
          .then((res: any) => {
            this.redis = res;
            resolve(true);
          })
          .catch(err => {
            reject(err);
          });
      }
    });
  }

  /**
   * 将键值对存储到 Redis 中
   *
   * @param key 键名
   * @param value 键值
   * @param expireInSeconds 可选，过期时间（秒）
   * @returns 返回 Redis 存储结果
   */
  async set(key: string, value: any, expireInSeconds?: number) {
    const _key: string = typeof key !== 'string' ? JSON.stringify(key) : key;
    const _value: string =
      typeof value !== 'string' ? JSON.stringify(value) : value;
    try {
      const isRedis = await this.connect();
      if (isRedis) {
        let res;
        if (expireInSeconds) {
          res = await this.redis?.set(_key, _value, 'EX', expireInSeconds);
        } else {
          res = await this.redis?.set(_key, _value);
        }
        return res;
      }
      return null;
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  async get(key: string) {
    const _key: string = typeof key !== 'string' ? JSON.stringify(key) : key;
    try {
      const isRedis = await this.connect();
      if (isRedis) {
        const res = await this.redis?.get(_key);
        return res;
      }
      return null;
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  async delete(key: string) {
    const _key: string = typeof key !== 'string' ? JSON.stringify(key) : key;
    try {
      const isRedis = await this.connect();
      if (isRedis) {
        const res = await this.redis?.del(_key);
        return res;
      }
      return null;
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  async exist(key: string) {
    const _key: string = typeof key !== 'string' ? JSON.stringify(key) : key;
    try {
      const isRedis = await this.connect();
      if (isRedis) {
        const res = await this.redis?.exists(_key);
        return res;
      }
      return null;
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  // 加锁
  async lock(key: string, duration: number = 60000) {
    const _key = typeof key !== 'string' ? JSON.stringify(key) : key;
    const lockValue = Date.now() + duration + 1;
    try {
      const isRedis = await this.connect();
      if (isRedis) {
        const result = await this.redis?.set(
          _key,
          lockValue,
          'NX' as any,
          'PX' as any,
          duration as any
        );
        if (result === 'OK') {
          // 成功获取锁
          return Promise.resolve(lockValue);
        }
      }
      return Promise.reject(new Error('Failed to acquire lock'));
    } catch (err) {
      return Promise.reject(new Error('Error acquiring lock'));
    }
  }
  // 释放锁
  async releaseLock(key: string) {
    const _key = typeof key !== 'string' ? JSON.stringify(key) : key;
    try {
      const value = await this.get(_key);
      if (value) {
        await this.delete(_key);
        return Promise.resolve(true);
      }
      return Promise.reject(new Error('lock is not exist to release'));
    } catch (err) {
      return Promise.reject(new Error('Error release lock'));
    }
  }
}
