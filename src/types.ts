type Method = 'get' | 'post' | 'delete' | 'put' | 'head' | 'options';
type Handler = () => void;
interface Route {
  url: string;
  method: Method;
  handler: Handler;
  middleware?: any;
}
type LogType =
  | 'monitor-api'
  | 'monitor-page-error'
  | 'monitor-base-info'
  | 'monitor-pv'
  | 'monitor-event'
  | 'monitor-page-error-cancel'
  | 'monitor-page-performance';

type FeedbackType = 'feedback-base-info' | 'feedback-satisfy-info';

interface performanceData {
  fetchStart?: number;
  responseEnd?: number;
}
interface ApiInfo {
  regUrl: string;
  completeUrl: string;
  url: string;
  method: string; // 请求类型
  status: number; // 响应状态码
  apiTime: number; // 请求时间
  protocol: string; // 请求协议
  requestData: any; // 请求数据
  responseData:
    | string
    | {
        success: any;
        message?: {
          global?: string;
          noSession?: string;
          redirect?: string;
          description?: string;
          detail?: any;
          filed?: any;
        };
        requestId?: string;
        code?: string;
        data?: any;
        result?: any;
        page?: any;
      }; // 响应数据
  responseError: {
    collect?: boolean;
    message?: {
      global?: string;
      noSession?: string;
      description?: string;
    };
    code?: string;
  }; // 处理后的响应数据
  responseSize: number; // 响应数据大小
  responseTime: number; // 响应时间
  cachePolicy: {
    // 缓存策略
    expires: string | undefined;
    cacheControl: string | undefined;
    etag: string | undefined;
    lastModify: string | undefined;
  };
  compressionPolicy: string; // 压缩策略
  corsPolicy: string; // 跨域策略
  performance: any;
  isError?: boolean;
  errorInfo?: {errorStatus: string; [key: string]: string};
  timeStamp: number;
  requestHeader: any;
  responseHeader: any;
}

interface PageError {
  type: string;
  uuid: string;
  error: any;
  errorId: string;
  snapshotEvent: any;
  timeStamp: number;
}
interface PageErrorCancel {
  type: string;
  uuid: string;
  error: any;
  snapshotEvent: any;
  timeStamp: number;
}
interface BaseInfo {
  host: string; // 域名
  path: string; // 路径
  userId?: string; // 用户id
  subUserId?: string; //子用户id
  locale: string; // 语言
  env: string; // 环境
  version?: string; // 前端版本
  browser: string; // 浏览器类型
  browserVersion: string; // 浏览器版本
  browserHeight: number; // 浏览器高度
  browserWidth: number; // 浏览器宽度
  platform: string; // 操作系统
  href: string; // 链接
  _href: string; // 没有query的链接
  serviceId: string; // 服务名
  signature: string; // 签名
  authToken: string; // 鉴权token
  uuid: string; // 随机ID
}
interface PV {
  from: string;
  href: string;
  type: string;
  timeStamp: number;
}
interface EventAction {
  eventData: {
    requestId?: string;
    timeStamp?: number;
  };
  timeStamp?: number;
  eventType: string;
  type: string;
}
interface AppPerformance {
  fp: number; // 首次像素点渲染时长
  fcp: number; // 首次渲染时长
  fmp: number; // 首次有效元素渲染时长
  fmpIdentifier: 'main'; // 首次有效渲染元素标识
  lcp: number; // 最大元素渲染时长
  onload: number; // 页面加载完成时长
  resourceTiming: // 资源列表
  {
    duration: number; // 资源加载持续时长
    initiatorType: 'script'; // 资源类型
    name: string; // 资源名称
    startTime: number; // 资源开始加载时刻
  }[];
  timeStamp: number; // 时间戳
  type: 'page-performance'; // 类型
}
interface AppPerformanceFid {
  fid: number; // 首次输入延迟
  lcp: number; // 最大元素渲染时长
  timeStamp: number; // 时间戳
  type: 'page-performance-fid'; // 类型
}

type Order = 'desc' | 'asc';
type OrderBy = 'timestamp';
interface PageQuery {
  pageNo: string;
  pageSize: string;
  timeRange?: string;
  keyword?: string; // 查询全部内容
  href?: string; // 查询异常页面
  browser?: string; // 浏览器
  device?: string; // 设备
  userId?: string; // 用户id
  order?: Order;
  orderBy?: OrderBy;
  isError?: boolean;
  [propsName: string]: any;
}

interface PageDetailQuery {
  id: string;
  [propsName: string]: any;
}

interface SatisfyInfo {
  title: string;
  score: number;
  reason?: string;
  resourceId?: string;
  reasonTags?: Array<string> | string;
}

interface AddActionItem extends EditActionItem {
  serviceId: string;
  actionType?: string;
  url?: string;
  env: string;
  trackId?: string;
  creator: string;
}

interface EditActionItem extends DeleteActionItem {
  name: string;
  description?: string;
  updater: string;
}

interface DeleteActionItem {
  id: string;
}

interface GetActionListItem {
  serviceId: string;
  env: string;
  actionType?: string;
  pageNo?: number | string;
  pageSize?: number | string;
  order?: string;
  orderBy?: string;
  keyword?: string;
  keywordType?: string;
}

interface GetListItem extends GetActionListItem {}

interface AddChartItem {
  serviceId: string;
  env: string;
  name: string;
  type: string;
  description?: string;
  actions: actionItem[];
  creator: string;
}

interface actionItem {
  actionId: string;
  color: string;
}

interface DeleteChartItem {
  id: string;
}

interface GetChartDetailQuery {
  id: string;
}

interface detailChartQuery {
  id: string;
}

interface EditChartItem extends AddChartItem {
  id: string;
  updater: string;
}

interface GetChartListQuery {
  serviceId: string;
  env: string;
  pageNo?: number | string;
  pageSize?: number | string;
  order?: string;
  orderBy?: string;
  keyword?: string;
  keywordType?: string;
}

interface previewChartBody {
  serviceId: string;
  env: string;
  startTime?: string;
  endTime?: string;
  id?: string;
  type?: string;
  actions?: actionItem[];
}

interface previewListChartQuery {
  serviceId: string;
  env: string;
  startTime?: string;
  endTime?: string;
  pageNo?: number | string;
  pageSize?: number | string;
  order?: string;
  orderBy?: string;
  keyword?: string;
  keywordType?: string;
}

interface ExemptionRule {
  id: number;
  serviceId: string;
  env: string;
  href?: string;
  path: string;
  code?: string;
  message?: string;
  reason?: string;
  createdAt?: Date;
  createdBy?: string;
  deletedAt?: Date;
  deletedBy?: string;
}
interface AddUrlMappingItem {
  serviceId: string;
  env: string;
  creator: string;
  items: Array<{
    url: string;
    name: string;
    description?: string;
    tags?: string[];
  }>;
}

interface EditUrlMappingItem extends DeleteUrlMappingItem {
  updater: string;
  url: string;
  name: string;
  tags?: string[];
  description?: string;
}

interface DeleteUrlMappingItem {
  id: string;
  serviceId: string;
  env: string;
  url?: string;
}

interface GetUrlMappingListItem {
  serviceId: string;
  env: string;
  pageNo?: number | string;
  pageSize?: number | string;
  order?: string;
  orderBy?: string;
  keyword?: string;
  keywordType?: string;
  tags?: string[];
}

/**
 * 新增应用
 */
interface AddAppItem {
  env: string;
  serviceId: string;
  tags?: string[];
  serviceName?: string;
  description?: string;
  creator?: string;
  updater?: string;
}

/**
 * 列举应用
 */
interface GetAppListItem {
  env: string;
  tags: string[];
  pageNo?: number | string;
  pageSize?: number | string;
  serviceName?: string;
}

/**
 * 新增映射关系
 */
interface AddTagMappingItem {
  env: string;
  service_id: string;
  tag_id: string;
  creator?: string;
}

interface UUAPUserInfo {
  username: string;
  name: string;
  email: string;
}

interface EditServiceContentSchema {
  serviceId: string;
  env: string;
  schema: string;
  content?: string;
  creator?: string;
  updater?: string;
}

interface GetServiceContent {
  env: string;
  serviceId: string;
}

interface EditServiceContentData {
  serviceId: string;
  env: string;
  content: string;
  updater: string;
  updated?: boolean;
}

interface PublishServiceContent {
  serviceId: string;
  env: string;
  updater: string;
}

interface ExtraCreationAttributes {
  user: UUAPUserInfo;
}
interface ServiceAlarmSetting {
  toid: number[];
  errorTimeGap: number;
  errorAlarmNumber: number;
}
type LogInfo = {
  [propName in LogType]:
    | ApiInfo[]
    | PV
    | PageError
    | EventAction
    | BaseInfo
    | AppPerformance
    | AppPerformanceFid;
};
declare module 'bce-sdk-js';
declare module '@baiducloud/sdk/src/bos_client';
