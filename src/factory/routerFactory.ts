/**
 * @file 注册controller、路由
 */

import type Koa from 'koa';
import Router from 'koa-router';
import 'reflect-metadata';

const __RouterCache: Object[] = [];
export const ControllerKey = 'ControllerKey';
export const RouterKey = 'RouterKey';

export class RouterFactory {
  /**
   * 注册控制器
   */
  static registerController(kclass: Object, apiPrefix: string = '') {
    Reflect.defineMetadata(ControllerKey, apiPrefix, kclass);
    __RouterCache.push(kclass);
    return kclass;
  }

  /**
   * 注册路由
   *
   */
  static registerRouter(
    method: Method = 'get',
    url: string,
    kclass: Object,
    handler: Handler,
    middleware?: Koa.Middleware
  ) {
    const routers: Route[] = Reflect.getOwnMetadata(RouterKey, kclass) || [];
    routers.push({
      url,
      method,
      handler,
      middleware
    });
    Reflect.defineMetadata(RouterKey, routers, kclass);
    return handler;
  }

  /**
   * 装载所有注册的路由到koa
   *
   * @param {Object} app koa实例
   */
  static resolve(app: Koa) {
    __RouterCache.forEach((controller: Object) => {
      const prefix = Reflect.getOwnMetadata(ControllerKey, controller) || [];
      const router = new Router({prefix: prefix as string});
      const routers = Reflect.getOwnMetadata(RouterKey, controller) || [];
      if (!routers || !routers.length) {
        return;
      }

      routers.forEach((route: Route) => {
        if (route.middleware != null) {
          router[route.method](route.url, route.middleware, route.handler);
        } else {
          router[route.method](route.url, route.handler);
        }
      });

      app.use(router.routes()).use(router.allowedMethods());
    });
  }
}
