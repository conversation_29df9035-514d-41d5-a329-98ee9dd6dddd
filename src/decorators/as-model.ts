import {Model as DBModel} from 'sequelize';
import {db, new_db} from '../utils';

export const PageMakerModel = (name: string) => (target: any) => {
  const pm_model = class extends DBModel {
    static fields: any;
  };
  pm_model.init(target.fields, {
    sequelize: db,
    tableName: name,
    freezeTableName: true,
    modelName: name,
    underscored: true,
    createdAt: false,
    updatedAt: false
  });
  return pm_model;
};

export const MonitorModel = (name: string) => (target: any) => {
  const monitor_model = class extends DBModel {
    static fields: any;
  };
  monitor_model.init(target.fields, {
    sequelize: new_db,
    tableName: name,
    freezeTableName: true,
    modelName: name,
    underscored: true,
    createdAt: false,
    updatedAt: false
  });
  return monitor_model;
};
