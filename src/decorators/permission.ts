import {Context} from 'koa';
import {AuthManage} from '../service';

// 定义权限类型
type Permission = string | string[];


// 权限装饰器工厂
export function RequiresPermission(permission: Permission) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (
      ctx: Context,
      next: () => Promise<void>
    ) {
      try {
        const request = ctx.request as any;
        const currentServiceId =
          request.query?.serviceId || request.body?.serviceId;
        // 从 ctx 中获取用户名和权限
        const username = ctx.userInfo.username;

        // 验证用户是否存在
        if (!username) {
          ctx.status = 401;
          ctx.body = {message: '未认证'};
          return;
        }

        const userPermissions = await AuthManage.queryUserRoleAndResource(
          username
        );
        const userAuthData = userPermissions.data;
        const auth: any = {};
        for (const key in userAuthData) {
          const serviceId = key.split('_')[0];
          if (currentServiceId === serviceId) {
            userAuthData[key].forEach((item: any) => {
              auth[item] = true;
            });
          }
        }
        const requiredPermissions = Array.isArray(permission)
          ? permission
          : [permission];
        const hasPermission = requiredPermissions.every(perm => auth[perm]);
        if (!hasPermission) {
          // ctx.status = 400;
          ctx.response.body = {
            success: false,
            msg: '权限不足',
            required: requiredPermissions
          };
          return;
        }

        // 权限验证通过，继续执行原方法
        return await originalMethod.apply(this, [ctx, next]);
      } catch (error) {
        ctx.status = 500;
        ctx.body = {message: '服务器内部错误'};
      }
    };
  };
}
