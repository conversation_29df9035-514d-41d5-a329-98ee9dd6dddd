/**
 * @file decorators/upload-file.ts
 * @desc 文件上传参数装饰器
 */

import fs from 'node:fs';
import path from 'node:path';
import multer from '@koa/multer';
import tmp from 'tmp';
import {isDev} from '../utils/helper';

// 默认支持的最大文件体积
const MaxSize = 15 * 1024 * 1024;
// 自定义上传路径和文件名称
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // 创建临时文件夹存放上传文件
    tmp.dir({}, (err, name, cleanupCb) => {
      if (err) {
        throw err;
      }

      if (isDev()) {
        console.log('临时文件夹创建成功', name);
      }

      cb(null, name);
    });
  },
  filename: function (req, file, cb) {
    // 加一个时间戳避免并发重复
    cb(
      null,
      `${file.fieldname}-${Date.now()}${path.extname(file.originalname)}`
    );
  }
});

export const upload = multer({
  storage: storage,
  limits: {
    fieldSize: MaxSize,
    fileSize: MaxSize
  },
  fileFilter: (req, file, cb) => {
    cb(
      null,
      file.mimetype === 'application/json' || file.mimetype === 'text/plain'
    );
  }
});

// 参数装饰器，暂时用不到
// export function FileUpload(
//   fileName: string,
//   maxSize = MaxSize
// ): ParameterDecorator {
//   return function (target, propertyKey, parameterIndex) {
//     if (!propertyKey) {
//       throw new Error(`@FileUpload with invalid propertyKey`);
//     }

//     const existingParams: any[] = Reflect.getOwnMetadata('fileParams', target, propertyKey) || [];

//     existingParams.push({index: parameterIndex, name: fileName});

//     Reflect.defineMetadata('fileParams', existingParams, target, propertyKey);
//   };
// }
