/**
 * @file 路由装饰器
 */

import {RouterFactory} from '../factory';
import type Koa from 'koa';

const registerRouter =
  (url: string, methodName: Method, middleware?: Koa.Middleware) =>
  (target: Object, propertyKey: string, descriptor: any) => {
    RouterFactory.registerRouter(methodName, url, target, descriptor.value, middleware);
    return descriptor;
  };

export const Put = (url: string) => registerRouter(url, 'put');

export const Get = (url: string) => registerRouter(url, 'get');

export const Delete = (url: string) => registerRouter(url, 'delete');

export const Post = (url: string) => registerRouter(url, 'post');

export const PostUpload = (url: string, middleware: Koa.Middleware) => registerRouter(url, 'post', middleware);
