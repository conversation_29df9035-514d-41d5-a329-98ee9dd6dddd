{"appList": [{"serviceId": "bcc", "api调用量": "3,148,273", "api报错量": "1,791", "api报错率": "0.0569%", "serviceName": "云服务器"}, {"serviceId": "bos", "api调用量": "2,106,221", "api报错量": "3,005", "api报错率": "0.1427%", "serviceName": "对象存储"}, {"serviceId": "rapidfs", "api调用量": "0", "api报错量": "0", "api报错率": "0%", "serviceName": "数据湖存储加速工具"}, {"serviceId": "cce", "api调用量": "713,075", "api报错量": "3,281", "api报错率": "0.4601%", "serviceName": "容器引擎"}, {"serviceId": "cdn", "api调用量": "569,859", "api报错量": "997", "api报错率": "0.1750%", "serviceName": "内容分发网络"}, {"serviceId": "network", "api调用量": "325,520", "api报错量": "205", "api报错率": "0.0630%", "serviceName": "私有网络"}, {"serviceId": "bcm", "api调用量": "301,423", "api报错量": "634", "api报错率": "0.2103%", "serviceName": "云监控"}, {"serviceId": "bcd", "api调用量": "284,573", "api报错量": "1,230", "api报错率": "0.4322%", "serviceName": "域名服务"}, {"serviceId": "tms", "api调用量": "280,763", "api报错量": "761", "api报错率": "0.2710%", "serviceName": "商标知产服务"}, {"serviceId": "rds", "api调用量": "268,156", "api报错量": "854", "api报错率": "0.3185%", "serviceName": "云数据库RDS"}, {"serviceId": "blb", "api调用量": "234,219", "api报错量": "158", "api报错率": "0.0675%", "serviceName": "负载均衡"}, {"serviceId": "eip", "api调用量": "152,375", "api报错量": "77", "api报错率": "0.0505%", "serviceName": "弹性公网IP"}, {"serviceId": "dns", "api调用量": "148,592", "api报错量": "89", "api报错率": "0.0599%", "serviceName": "智能云解析"}, {"serviceId": "scs", "api调用量": "139,119", "api报错量": "243", "api报错率": "0.1747%", "serviceName": "云数据库Redis"}, {"serviceId": "smsv3", "api调用量": "114,277", "api报错量": "130", "api报错率": "0.1138%", "serviceName": "简单消息服务"}, {"serviceId": "lcps", "api调用量": "77,049", "api报错量": "274", "api报错率": "0.3556%", "serviceName": "智能直播平台"}, {"serviceId": "ls", "api调用量": "68,072", "api报错量": "185", "api报错率": "0.2718%", "serviceName": "轻量应用服务器"}, {"serviceId": "mct", "api调用量": "58,827", "api报错量": "71", "api报错率": "0.1207%", "serviceName": "音视频处理"}, {"serviceId": "bls", "api调用量": "56,973", "api报错量": "377", "api报错率": "0.6617%", "serviceName": "日志服务"}, {"serviceId": "dts", "api调用量": "46,988", "api报错量": "57", "api报错率": "0.1213%", "serviceName": "数据传输服务"}, {"serviceId": "evs", "api调用量": "45,695", "api报错量": "118", "api报错率": "0.2582%", "serviceName": "智能视联网平台"}, {"serviceId": "gaiadb", "api调用量": "36,631", "api报错量": "123", "api报错率": "0.3358%", "serviceName": "云原生数据库GaiaDB"}, {"serviceId": "mongodb", "api调用量": "31,309", "api报错量": "110", "api报错率": "0.3513%", "serviceName": "云数据库DocDB for MongoDB"}, {"serviceId": "ccr", "api调用量": "29,613", "api报错量": "97", "api报错率": "0.3276%", "serviceName": "容器镜像服务"}, {"serviceId": "apigw", "api调用量": "20,616", "api报错量": "593", "api报错率": "2.8764%", "serviceName": "API网关"}, {"serviceId": "pfs", "api调用量": "15,337", "api报错量": "43", "api报错率": "0.2804%", "serviceName": "并行文件存储PFS"}, {"serviceId": "cprom", "api调用量": "13,880", "api报错量": "37", "api报错率": "0.2666%", "serviceName": "Prometheus监控服务"}, {"serviceId": "cfs", "api调用量": "13,750", "api报错量": "35", "api报错率": "0.2545%", "serviceName": "文件存储"}, {"serviceId": "lss", "api调用量": "13,429", "api报错量": "47", "api报错率": "0.3500%", "serviceName": "音视频直播"}, {"serviceId": "oos", "api调用量": "13,246", "api报错量": "171", "api报错率": "1.2910%", "serviceName": "运维编排服务"}, {"serviceId": "videoworks", "api调用量": "11,612", "api报错量": "107", "api报错率": "0.9215%"}, {"serviceId": "bbc", "api调用量": "10,912", "api报错量": "26", "api报错率": "0.2383%", "serviceName": "弹性裸金属服务器"}, {"serviceId": "as", "api调用量": "9,817", "api报错量": "9", "api报错率": "0.0917%", "serviceName": "弹性伸缩"}, {"serviceId": "cdn_aboad", "api调用量": "6,192", "api报错量": "39", "api报错率": "0.6298%", "serviceName": "海外CDN"}, {"serviceId": "csn", "api调用量": "6,008", "api报错量": "2", "api报错率": "0.0333%", "serviceName": "云智能网"}, {"serviceId": "cbs", "api调用量": "5,415", "api报错量": "14", "api报错率": "0.2585%", "serviceName": "工商财税服务"}, {"serviceId": "cfw", "api调用量": "5,233", "api报错量": "80", "api报错率": "1.5288%", "serviceName": "云防火墙"}, {"serviceId": "bts", "api调用量": "4,376", "api报错量": "13", "api报错率": "0.2971%", "serviceName": "云数据库TableStorage"}, {"serviceId": "rtc", "api调用量": "4,153", "api报错量": "17", "api报错率": "0.4093%", "serviceName": "实时音视频"}, {"serviceId": "cloudflow", "api调用量": "3,930", "api报错量": "46", "api报错率": "1.1705%", "serviceName": "数据流转平台"}, {"serviceId": "bci", "api调用量": "3,223", "api报错量": "31", "api报错率": "0.9618%", "serviceName": "容器实例"}, {"serviceId": "hbase", "api调用量": "1,383", "api报错量": "16", "api报错率": "1.1569%", "serviceName": "云数据库HBase"}, {"serviceId": "ddc", "api调用量": "1,049", "api报错量": "17", "api报错率": "1.6206%", "serviceName": "云数据库DDS"}, {"serviceId": "bvc", "api调用量": "1,032", "api报错量": "25", "api报错率": "2.4225%"}, {"serviceId": "bsg", "api调用量": "588", "api报错量": "3", "api报错率": "0.5102%", "serviceName": "存储网关"}, {"serviceId": "cos", "api调用量": "246", "api报错量": "0", "api报错率": "0%", "serviceName": "云编排服务"}, {"serviceId": "comp", "api调用量": "156", "api报错量": "0", "api报错率": "0%", "serviceName": "云资源运营管理系统"}, {"serviceId": "ecs", "api调用量": "706,913", "api报错量": "118", "api报错率": "0.0167%", "serviceName": "边缘服务器ECS"}, {"serviceId": "kafka", "api调用量": "188,654", "api报错量": "338", "api报错率": "0.1792%", "serviceName": "消息服务 for Kafka"}, {"serviceId": "lssv2", "api调用量": "185,120", "api报错量": "123", "api报错率": "0.0664%"}, {"serviceId": "edap", "api调用量": "148,812", "api报错量": "304", "api报错率": "0.2043%", "serviceName": "EasyDAP数据湖管理与分析平台"}, {"serviceId": "cas", "api调用量": "126,636", "api报错量": "189", "api报错率": "0.1492%", "serviceName": "SSL证书"}, {"serviceId": "smart-term", "api调用量": "96,104", "api报错量": "225", "api报错率": "0.2341%", "serviceName": "远程连接smart-term"}, {"serviceId": "bec", "api调用量": "58,745", "api报错量": "291", "api报错率": "0.4954%", "serviceName": "边缘计算节点BEC"}, {"serviceId": "bmr", "api调用量": "25,988", "api报错量": "106", "api报错率": "0.4079%", "serviceName": "MapReduce"}, {"serviceId": "dbsc", "api调用量": "16,136", "api报错量": "399", "api报错率": "2.4727%", "serviceName": "数据库智能驾驶舱 DBSC"}, {"serviceId": "chpc", "api调用量": "5,111", "api报错量": "66", "api报错率": "1.2913%", "serviceName": "云高性能计算平台"}, {"serviceId": "bch", "api调用量": "3,494", "api报错量": "155", "api报错率": "4.4362%", "serviceName": "云虚拟主机"}, {"serviceId": "suda", "api调用量": "2,256", "api报错量": "19", "api报错率": "0.8422%"}, {"serviceId": "aicp", "api调用量": "1,318", "api报错量": "24", "api报错率": "1.8209%"}, {"serviceId": "ms", "api调用量": "924", "api报错量": "5", "api报错率": "0.5411%"}, {"serviceId": "bnd_e", "api调用量": "60", "api报错量": "3", "api报错率": "5.0000%"}, {"serviceId": "smart_wan", "api调用量": "6", "api报错量": "0", "api报错率": "0%", "serviceName": "智能网络接入服务"}, {"serviceId": "palo", "api调用量": "6", "api报错量": "0", "api报错率": "0%", "serviceName": "<PERSON><PERSON>"}, {"serviceId": "bes", "api调用量": "6", "api报错量": "0", "api报错率": "0%", "serviceName": "Elasticsearch"}, {"serviceId": "aihc", "api调用量": "6", "api报错量": "0", "api报错率": "0%", "serviceName": "百舸"}, {"serviceId": "assistant", "serviceName": "云助手"}]}