/**
 * 服务入口
 */

/**
 * 用于本地开发调试localhost数据库使用，在项目根目录创建`.env`文件，文件中写入相关的环境变量即可
 */
if (process.env.NODE_ENV === 'development') {
  require('dotenv').config();
}

import Koa from 'koa';
import http from 'http';
import https from 'https';
import bodyParser from 'koa-bodyparser';

import './model';
import './controllers';

import getCredentials from './utils/getCredentials';
import {RouterFactory} from './factory';
import {cors, timeFormat, selectClient, uuap, OAuth} from './middleware';
import {startCronJob, removeLogJob, sendEmail} from './cron';
import {isProd, isSandbox, isDevOrSandbox} from './utils';

process.on('uncaughtException', err => {
  console.log(err.stack);
});

process.on('beforeExit', code => {
  console.log('Exit with code', code);
});
const app = new Koa();
const credentials = getCredentials();
app.use(
  bodyParser({
    enableTypes: ['json', 'form', 'text'],
    jsonLimit: '15mb',
    textLimit: '15mb'
  })
);
app.use(cors);
app.use(timeFormat);
app.use(selectClient);
app.use(async (ctx, next) => {
  /** OpenAPI走OAuth鉴权方式 */
  if (ctx.path.startsWith('/testing/v1/import')) {
    await OAuth(ctx, next);
  } else {
    await uuap(ctx, next);
  }
});

// 沙盒环境删除日志的定时任务需要启动
if (isSandbox()) {
  removeLogJob();
}
// 生成环境删除日志的定时任务需要启动
if (isProd()) {
  // startCronJob();
  removeLogJob();
  // 本地和沙盒不要放开发邮件的定时任务，可能会真的发送邮件给相关用户
  // 在本地和沙盒环境调试的时候，要注释发邮件执行代码
  sendEmail();
}

RouterFactory.resolve(app);

http.createServer(app.callback()).listen(8003);

const port = 8004;
https
  .createServer(
    {
      key: credentials.privateKey,
      cert: credentials.certificate
    },
    app.callback()
  )
  .listen(port);

export default app;
