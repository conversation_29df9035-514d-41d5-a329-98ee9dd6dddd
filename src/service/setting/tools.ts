import Container from 'typedi';
import {ESClient, redisClient} from '../../client';
import {API_INDEX, DEFAULT_CLIENT} from '../../utils';
import {getRegUrl} from '../../utils/getServiceApi';

export class Tools {
  // 根据服务，获取对应的Client类
  static getClient(esType: string) {
    if (esType) {
      return ESClient[esType];
    }
    return ESClient[DEFAULT_CLIENT];
  }
  /*
    修改url为regUrl
  */
  static async resetUrlToRegUrl(esType: string, body: any) {
    const esClient = this.getClient(esType);
    const handlerArr = async (result: any[]) => {
      const handlerItem = async (item: any) => {
        const _item = item._source;
        const regUrl = await getRegUrl(_item.baseInfo.serviceId, {
          url: _item.url,
          method: _item.method
        });
        return {
          index: item._index,
          type: item._type,
          id: item._id,
          regUrl
        };
      };
      for (let i = 0; i < result.length; i++) {
        const doc = await handlerItem(result[i]);
        await esClient.avaiable().update({
          index: doc.index,
          type: doc.type, // 文档类型
          id: doc.id, // 文档ID
          body: {
            doc: {
              regUrl: doc.regUrl
            }
          }
        });
      }
    };
    const esQuery: any = {
      bool: {
        filter: [],
        should: []
      }
    };

    const {serviceId, env, apis} = body;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }

    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    if (Array.isArray(apis) && apis.length) {
      apis.forEach((api: string) => {
        // ** => .+
        // ${p.xxxxx} => [^/]+
        // * => [^/]+
        const regApi = api
          .replace(/\*\*/g, '.+')
          .replace(/\${p\..+}/g, '[^/]+')
          .replace(/\*/g, '[^/]+');
        esQuery.bool.should.push({
          regexp: {
            'url.keyword': {
              value: regApi
            }
          }
        });
      });
      esQuery.bool.minimum_should_match = 1;
    }

    esClient.avaiable().search(
      {
        index: API_INDEX,
        type: '_doc',
        size: 10000,
        scroll: '2m',
        body: {
          query: esQuery
        }
      },
      async function handlerData(error: any, response: any) {
        const result = response.body.hits.hits;
        if (result && result.length > 0) {
          await handlerArr(result);
          esClient.avaiable().scroll(
            {
              scroll: '2m',
              scroll_id: response.body._scroll_id
            },
            handlerData
          );
        }
      }
    );
  }

  /*
    根据url上挂在的locale参数重刷baseInfo.locale
  */
  static async resetLocale(esType: string, body: any) {
    const esClient = this.getClient(esType);
    const handlerArr = async (result: any[]) => {
      const handlerItem = async (item: any) => {
        const _item = item._source;
        const query = _item.completeUrl.split('?')[1];
        let locale = 'zh-CN';
        if (query && query.indexOf('locale') > -1) {
          const arr = query.split('&');
          const obj: any = {};
          arr.forEach((item: any) => {
            obj[item.split('=')[0]] = item.split('=')[1];
          });
          locale = obj.locale?.includes('en') ? 'en-US' : 'zh-CN';
        }
        return {
          index: item._index,
          type: item._type,
          id: item._id,
          locale
        };
      };
      for (let i = 0; i < result.length; i++) {
        const doc = await handlerItem(result[i]);
        await esClient.avaiable().update({
          index: doc.index,
          type: doc.type, // 文档类型
          id: doc.id, // 文档ID
          body: {
            doc: {
              baseInfo: {
                locale: doc.locale
              }
            }
          }
        });
      }
    };
    const esQuery: any = {
      bool: {
        filter: []
      }
    };

    const {serviceId, env} = body;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }

    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    esClient.avaiable().search(
      {
        index: API_INDEX,
        type: '_doc',
        size: 10000,
        scroll: '2m',
        body: {
          query: esQuery
        }
      },
      async function handlerData(error: any, response: any) {
        const result = response.body.hits.hits;
        if (result && result.length > 0) {
          await handlerArr(result);
          esClient.avaiable().scroll(
            {
              scroll: '2m',
              scroll_id: response.body._scroll_id
            },
            handlerData
          );
        }
      }
    );
  }

  /*
    导出全量数据为excel
  */
  static async exportExcel(body: any) {}

  static async checkRedis(body: any) {
    let data;
    const {
      action,
      keyword,
      payload
    }: {
      action: 'get' | 'set' | 'delete' | 'exist';
      keyword: string;
      payload: any;
    } = body;

    if (
      !['get', 'set', 'delete', 'exist'].includes(action) ||
      typeof keyword !== 'string'
    ) {
      data = '请求参数不合法';
    } else {
      try {
        const redisIns = Container.get(redisClient);

        data = await redisIns[action](keyword, payload);
      } catch (error) {
        data = 'redis 连接失败';
      }
    }
    return data;
  }
}
