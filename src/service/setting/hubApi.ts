import {Context} from 'koa';
import axios from 'axios';
import {HubInterface as HubInterfaceDto} from '../../model';
import {and, Op, QueryTypes, Sequelize, where} from 'sequelize';
import {new_db} from '../../utils';
import moment from 'moment';
import {refreshApiRedis} from '../../utils/getServiceApi';

const prefix = {
  sandbox: 'http://10.229.62.17:8143/',
  online: ''
};

interface HubService {
  interfaceCount: number;
  serviceName: string;
  serviceType: string;
  uuid: string;
}

interface HubInterface {
  interfaceType: string;
  method: string;
  name: string;
  path: string;
  prefix: string;
  prefixUuid: string;
  protocol: string;
  queryParams: string;
  uuid: string;
}

export class HubApi {
  static async getServices(ctx: Context) {
    const res: {
      data: {result: HubService[]};
    } = await axios.request({
      url: 'v1/console_hub/service',
      baseURL: prefix.sandbox,
      timeout: 3000,
      headers: {
        Cookie: ctx.header.cookie
      },
      params: {
        pageNo: 1,
        pageSize: 9999
      }
    });
    return res.data.result || [];
  }

  static async getApiWithWildcard(serviceUuid: string, ctx: Context) {
    const res: {
      data: {
        result: HubInterface[];
      };
    } = await axios.request({
      url: '/v1/console_hub/interface',
      baseURL: prefix.sandbox,
      timeout: 3000,
      headers: {
        Cookie: ctx.header.cookie
      },
      params: {
        serviceUuid,
        pageNo: 1,
        pageSize: 9999,
        keyword: '*',
        keywordType: 'path'
      }
    });
    return res.data.result || [];
  }

  static async syncHubInterface(ctx: any) {
    let items = [];
    console.log('查询services');
    const params: any = ctx.request.body;
    const services = params.services || [];
    console.log('services:', services);
    for (const service of services) {
      console.log('service 遍历开始：', service.serviceName);
      const res = await this.syncInterfaceByService(service, ctx);
      items.push(res);
    }
    return items;
  }

  static async syncInterfaceByService(service: HubService, ctx: Context) {
    console.log('interfaces 开始处理：', service);
    const interfaces = await this.getApiWithWildcard(service.uuid, ctx);

    const uuids = interfaces.map(el => el.uuid);
    const t = await new_db.transaction();
    let success = false;
    let result = undefined;
    try {
      await HubInterfaceDto.destroy({
        where: {
          uuid: {
            [Op.in]: uuids
          }
        }
      });

      const apis = interfaces.map(el => ({
        ...el,
        interface_type: el.interfaceType,
        interface_name: el.interfaceType,
        prefix_uuid: el.prefixUuid,
        query_params: el.queryParams,
        service_name: service.serviceName,
        source: 'hub'
      }));

      const prefixs = apis.map(el => el.prefix);

      result = await HubInterfaceDto.bulkCreate(apis);
      await refreshApiRedis(Array.from(new Set(prefixs)));
      await t.commit();
      success = true;
    } catch (error) {
      console.error(error);
      await t.rollback();
    }
    console.log('interfaces 处理结束：', service);

    return {
      success,
      serviceName: service.serviceName,
      result
    };
  }

  static async getInterfaces(params: {
    serviceName: string;
    pageNo: number;
    pageSize: number;
    path: string;
    prefix: string;
    method: string;
    source: string;
  }) {
    let andWhere: any = [];
    if (params.serviceName) {
      andWhere.push({
        ['service_name']: {
          [Op.like]: params.serviceName
        }
      });
    }
    if (params.path) {
      andWhere.push({
        ['path']: {
          [Op.like]: params.path
        }
      });
    }
    if (params.prefix) {
      andWhere.push({
        ['prefix']: {
          [Op.like]: params.prefix
        }
      });
    }
    if (params.method) {
      andWhere.push({
        ['method']: {
          [Op.like]: params.method
        }
      });
    }
    if (params.source) {
      andWhere.push({
        ['source']: {
          [Op.like]: params.source
        }
      });
    }

    return await HubInterfaceDto.findAndCountAll({
      where: {
        [Op.and]: andWhere
      },
      limit: parseInt(params.pageSize as any) || 10,
      offset: parseInt(((params.pageNo - 1) * params.pageSize) as any) || 0
    });
  }

  static async createServices(ctx: Context) {
    let success = false;
    let message = '';
    const params: any = {
      prefix: ctx.prefix,
      service_name: ctx.service,
      method: ctx.method,
      path: ctx.path,
      source: 'custom'
    };
    try {
      const t = await new_db.transaction();
      const item: any = await HubInterfaceDto.create(params);
      await t.commit();

      if (item) {
        await refreshApiRedis([item.prefix]);
      }
      success = true;
      message = '创建成功';
    } catch (error) {
      (message = '创建失败，请重试'), (success = false);
    }
    return {
      success,
      message
    };
  }

  static async editServices(ctx: Context) {
    let success = false;
    let message = '';
    const params: any = {
      prefix: ctx.prefix,
      service_name: ctx.service,
      method: ctx.method,
      path: ctx.path,
      source: 'custom'
    };
    try {
      const apiService: any = await HubInterfaceDto.findOne({
        where: {
          id: ctx.id
        }
      });
      if (!apiService) {
        return {
          message: '该规则不存在，请重试',
          success: false
        };
      }
      apiService.update(params);

      await apiService.save();
      await refreshApiRedis([apiService.prefix]);
      success = true;
      message = '更新成功';
    } catch (error) {
      (message = '更新失败，请重试'), (success = false);
    }
    return {
      success,
      message
    };
  }

  static async deleteServices(ctx: Context) {
    let success = false;
    let message = '';
    const {id} = ctx;
    if (!id) {
      return {
        msg: '删除缺少参数，请检查',
        success: false
      };
    }

    try {
      const item: any = await HubInterfaceDto.findOne({
        where: {
          id: id
        }
      });
      if (item) {
        await item?.destroy();
        await refreshApiRedis([item.prefix]);
      }
      (message = '删除成功'), (success = true);
    } catch (error) {
      (message = '删除失败，请重试'), (success = false);
    }
    return {
      success,
      message
    };
  }

  static async getAllPrefix() {
    try {
      const result: {prefix: string}[] = await new_db.query(
        'select distinct  prefix from hub_interfaces ',
        {type: QueryTypes.SELECT}
      );
      return result.map(el => el?.prefix).filter(el => !!el);
    } catch (error) {
      console.log(error);
    }
  }

  static async getInterfacesByPrefixs(prefixs: string[]) {
    try {
      const result: any[] = await HubInterfaceDto.findAll({
        where: {
          prefix: {
            [Op.in]: prefixs
          }
        }
      });

      let map: any = {};
      prefixs.forEach(prefix => {
        map[prefix] = result
          .filter((el: any) => el.prefix === prefix)
          .map(el => {
            return {
              url: el.prefix + el.path,
              method: el.method
            };
          });
      });

      return map;
    } catch (error) {
      console.log(error);
    }
  }
}
