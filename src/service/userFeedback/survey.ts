/**
 * @file service/survey.ts
 * @desc 调查问卷相关公共逻辑
 */

import uniq from 'lodash/uniq';
import omit from 'lodash/omit';
import isEmpty from 'lodash/isEmpty';
import orderBy from 'lodash/orderBy';
import isInteger from 'lodash/isInteger';
import inRange from 'lodash/inRange';
import isPlainObject from 'lodash/isPlainObject';
import {hashId} from '../../utils/hashId';
import {ErrorMsg} from '../../utils/error';
import {xssFilter, getJSONFromText, omitNil} from '../../utils/helper';
import {
  AppManage,
  SurveyModel,
  QuestionModel,
  SubmissionModel
} from '../../model';

import type {InferAttributes} from 'sequelize';
import type {
  SubmissionAnswerItemFE,
  SubmissionAnswerItem
} from '../../model/survey/submission';
import type {QuestionType} from '../../model/survey/question';

type RequiredKeys<T> = {
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];

export interface OptionItem {
  label: string;
  value: string;
  description?: string;
}

export interface BaseSurveyReq {
  id: string;
}

export interface CreateSurveyReq {
  appId: string;
  title: string;
  description?: string;
  images?: string[];
  questions?: Omit<CreateQuestionReq, 'surveyId'>[];
}

export interface UpdateSurveyReq
  extends Partial<Omit<CreateSurveyReq, 'appId'>> {
  id: string;
}

export interface GetSurveyListReq {
  appId: string;
  page?: number;
  perPage?: number;
}

export interface CreateQuestionReq {
  /** 问题序号 */
  order: number;
  /** 调查问卷的ID */
  surveyId: string;
  /** 问题的类型 */
  type: QuestionType;
  /** 问题内容 */
  title: string;
  /** 问题图片 */
  images?: string[];
  /** 问题信息描述 */
  description?: string;
  /** 问题选项（仅"multiple_choice"题目支持） */
  options?: OptionItem[];
  /** 是否为必填问题 */
  required?: boolean;
  /** 最大输入字数（仅"essay"题目支持） */
  maxLength?: number;
  /** 是否为子问题 */
  isSubQuestion?: boolean;
}

export interface UpdateQuestionReq
  extends Partial<Omit<CreateQuestionReq, 'surveyId'>> {
  id: string;
}

export type BulkInsertQuestionReq = Omit<CreateQuestionReq, 'surveyId'>;

export interface BulkUpdateQuestionReq {
  surveyId: string;
  toInsert: BulkInsertQuestionReq[];
  /** question id 集合 */
  toDelete: string[];
  toUpdate: UpdateQuestionReq[];
}

export interface GetQuestionListReq {
  surveyId: string;
  page?: number;
  perPage?: number;
}

export interface GetSurveyContentReq {
  appId: string;
  id: string;
}

export interface SubmitSurveyReq {
  appId: string;
  id: string;
  context: {
    isSubUser: boolean;
    /** 账户ID */
    accountId: string;
    /** 用户ID */
    userId?: string;
    /** 用户姓名 */
    name?: string;
    /** 用户联系方式 */
    phone?: string;
    browser?: string;
    browserVersion?: string;
    platform?: string;
    resolution?: string;
    browserResolution?: string;
    language?: string;
    referer?: string;
  };
  answers: SubmissionAnswerItemFE[];
}

export interface DownloadStatisticsQuery {
  /** 应用ID */
  appId: string;
  /** 调查问卷ID */
  id: string;
}

export interface ValidateSurveyReq {
  appId: string;
  id: string;
  accountId: string;
  userId?: string;
}

export interface AnswerItem2FE {
  id: string;
  type: QuestionType;
  title: string;
  isSubQuestion: boolean;
  required: boolean;
  content: string;
}

function isNotNil<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

const langMapping: Record<string, string> = Object.freeze({
  'en': '英语',
  'en-us': '英语',
  'en-gb': '英语',
  'zh': '中文',
  'zh-cn': '中文 - 简体',
  'zh-rw': '中文 - 繁体',
  'zh-hk': '中文 - 繁体',
  'ja': '日语',
  'ko': '韩语',
  'fr': '法语',
  'fr-fr': '法语',
  'fr-ca': '法语',
  'de': '德语',
  'de-de': '德语',
  'es': '西班牙语',
  'es-es': '西班牙语',
  'es-mx': '西班牙语',
  'it': '意大利语',
  'it-it': '意大利语',
  'ru': '俄语',
  'ru-ru': '俄语',
  'pt': '葡萄牙语',
  'pt-pt': '葡萄牙语',
  'pt-br': '葡萄牙语',
  'ar': '阿拉伯语',
  'ar-sa': '阿拉伯语',
  'hi': '印地语',
  'hi-in': '印地语',
  'th': '泰语',
  'th-th': '泰语',
  'vi': '越南语',
  'vi-vn': '越南语'
});

export class SurveyService {
  static defaultUsername = 'acg-iaas-fe';

  static requiredSurveyCreationParams: (keyof CreateSurveyReq)[] = [
    'appId',
    'title'
  ];

  static requiredQuestionCreationParams: RequiredKeys<CreateQuestionReq>[] = [
    'order',
    'type',
    'title'
  ];

  static validateCreationSurveyParams(
    payload: CreateSurveyReq
  ): [number, string] {
    if (isEmpty(payload)) {
      return [400, ErrorMsg.InvalidEmptyParameter];
    }

    if (!isPlainObject(payload)) {
      return [400, ErrorMsg.InvalidNonObjectParameter];
    }

    // 创建必填参数验证
    for (const key of SurveyService.requiredSurveyCreationParams) {
      if (payload[key] == null) {
        return [400, ErrorMsg.InvalidParameter(key)];
      }
    }

    // title不能为空
    if (payload.hasOwnProperty('title') && typeof payload.title !== 'string') {
      return [400, ErrorMsg.InvalidParameterPattern('title')];
    }

    if (payload.hasOwnProperty('images')) {
      // images只能为空或者数组
      if (!Array.isArray(payload.images) && payload.images != null) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }

      // images数组中元素必须为非空字符串
      if (
        Array.isArray(payload.images) &&
        payload.images?.some(i => typeof i !== 'string' || !i)
      ) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }
    }

    if (
      payload.hasOwnProperty('description') &&
      typeof payload.description !== 'string' &&
      payload.description != null
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('description')];
    }

    return [200, ''];
  }

  static validateUpdateSurveyParams(
    payload: UpdateSurveyReq
  ): [number, string] {
    if (isEmpty(payload)) {
      return [400, ErrorMsg.InvalidEmptyParameter];
    }

    if (!isPlainObject(payload)) {
      return [400, ErrorMsg.InvalidNonObjectParameter];
    }

    // title必填，不能为空
    if (
      payload.hasOwnProperty('title') &&
      (typeof payload.title !== 'string' || !payload.title)
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('title')];
    }

    if (payload.hasOwnProperty('images')) {
      // images只能为空或者数组
      if (!Array.isArray(payload.images) && payload.images != null) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }

      // images数组中元素必须为非空字符串
      if (
        Array.isArray(payload.images) &&
        payload.images?.some(i => typeof i !== 'string' || !i)
      ) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }
    }

    if (
      payload.hasOwnProperty('description') &&
      typeof payload.description !== 'string' &&
      payload.description != null
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('description')];
    }

    return [200, ''];
  }

  static validateCreationQuestionParams(
    payload: Omit<CreateQuestionReq, 'surveyId'> & {surveyId?: string}
  ): [number, string] {
    if (isEmpty(payload)) {
      return [400, ErrorMsg.InvalidEmptyParameter];
    }

    if (!isPlainObject(payload)) {
      return [400, ErrorMsg.InvalidNonObjectParameter];
    }

    // 必填验证
    for (const key of SurveyService.requiredQuestionCreationParams) {
      if (payload[key] == null) {
        return [400, ErrorMsg.InvalidParameter(key)];
      }
    }

    /** 限制一下order大小，问卷题目应该不会超过1000个 */
    if (!isInteger(payload.order) || !inRange(payload.order, 0, 1001)) {
      return [400, ErrorMsg.InvalidParameterPattern('order')];
    }

    // 题目类型验证
    if (!['rating', 'multiple_choice', 'essay'].includes(payload.type)) {
      return [400, ErrorMsg.InvalidParameterPattern('type')];
    }

    // 问题
    if (typeof payload.title !== 'string') {
      return [400, ErrorMsg.InvalidParameterPattern('title')];
    }

    // 问题图片
    if (payload.hasOwnProperty('images')) {
      // images只能为空或者数组
      if (!Array.isArray(payload.images) && payload.images != null) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }

      // images数组中元素必须为非空字符串
      if (
        Array.isArray(payload.images) &&
        payload.images?.some(i => typeof i !== 'string' || !i)
      ) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }
    }

    // 问题描述
    if (
      payload.hasOwnProperty('description') &&
      typeof payload.description !== 'string'
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('description')];
    }

    // 是否必填
    if (
      payload.hasOwnProperty('required') &&
      typeof payload.required !== 'boolean'
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('required')];
    }

    // 是否为子问题
    if (
      payload.hasOwnProperty('isSubQuestion') &&
      typeof payload.isSubQuestion !== 'boolean'
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('required')];
    }

    // 多选题-选项参数验证
    if (
      payload.type === 'multiple_choice' &&
      (!payload.options ||
        !Array.isArray(payload.options) ||
        payload.options.length < 1 ||
        payload.options.length !== payload.options.filter(Boolean).length ||
        payload.options.some(i => !i.label || !i.value))
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('options')];
    }

    // 问答题目-字数限制
    if (
      payload.type === 'essay' &&
      payload.maxLength != null &&
      (!isInteger(payload.maxLength) || !inRange(payload.maxLength, 1, 5000))
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('maxLength')];
    }

    return [200, ''];
  }

  static validateUpdateQuestionParams(
    payload: UpdateQuestionReq
  ): [number, string] {
    if (isEmpty(payload)) {
      return [400, ErrorMsg.InvalidEmptyParameter];
    }

    if (!isPlainObject(payload)) {
      return [400, ErrorMsg.InvalidNonObjectParameter];
    }

    if (!payload.id) {
      return [400, ErrorMsg.InvalidParameterPattern('question.id')];
    }

    // 问题类型
    if (
      payload.type &&
      !['rating', 'multiple_choice', 'essay'].includes(payload.type)
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('type')];
    }

    /** 限制一下order大小，问卷题目应该不会超过1000个 */
    if (
      payload.order != null &&
      (!isInteger(payload.order) || !inRange(payload.order, 0, 1001))
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('order')];
    }

    // 问题
    if (
      payload.hasOwnProperty('title') &&
      (typeof payload.title !== 'string' || !payload.title)
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('title')];
    }

    // 问题图片
    if (payload.hasOwnProperty('images')) {
      // images只能为空或者数组
      if (!Array.isArray(payload.images) && payload.images != null) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }

      // images数组中元素必须为非空字符串
      if (
        Array.isArray(payload.images) &&
        payload.images?.some(i => typeof i !== 'string' || !i)
      ) {
        return [400, ErrorMsg.InvalidParameterPattern('images')];
      }
    }

    // 问题描述
    if (
      payload.hasOwnProperty('description') &&
      typeof payload.description !== 'string'
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('description')];
    }

    // 是否必填
    if (
      payload.hasOwnProperty('required') &&
      typeof payload.required !== 'boolean'
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('required')];
    }

    // 是否为子问题
    if (
      payload.hasOwnProperty('isSubQuestion') &&
      typeof payload.isSubQuestion !== 'boolean'
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('required')];
    }

    if (
      payload.type === 'multiple_choice' &&
      (!payload.options ||
        !Array.isArray(payload.options) ||
        payload.options.length < 1 ||
        payload.options.length !== payload.options.filter(Boolean).length ||
        payload.options.some(i => !i.label || !i.value))
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('options')];
    }

    // 问答题目-字数限制
    if (
      payload.type === 'essay' &&
      payload.maxLength != null &&
      (!isInteger(payload.maxLength) || !inRange(payload.maxLength, 200, 5000))
    ) {
      return [400, ErrorMsg.InvalidParameterPattern('maxLength')];
    }

    return [200, ''];
  }

  /** 校验外键对应的Survey是否存在 */
  static async validateFKSurvey(surveyId: number): Promise<[number, string]> {
    if (!surveyId || typeof surveyId !== 'number') {
      return [400, ErrorMsg.InvalidParameter('surveyId')];
    }

    try {
      const survey = await SurveyModel.findOne({
        where: {
          id: surveyId
        }
      });

      if (!survey) {
        return [404, ErrorMsg.NoSuchSurvey];
      }

      return [200, ''];
    } catch (error) {
      return [500, ErrorMsg.NoSuchSurvey];
    }
  }

  static async validateCreationSubmissionParams(
    payload: SubmitSurveyReq
  ): Promise<[number, string]> {
    if (isEmpty(payload)) {
      return [400, ErrorMsg.InvalidEmptyParameter];
    }

    const appId = payload.appId;
    const surveyId = hashId.decode(payload.id);

    if (!appId) {
      return [400, ErrorMsg.InvalidParameter('appId')];
    }

    const app = await AppManage.findOne({
      where: {
        service_id: appId
      }
    });

    if (!app) {
      return [404, ErrorMsg.NoSuchAppSurvey];
    }

    if (!surveyId) {
      return [400, ErrorMsg.InvalidParameter('id')];
    }

    /** 获取到问卷对应的全量问题 */
    // const survey = await SurveyModel.findOne({where: {id: surveyId, appId}});
    const survey = await SurveyModel.findOne({
      attributes: ['id', 'appId'],
      where: {
        id: surveyId,
        appId
      },
      include: {
        model: QuestionModel,
        as: 'Questions'
      }
    });

    if (!survey) {
      return [400, ErrorMsg.NoSuchSurvey];
    }

    if (!payload?.context || !isPlainObject(payload.context)) {
      return [400, ErrorMsg.InvalidParameter('context')];
    }

    const contextData = payload.context;

    /** 账户ID必填 */
    if (!contextData?.accountId) {
      return [400, ErrorMsg.InvalidParameter('accountId')];
    }

    const isSubUser = !!contextData?.isSubUser;

    /** 子用户的用户ID必填 */
    if (isSubUser && !contextData?.userId) {
      return [400, ErrorMsg.InvalidParameter('userId')];
    }

    /** 非必填字段校验 */
    const optionalKeys = [
      'browser',
      'browserVersion',
      'platform',
      'resolution',
      'browserResolution',
      'language',
      'referer',
      'name',
      'phone'
    ] as unknown as (keyof SubmitSurveyReq['context'])[];
    for (let i = 0; i < optionalKeys.length; i++) {
      const key = optionalKeys[i];
      if (
        contextData.hasOwnProperty(key) &&
        typeof contextData[key] !== 'string'
      ) {
        return [400, ErrorMsg.InvalidParameterPattern(key)];
      }
    }

    /** 答案为数组结构 */
    if (!Array.isArray(payload?.answers)) {
      return [400, ErrorMsg.InvalidParameterPattern('answers')];
    }

    // 每个回答必须指明问题ID
    if (payload.answers.some(item => !item.questionId)) {
      return [400, ErrorMsg.InvalidParameter('answers.questionId')];
    }

    // 问题答案必须是字符串类型或者为空
    if (
      payload.answers.some(
        item => typeof item.content !== 'string' && item.content != null
      )
    ) {
      return [400, ErrorMsg.InvalidParameter('answers.content')];
    }

    /** 问题集合 */
    const questionCollection = (survey?.Questions ?? []).map(i => {
      const question = i.get();

      return {
        id: question.id,
        type: question.type,
        required: !!question.required
      };
    });
    /** 问题映射 */
    const questionMap = questionCollection.reduce<
      Record<
        number,
        {
          id: number;
          type: QuestionType;
          required: boolean;
        }
      >
    >((acc, item) => {
      acc[item.id] = item;
      return acc;
    }, {});

    /** 调查问卷不能为空，如果未绑定任何问题，不允许提交 */
    if (questionCollection.length < 1) {
      return [404, ErrorMsg.NoQuestionsInSurvey];
    }

    /** 必填问题ID集合 */
    const requiredQIds = questionCollection
      .filter(i => i.required)
      .map(i => i.id);
    /** 提交问题ID集合 */
    const decodedQIds = payload.answers.map(i => hashId.decode(i.questionId));

    /** 验证答案列表中的questionId是否合法 */
    if (
      decodedQIds.length < requiredQIds.length ||
      !requiredQIds.every(item => decodedQIds.includes(item))
    ) {
      return [400, ErrorMsg.InCompleteRequiredAnswer];
    }

    /** 验证答案列表中的必填问题的content是否合法 */
    if (
      payload.answers.some(i => {
        const isRequired = questionMap[hashId.decode(i.questionId)].required;

        return isRequired && i.content == null;
      })
    ) {
      return [400, ErrorMsg.InCompleteRequiredAnswer];
    }

    /** 如果是主账户用户，则没有userId，如果是子用户，则accountId和userId都存在 */
    const submission = await SubmissionModel.findOne({
      where: {
        accountId: payload?.context?.accountId,
        userId: isSubUser ? payload?.context?.userId : null,
        surveyId
      }
    });

    /** 重复提交验证 */
    if (submission) {
      return [400, ErrorMsg.DuplicatedSurveySubmission];
    }

    return [200, ''];
  }

  static async validateQuestionAndAnswers(
    answers: SubmissionAnswerItemFE[],
    surveyId: number
  ): Promise<[number, string, QuestionModel[]]> {
    try {
    } catch (error) {}

    let questionModelCollection: QuestionModel[];

    /** 验证questionId外键 & 问题答案合法性 */
    try {
      questionModelCollection = await Promise.all(
        answers.map(async item => {
          const questionId = hashId.decode(item.questionId);
          const quesModel = await QuestionModel.findByPk(questionId);

          if (!quesModel) {
            throw new Error(ErrorMsg.InvalidQuestionId(item.questionId));
          }

          /** 如果为非必填问题，则跳过验证 */
          if (!quesModel.required) {
            return quesModel;
          }

          /** 必填题目校验 */
          if (!item.content || typeof item.content !== 'string') {
            throw new Error(ErrorMsg.InvalidEmptyAnswer);
          }

          /** 评分题验证 */
          if (
            quesModel.type === 'rating' &&
            !/^(1|2|3|4|5)$/.test(item.content)
          ) {
            throw new Error(ErrorMsg.InvalidRatingAnswer);
          }

          /** 多选题验证 */
          if (quesModel.type === 'multiple_choice') {
            const validateOptionsValues =
              (quesModel?.options as OptionItem[])?.map?.(i => i.value) ?? [];
            const answerCollection = item.content.split(',');

            for (let i = 0; i < answerCollection.length; i++) {
              if (!validateOptionsValues.includes(answerCollection[i])) {
                throw new Error(ErrorMsg.InvalidChoiceAnswer);
              }
            }
          }

          /** 简答题验证 */
          if (
            quesModel.type === 'essay' &&
            (typeof item.content !== 'string' || !item.content)
          ) {
            throw new Error(ErrorMsg.InvalidEssayAnswer);
          }

          return quesModel;
        })
      );
      return [200, '', questionModelCollection];
    } catch (error: any) {
      return [400, error.message, []];
    }
  }

  static normalizeCreateQuestion2SQL(
    q: CreateQuestionReq | BulkInsertQuestionReq,
    options: {surveyId: number; createdBy: string}
  ) {
    return {
      order: q.order ?? 1,
      type: q.type,
      title: xssFilter(q.title),
      images: q.images,
      description: xssFilter(q.description ?? ''),
      isSubQuestion: !!q.isSubQuestion,
      required: !!q.required,
      ...(q.options
        ? {
            options: q.options.map(i => ({
              label: xssFilter(i.label),
              value: xssFilter(i.value),
              description: xssFilter(i.description ?? '')
            }))
          }
        : {}),
      maxLength: q.maxLength ?? 1000,
      surveyId: options.surveyId,
      createdBy: options.createdBy
    };
  }

  static normalizeUpdateQuestion2SQL(q: UpdateQuestionReq) {
    return omitNil({
      order: q.order,
      type: q.type,
      title: xssFilter(q.title) ? xssFilter(q.title) : undefined,
      images: q.images,
      description: xssFilter(q.description)
        ? xssFilter(q.description)
        : undefined,
      required: q.required,
      isSubQuestion: q.isSubQuestion,
      options: q.options
        ? q.options.map(i => ({
            label: xssFilter(i.label),
            value: xssFilter(i.value),
            description: xssFilter(i.description)
              ? xssFilter(i.description)
              : undefined
          }))
        : null,
      maxLength: q.maxLength
    });
  }

  static normalizeSurvey2FE(surveyModel: SurveyModel, omitKeys?: string[]) {
    const survey = surveyModel.get();
    const defaultOmitPropsList = ['id', 'Questions'];
    const omitList = uniq([
      ...defaultOmitPropsList,
      ...(Array.isArray(omitKeys) ? omitKeys.filter(Boolean) : [])
    ]);
    /** 按照order排序 */
    const questions = orderBy(
      (Array.isArray(survey?.Questions) ? survey.Questions : []).map(q => {
        return {
          ...SurveyService.normalizeSurveyQuestion2FE(q, {
            withFK: false,
            omitKeys: ['id', 'surveyId', 'createdBy', 'updatedBy']
          })
        };
      }),
      ['order', 'isSubQuestion', 'createdAt', 'updatedAt'],
      ['asc', 'asc', 'asc', 'asc']
    );

    // 初始化序号计数器
    let serialCounter = 0;

    // 添加问题序号，因为子问题不带序号，这里需要重排一下
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];

      if (!question.isSubQuestion) {
        serialCounter++;
      }

      question.serialNumber = serialCounter;
    }

    return {
      ...omit(survey, omitList),
      id: hashId.encode(survey.id),
      images: getJSONFromText(survey.images, []),
      questionCount: questions.length,
      questions: questions
    };
  }

  static normalizeSurveyQuestion2FE(
    questionModel: QuestionModel,
    options?: {withFK?: boolean; omitKeys?: string[]}
  ) {
    const withFK = options?.withFK ?? false;
    const omitList =
      options?.omitKeys && Array.isArray(options.omitKeys)
        ? options.omitKeys
        : ['id', 'surveyId', 'createdBy', 'updatedBy'];
    const question = questionModel.get();

    return {
      ...omit(question, omitList),
      id: hashId.encode(question.id),
      ...(withFK ? {surveyId: hashId.encode(question.surveyId)} : {}),
      ...(question.options
        ? {options: getJSONFromText(question.options, [])}
        : {}),
      images: getJSONFromText(question.images, []),
      required: !!question.required
    };
  }

  static getBrowserInfo(submission: InferAttributes<SubmissionModel>) {
    const userAgent = submission.browser!;
    const parsedInfo: {
      os: 'Windows' | 'MacOS' | 'Linux' | 'iOS' | 'Android' | 'unknown';
      browser:
        | 'Chrome'
        | 'Safari'
        | 'Firefox'
        | 'Edge'
        | 'Internet Explorer'
        | 'unknown';
      version: string | 'unknown';
      device: 'PC' | 'Mobile';
    } = {
      os: 'unknown',
      browser: 'unknown',
      version: 'unknown',
      device: 'PC'
    };

    // 操作系统解析
    if (/Windows NT/i.test(userAgent)) {
      parsedInfo.os = 'Windows';
    } else if (/Macintosh/i.test(userAgent)) {
      parsedInfo.os = 'MacOS';
    } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
      parsedInfo.os = 'iOS';
      parsedInfo.device = 'Mobile';
    } else if (/Android/i.test(userAgent)) {
      parsedInfo.os = 'Android';
      parsedInfo.device = 'Mobile';
    } else if (/Linux/i.test(userAgent)) {
      parsedInfo.os = 'Linux';
    }

    /**
     * 浏览器解析,
     * Chrome和Edge都会包含Chrome信息，所以先判断Edge
     */
    if (/Safari\/([\d.]+)/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
      parsedInfo.browser = 'Safari';
      parsedInfo.version =
        userAgent.match(/Version\/([\d.]+)/i)?.[1] || 'unknown';
    } else if (/Firefox\/([\d.]+)/i.test(userAgent)) {
      parsedInfo.browser = 'Firefox';
      parsedInfo.version =
        userAgent.match(/Firefox\/([\d.]+)/i)?.[1] || 'unknown';
    } else if (/Edg\/([\d.]+)/i.test(userAgent)) {
      parsedInfo.browser = 'Edge';
      parsedInfo.version = userAgent.match(/Edg\/([\d.]+)/i)?.[1] || 'unknown';
    } else if (/Chrome\/([\d.]+)/i.test(userAgent)) {
      parsedInfo.browser = 'Chrome';
      parsedInfo.version =
        userAgent.match(/Chrome\/([\d.]+)/i)?.[1] || 'unknown';
    } else if (
      /MSIE\s([\d.]+)/i.test(userAgent) ||
      /Trident\/.*rv:([\d.]+)/i.test(userAgent)
    ) {
      parsedInfo.browser = 'Internet Explorer';
      parsedInfo.version =
        userAgent.match(/MSIE\s([\d.]+)/i)?.[1] ||
        userAgent.match(/rv:([\d.]+)/i)?.[1] ||
        'unknown';
    }

    const browserVersion = submission.browserVersion;
    const platform = submission.platform;

    /** 如果为Chrome或者Edge浏览器，使用更准确的userAgentData信息 */
    if (
      (parsedInfo.browser === 'Chrome' || parsedInfo.browser === 'Edge') &&
      browserVersion &&
      platform
    ) {
      const platformInfos = platform.split(/;\s*/).filter(Boolean);
      const osInfo = platformInfos?.[0];
      /**
       * 仅在数字后的空格处截断
       * "Google Chrome/131.0.6778.140 Chromium/131.0.6778.140 Not_A Brand/24.0.0.0"
       */
      const browserInfos = browserVersion.split(/(?<=\d) /);
      const browserInfo = browserInfos?.[0];

      if (/Chrome/.test(browserInfo)) {
        parsedInfo.browser = 'Chrome';
        const version = browserInfo?.split?.('/')?.[1];
        if (version) {
          parsedInfo.version = version;
        }
      } else if (/Edg/.test(browserInfo)) {
        parsedInfo.browser = 'Edge';
        const version = browserInfo?.split?.('/')?.[1];
        if (version) {
          parsedInfo.version = version;
        }
      }

      if (/Windows/i.test(osInfo)) {
        parsedInfo.os = 'Windows';
      } else if (/Macintosh/i.test(osInfo) || /macOS/i.test(osInfo)) {
        parsedInfo.os = 'MacOS';
      } else if (/iPhone|iPad|iPod/i.test(osInfo)) {
        parsedInfo.os = 'iOS';
        parsedInfo.device = 'Mobile';
      } else if (/Android/i.test(osInfo)) {
        parsedInfo.os = 'Android';
        parsedInfo.device = 'Mobile';
      } else if (/Linux/i.test(osInfo)) {
        parsedInfo.os = 'Linux';
      }

      /** 包含额外的model或formFactor信息，代表为移动端设备 */
      if (platformInfos.length >= 4) {
        parsedInfo.device = 'Mobile';
      }
    }

    return parsedInfo;
  }

  static normalizeSubmission2FE(
    submissionModel: SubmissionModel,
    questionModels?: QuestionModel[]
  ) {
    const submission = submissionModel.get();
    const userAnswers: SubmissionAnswerItem[] = submission.answers ?? [];
    /** 问题元数据 */
    const questions = orderBy(
      (questionModels ?? []).map(i => i.get()),
      ['order', 'isSubQuestion', 'createdAt', 'updatedAt'],
      ['asc', 'asc', 'asc', 'asc']
    );

    const {os, browser, version, device} =
      SurveyService.getBrowserInfo(submission);
    /** 统一转化成小写比较 */
    const langCode =
      submission.language && typeof submission.language === 'string'
        ? submission.language.toLowerCase()
        : '';

    return {
      ...omit(submission, ['id']),
      id: hashId.encode(submission.id),
      os: os,
      browser: browser,
      browserVersion: version,
      device: device,
      language: langMapping.hasOwnProperty(langCode)
        ? langMapping[langCode]
        : 'unknown',
      answers: userAnswers
        .map(ans => {
          const qMeta = questions.find(i => i.id === ans.questionId);

          if (!qMeta) {
            return null;
          }

          return {
            id: hashId.encode(ans.questionId),
            type: qMeta.type,
            title: qMeta.title,
            isSubQuestion: !!qMeta.isSubQuestion,
            required: !!qMeta.required,
            content: ans.content
          } as AnswerItem2FE;
        })
        .filter(isNotNil<AnswerItem2FE>)
    };
  }
}
