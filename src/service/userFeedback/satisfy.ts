import xlsx from 'node-xlsx';
import {mapping} from '../../mapping/satisfy-log';
import {ESClient} from '../../client';
import axios from 'axios';
import {SATISFY_INDEX, handlerPercent, DEFAULT_CLIENT} from '../../utils';

// excel表头
const fieldList = [
  '调研内容',
  '所属产品',
  '参与时间',
  '用户账号',
  '满意度评价',
  '不满意原因',
  '其他原因',
  '所属页面',
  '浏览器',
  '浏览器版本',
  '浏览器高度',
  '浏览器宽度'
];

export class Satisfy {
  // 根据服务，获取对应的Client类
  static getClient(esType: string) {
    if (esType) {
      return ESClient[esType];
    }
    return ESClient[DEFAULT_CLIENT];
  }
  /*
    获取api索引的mapping
  */
  static async getMapping(esType: string) {
    const esClient = this.getClient(esType);
    const mapping: any = await esClient.getMapping(SATISFY_INDEX);
    const setting: any = await esClient.getSetting(SATISFY_INDEX);
    return {
      mapping: mapping.body[SATISFY_INDEX],
      setting
    };
  }

  static async clearIndex(esType: string) {
    const esClient = this.getClient(esType);
    await esClient.clear(SATISFY_INDEX);
  }
  /*
    获取api索引
  */
  static async setMapping(esType: string) {
    await this.clearIndex(esType);
    const result: any = await axios({
      url: 'http://10.27.218.69:8903/' + SATISFY_INDEX,
      method: 'PUT',
      data: {
        mappings: {
          ...mapping
        }
      }
    });
    return result;
  }

  /*
    统一获取es过滤参数
  */
  static getEsFilter(esType: string, query: any) {
    const {serviceId, env, startTime, endTime, title, resourceId} = query;
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (title) {
      esquery.bool.filter.push({
        term: {
          'title.keyword': title
        }
      });
    }
    if (resourceId) {
      esquery.bool.filter.push({
        term: {
          'resourceId.keyword': resourceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    return esquery;
  }

  /*
    所有满意度分类
  */
  static async getSatisfyInfoTitle(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = this.getEsFilter(esType, query);
    const totalResult: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        size: 0
      }
    });
    // 获取总数，用于分页size
    const total = totalResult.body.hits.total?.value || 100;
    const aggs: any = {
      group_by: {
        terms: {
          field: 'title.keyword',
          size: total
        }
      }
    };
    const result: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        aggs: aggs,
        size: 0
      }
    });
    const resultList = result.body.aggregations?.group_by?.buckets || [];
    const items: Array<any> = resultList.map((item: any) => {
      return {
        title: item.key,
        count: item.doc_count
      };
    });
    return items;
  }

  /*
    填写率、总数
  */
  static async getSatisfyInfoRatio(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = this.getEsFilter(esType, query);

    const aggs: any = {
      initType_count: {
        value_count: {
          field: 'baseInfo.feedbackType.keyword'
        }
      }
    };
    const result: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        aggs,
        size: 0,
        track_total_hits: true
      }
    });
    const initTypeCount = result.body.aggregations.initType_count.value;
    const totalCount = result.body.hits.total.value; // 包含初始化弹窗数量，不一定填写
    const fillCount = totalCount - initTypeCount;
    const ratio = fillCount / initTypeCount || 0;
    const satisfyInfoRatio = Number(ratio).toFixed(4);
    return {satisfyInfoRatio, total: fillCount, initTotal: initTypeCount};
  }

  /*
    打分人数占比、打分人数统计、平均数
  */
  static async getSatisfyScoreRatio(esType: string, query: any, total: number) {
    const esClient = this.getClient(esType);
    const esquery: any = this.getEsFilter(esType, query);
    esquery.bool.must_not = [
      {
        exists: {
          field: 'baseInfo.feedbackType.keyword'
        }
      }
    ];

    const aggs: any = {
      averageScore: {
        avg: {
          field: 'score'
        }
      },
      group_by: {
        terms: {
          field: 'score',
          size: 100
        }
      }
    };

    const result: any = await esClient.search(SATISFY_INDEX, {
      body: {
        size: 0,
        query: esquery,
        aggs
      }
    });
    const scoreAverage =
      result.body.aggregations?.averageScore?.value?.toFixed(2);
    const resultList = result.body.aggregations?.group_by?.buckets || [];
    const scoreCount: Array<any> = resultList.map((item: any) => {
      return {
        score: item.key,
        count: item.doc_count
      };
    });
    const valArr = scoreCount.map((item: any) =>
      Number((item.count / total).toFixed(4))
    );
    const scoreRatio: Array<any> = scoreCount.map(
      (item: any, index: number) => {
        return {
          score: item.score,
          ratio: handlerPercent(valArr, index, 6)
        };
      }
    );
    return {scoreRatio, scoreAverage, scoreCount};
  }

  /*
    不满意原因统计
  */
  static async getSatisfyReasonTags(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = this.getEsFilter(esType, query);
    esquery.bool.must_not = [
      {
        exists: {
          field: 'baseInfo.feedbackType.keyword'
        }
      }
    ];

    const aggs: any = {
      group_by: {
        terms: {
          field: 'score',
          size: 100
        },
        aggs: {
          reasonTags: {
            terms: {
              field: 'reasonTags.keyword',
              size: 100
            }
          }
        }
      }
    };

    const result: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        aggs
      }
    });
    const resultList = result.body.aggregations?.group_by?.buckets || [];
    const reasonTags: Array<any> = resultList.map((item: any) => {
      let reasonTags = item.reasonTags.buckets.map((item: any) => {
        return {
          reason: item.key,
          count: item.doc_count
        };
      });
      return {
        [item.key]: reasonTags
      };
    });
    return reasonTags;
  }

  /*
    其他原因详情
  */
  static async getSatisfyReasons(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = this.getEsFilter(esType, query);
    esquery.bool.must_not = [
      {
        exists: {
          field: 'baseInfo.feedbackType.keyword'
        }
      }
    ];
    const totalResult: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        size: 0
      }
    });
    // 获取总数，用于分页size
    const total = totalResult.body.hits.total?.value || 100;
    const result: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        size: total
      }
    });
    const resultList = result.body.hits.hits || [];
    const reasonTags: Array<any> = resultList.map((item: any) => {
      return {
        title: item._source.title,
        score: item._source.score,
        timeStamp: item._source.timeStamp,
        reason: item._source.reason,
        resourceId: item._source.resourceId,
        reasonTags:
          typeof item._source.reasonTags === 'string'
            ? JSON.parse(item._source.reasonTags)
            : [],
        ...item._source.baseInfo
      };
    });
    return reasonTags;
  }

  /*
    下载记录
  */
  static async downloadSatisfyDate(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = this.getEsFilter(esType, query);
    esquery.bool.must_not = [
      {
        exists: {
          field: 'baseInfo.feedbackType.keyword'
        }
      }
    ];
    const totalResult: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        size: 0
      }
    });
    // 获取总数，用于分页size
    const total = totalResult.body.hits.total?.value || 100;
    const result: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        size: total
      }
    });
    const resultList = result.body.hits.hits || [];
    const data: Array<any> = resultList.map((item: any) => {
      const reasonTags =
        typeof item._source.reasonTags === 'string'
          ? JSON.parse(item._source.reasonTags)
          : item._source.reasonTags || [];
      return [
        item._source.title,
        item._source.baseInfo.serviceId,
        item._source.timeStamp,
        item._source.baseInfo.userId,
        item._source.score,
        reasonTags.join(';'),
        item._source.reason || '',
        item._source.baseInfo._href,
        item._source.baseInfo.browser,
        item._source.baseInfo.browserVersion,
        item._source.baseInfo.browserHeight,
        item._source.baseInfo.browserWidth
      ];
    });
    // 构造excel结构
    const list: any = [
      {
        name: 'satisfy', // 工作薄的名称
        data: [fieldList, ...data]
      }
    ];
    var buffer = xlsx.build(list);
    return buffer;
  }

  /**
   * 获取资源id
   */
  static async getSatisfyInfoResourceId(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = this.getEsFilter(esType, query);
    const totalResult: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        size: 0
      }
    });
    // 获取总数，用于分页size
    const total = totalResult.body.hits.total?.value || 100;
    const aggs: any = {
      group_by: {
        terms: {
          field: 'resourceId.keyword',
          size: total
        }
      }
    };
    const result: any = await esClient.search(SATISFY_INDEX, {
      body: {
        query: esquery,
        aggs: aggs,
        size: 0
      }
    });
    const resultList = result.body.aggregations?.group_by?.buckets || [];
    const items: Array<any> = resultList.map((item: any) => {
      return {
        resourceId: item.key
      };
    });
    return items;
  }
}
