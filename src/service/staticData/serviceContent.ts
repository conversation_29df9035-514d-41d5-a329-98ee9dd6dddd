// /**
//  * 应用管理操作
//  */
// import {DEFAULT_CLIENT} from '../../utils';
// import {ServiceContentManage as ServiceContentDTO} from '../../model';
// import moment from 'moment';
// import {BosClient} from '../../client';
// import Container from 'typedi';
// export class ServiceContentManage {
//   /*
//    * 新建/编辑数据结构
//    */
//   static async editSchema(body: EditServiceContentSchema) {
//     const {serviceId, env, schema, creator, updater} = body;
//     if (!serviceId) {
//       return {
//         msg: '缺少参数，请检查',
//         success: false
//       };
//     }

//     // 查询数据库是否存在相同serviceId
//     const data = await ServiceContentDTO.findOne({
//       where: {
//         env,
//         service_id: serviceId
//       }
//     });

//     if (!data) {
//       await ServiceContentDTO.create({
//         service_id: serviceId,
//         env,
//         schema,
//         published: false,
//         updated: true,
//         content: '',
//         created_by: creator,
//         created_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
//         updated_by: creator,
//         updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
//       });
//     } else {
//       await data.update({
//         schema,
//         updated_by: updater,
//         updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
//       });
//       await data.save();
//     }

//     return {
//       msg: '提交成功',
//       success: true
//     };
//   }

//   /**
//    * 获取数据结构
//    */
//   static async getSchema(query: GetServiceContent) {
//     const {serviceId, env} = query;

//     if (!serviceId) {
//       return {
//         msg: '缺少参数，请检查',
//         success: false
//       };
//     }

//     // 查询
//     const data = await ServiceContentDTO.findOne({
//       where: {
//         env,
//         service_id: serviceId
//       },
//       attributes: [
//         'id',
//         ['service_id', 'serviceId'],
//         'env',
//         'schema',
//         ['created_by', 'creator'],
//         ['updated_by', 'updater'],
//         ['created_at', 'createdAt'],
//         ['updated_at', 'updatedAt']
//       ]
//     });

//     return {
//       data,
//       msg: '获取成功',
//       success: true
//     };
//   }

//   /**
//    * 编辑文案内容
//    */
//   static async editData(body: EditServiceContentData) {
//     const {
//       serviceId,
//       env,
//       content,
//       updater = DEFAULT_CLIENT,
//       updated = true
//     } = body;

//     if (!serviceId) {
//       return {
//         msg: '缺少参数，请检查',
//         success: false
//       };
//     }

//     // 查询数据库是否存在相同serviceId
//     const data = await ServiceContentDTO.findOne({
//       where: {
//         env,
//         service_id: serviceId
//       }
//     });

//     if (!data || !data.get('schema')) {
//       return {
//         msg: '数据结构不存在，请先创建数据结构',
//         success: false
//       };
//     } else {
//       await data.update({
//         content,
//         updater,
//         updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
//         published: false,
//         updated
//       });
//       await data.save();
//     }
//     // 上传文件
//     const res = await this.uploadFile(content, env);

//     return res.success
//       ? {
//           msg: '提交成功',
//           success: true
//         }
//       : {
//           msg: '文件上传失败，请重试',
//           success: false
//         };
//   }

//   /**
//    * 获取文案内容
//    */
//   static async getData(query: GetServiceContent) {
//     const {serviceId, env} = query;

//     if (!serviceId) {
//       return {
//         msg: '缺少参数，请检查',
//         success: false
//       };
//     }

//     // 查询
//     const data = await ServiceContentDTO.findOne({
//       where: {
//         env,
//         service_id: serviceId
//       },
//       attributes: [
//         'id',
//         ['service_id', 'serviceId'],
//         'env',
//         'schema',
//         'content',
//         'updated',
//         'published',
//         ['created_by', 'creator'],
//         ['updated_by', 'updater'],
//         ['created_at', 'createdAt'],
//         ['updated_at', 'updatedAt']
//       ]
//     });

//     return {
//       data,
//       msg: '获取成功',
//       success: true
//     };
//   }

//   /**
//    * 发布文案内容
//    */
//   static async publishContent(body: PublishServiceContent) {
//     const {serviceId, env = 'consoleOffline'} = body;

//     if (!serviceId) {
//       return {
//         msg: '缺少参数，请检查',
//         success: false
//       };
//     }

//     // 查询数据库是否存在数据
//     const offData = await ServiceContentDTO.findOne({
//       where: {
//         service_id: serviceId,
//         env: 'consoleOffline',
//         updated: true
//       }
//     });

//     if (!offData || !offData.get('schema') || !offData.get('content')) {
//       return {
//         msg: '无待发布内容',
//         success: false
//       };
//     }

//     // 查询数据库是否存在数据
//     const onlineData = await ServiceContentDTO.findOne({
//       where: {
//         service_id: serviceId,
//         env,
//         updated: true
//       }
//     });

//     if (!onlineData) {
//       await ServiceContentDTO.create({
//         service_id: serviceId,
//         env,
//         schema: offData.get('schema'),
//         content: offData.get('content'),
//         updater: offData.get('updated_by'),
//         updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
//         published: true,
//         updated: false
//       });

//       await offData.update({
//         published: true,
//         updated: false
//       });
//       await offData.save();
//     } else {
//       await onlineData.update({
//         schema: offData.get('schema'),
//         content: offData.get('content'),
//         updater: offData.get('updated_by'),
//         updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
//         published: true,
//         updated: false
//       });
//       await onlineData.save();

//       await offData.update({
//         published: true,
//         updated: false
//       });
//       await offData.save();
//     }

//     // 上传文件
//     const res = await this.uploadFile(
//       offData.get('content') as string,
//       serviceId,
//       env
//     );

//     return res.success
//       ? {
//           msg: '更新成功',
//           success: true
//         }
//       : {
//           msg: '文件上传失败，请重试',
//           success: false
//         };
//   }

//   static async upload(body: {serviceId: string; env: string; content: string}) {
//     const {serviceId, env, content} = body;

//     if (!serviceId) {
//       return {
//         msg: '缺少参数，请检查',
//         success: false
//       };
//     }

//     // 上传文件
//     const res = await this.uploadFile(content, serviceId, env);

//     return res.success
//       ? {
//           msg: '提交成功',
//           success: true
//         }
//       : {
//           msg: '文件上传失败，请重试',
//           success: res.error
//         };
//   }

//   /**
//    * 上传文件
//    */
//   static async uploadFile(
//     text: string,
//     serviceId: string,
//     env: string = 'consoleOffline'
//   ): Promise<{success: boolean; res?: any; error?: any}> {
//     const bucket = '000002';
//     const key = `${serviceId}-${env}.json`;
//     const client = Container.get(BosClient);

//     try {
//       var buffer = new Buffer(text);

//       const res = await client.putObject(bucket, key, buffer);

//       return {
//         success: true
//       };
//     } catch (error) {
//       return {
//         success: false,
//         error
//       };
//     }
//   }
// }
