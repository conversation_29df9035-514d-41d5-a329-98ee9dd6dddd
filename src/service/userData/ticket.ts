import Axios from 'axios';
import {HttpClient, Auth} from 'bce-sdk-js';

import {ticketConfig} from '../../utils';
import moment from 'moment';

let TOKEN: string; // 服务token
let TOKEN_CACHE_TIME: number; // token缓存时间
let CACHE_DURATION = 25 * 60; // token缓存时长，25分钟
const createSignature = (
  credentials: any,
  httpMethod: string,
  path: string,
  params: {},
  headers: {}
) => {
  let auth = new Auth(credentials.ak, credentials.sk);
  return auth.generateAuthorization(httpMethod, path, params, headers, 0, 0, [
    'host',
    'x-bce-date'
  ]);
};

const getToken = () => {
  if (Date.now() - TOKEN_CACHE_TIME < CACHE_DURATION && TOKEN) {
    return Promise.resolve(TOKEN);
  }

  const payload = {
    auth: {
      identity: {
        methods: ['password'],
        password: {
          user: {
            domain: {
              name: 'Default'
            },
            name: ticketConfig.IAM_AUTH_INFO.name,
            password: ticketConfig.IAM_AUTH_INFO.password
          }
        }
      },
      scope: {
        domain: {
          id: 'default'
        }
      }
    }
  };
  return Axios({
    baseURL: ticketConfig.IAM_AUTH_ENDPOINT,
    url: '/v3/auth/tokens',
    method: 'post',
    data: payload
  }).then((res: any) => {
    const tokenId = res.headers ? res.headers['x-subject-token'] : '';
    const token = res.data && res.data.token ? res.data.token : {};
    const expiresAt = token.expires_at;
    token.id = tokenId;
    if (new Date(expiresAt).valueOf() - Date.now() > CACHE_DURATION) {
      TOKEN = token;
      TOKEN_CACHE_TIME = Date.now();
    }
    return token;
  });
};

// 获取服务ak/sk
const getAccesskeys = async () => {
  const token = await getToken();
  const {user, id: tokenId} = token;
  const requestOptions = {
    baseURL: ticketConfig.IAM_AUTH_ENDPOINT,
    url: `/v3/users/${user.id}/accesskeys`,
    method: 'get',
    headers: {
      'X-Auth-Token': tokenId
    }
  };
  const response = await Axios(requestOptions);
  const ak = response.data.accesskeys[0].access;
  const sk = response.data.accesskeys[0].secret;
  return {ak, sk};
};

export const getAllTicketList = async (payload: any) => {
  const accesskey = await getAccesskeys();
  const client = new HttpClient({
    endpoint: ticketConfig.TICKET_SERVICE_ENDPOINT,
    credentials: {
      secure: false,
      ak: accesskey.ak,
      sk: accesskey.sk
    }
  });
  return client
    .sendRequest(
      'POST',
      '/ticket/json-api/v1/external/issue/count-cts',
      JSON.stringify(payload),
      null,
      null,
      createSignature
    )
    .then((res: any) => {
      // 默认返回第一个就行，因为只有一个服务
      return res?.body?.result || {};
    })
    .catch((err: any) => {
      console.log(err);
      return {};
    });
};

// 获取ticket数量
export const getTicketCount = async (options: any) => {
  // 处理时间范围，格式化时间，时间跨度不超过12个月
  let startTime = parseInt(options.timeRange.split(',')[0]);
  let endTime =
    parseInt(options.timeRange.split(',')[1]) || new Date().getTime();
  startTime = startTime.toString().length === 10 ? startTime * 1000 : startTime;
  endTime = endTime.toString().length === 10 ? endTime * 1000 : endTime;
  // 时间跨度不超过12个月
  if (moment(endTime).subtract(12, 'months').valueOf() > startTime) {
    startTime = moment(endTime).subtract(12, 'months').valueOf();
  }
  let oldStartTime = startTime - (endTime - startTime);
  // 格式化utc时间，两个时间段计算同比 start-end, oldStartTime-start
  const utcStartTime = moment(startTime).utc().format('YYYY-MM-DDTHH:mm:ss');
  const utcOldStartTime = moment(oldStartTime)
    .utc()
    .format('YYYY-MM-DDTHH:mm:ss');
  const utcEndTime = moment(endTime).utc().format('YYYY-MM-DDTHH:mm:ss');
  const curPayload = {
    serviceTypes: [options.serviceId],
    startTime: utcStartTime + 'Z',
    endTime: utcEndTime + 'Z'
  };
  const [curResult] = await getAllTicketList(curPayload);
  // 上一周期数据,终点时间为当前开始时间
  const oldPayload = {
    serviceTypes: [options.serviceId],
    startTime: utcOldStartTime + 'Z',
    endTime: utcStartTime + 'Z'
  };
  const [oldResult] = await getAllTicketList(oldPayload);
  if (oldResult && oldResult.total > 0) {
    // 计算环比
    curResult.ticketsGap =
      (curResult.total - oldResult.total) / oldResult.total.toFixed(6);
    curResult.ticketsLast = oldResult.total;
  } else {
    // 重置页面数据为空
    curResult.ticketsGap = '';
    curResult.ticketsLast = 0;
  }
  if (curResult?.featureCounts?.length) {
    curResult.countInfo = curResult.featureCounts
      .map((item: any) => {
        return `${item.featureName}(${item.count})`;
      })
      .join('<br>');
  } else {
    curResult.countInfo = '';
  }
  return curResult;
};
