import moment from 'moment';
import {isEmpty, isObject} from 'lodash';
import {v4 as uuidv4} from 'uuid';
import intersection from 'lodash/intersection';

import {
  apiErrorLoggers,
  apiInfoLoggers,
  pageInfoLoggers,
  pageErrorLoggers,
  snapshotErrorLoggers,
  actionLoggers,
  performanceLoggers,
  satisfyLoggers
} from '../../logger';
import {ESClient} from '../../client';
import {getSnapshotLog} from '../../utils/getSnapshotLog';
import {
  PAGE_INDEX,
  API_INDEX,
  ticketAction,
  urlHandler,
  ACTION_INDEX,
  PERFORMANCE_INDEX,
  handlerMergeArr,
  handlerPercent,
  DEFAULT_CLIENT
} from '../../utils';
import {getRegUrl} from '../../utils/getServiceApi';
import {
  allServiceIdList,
  SANDBOX_OTHER_CHANGE_TIME,
  ONLINE_OTHER_CHANGE_TIME
} from '../../utils/const';
import {
  exemptionValidate,
  getRules,
  isUrlMatch
} from '../../utils/apiExemptionRulesUtil';

import {validateAuthToken} from '../../utils/rules';
import {PageErrorHandle} from '../../utils/alarm';

type LogInfo = {
  [propName in LogType]:
    | ApiInfo[]
    | PV
    | PageError
    | EventAction
    | BaseInfo
    | AppPerformance
    | AppPerformanceFid;
};

type FeedbackInfo = {
  [propName in FeedbackType]: SatisfyInfo | BaseInfo;
};

type Item = {
  serviceId: string;
  totalCount: number;
  errCount: number;
  errPercent: number;
  totalCountLast?: number;
  errCountLast?: number;
  errPercentLast?: number;
  totalCountGap?: string;
  errCountGap?: string;
  errPercentGap?: string;
};

const subtract = (val1: number = 0, val2: number = 0) => {
  // 保留四位小数，处理精度问题
  let res1 = Number(val1.toFixed(4)) * 10000;
  let res2 = Number(val2.toFixed(4)) * 10000;
  return (res2 - res1) / 10000;
};

export class Base {
  // 根据服务，获取对应的Client类
  static getClient(esType?: string) {
    if (esType) {
      return ESClient[esType];
    }
    return ESClient[DEFAULT_CLIENT];
  }
  static async apiHandle(source: ApiInfo, baseInfo: BaseInfo) {
    const serviceId = baseInfo.serviceId;
    const env = baseInfo.env;
    source.responseError = {};
    let resData: any = {};
    if (isObject(source.requestData)) {
      source.requestData = JSON.stringify(source.requestData);
    }
    if (!isEmpty(source.requestHeader)) {
      for (let key in source.requestHeader) {
        source.requestHeader[key] = source.requestHeader[key] + '';
      }
    }
    if (!isEmpty(source.responseHeader)) {
      for (let key in source.responseHeader) {
        source.responseHeader[key] = source.responseHeader[key] + '';
      }
    }
    if (isObject(source.performance)) {
      const performanceData: performanceData = source.performance;
      if (performanceData.fetchStart && performanceData.responseEnd) {
        source.responseTime = subtract(
          performanceData.fetchStart,
          performanceData.responseEnd
        );
      }
      source.performance = JSON.stringify(source.performance);
    }
    // 保留api全路径和去掉query以后的路径，用于聚合
    source.completeUrl = source.completeUrl ?? source.url ?? '';
    source.url = urlHandler(source.completeUrl, 'api');
    // bos产品上传接口特殊处理
    const uploadApiReg = /(http|https):\/\/(.*)\.bcebos\.com(\/.*)?/;
    if (uploadApiReg.test(source.completeUrl)) {
      source.regUrl = '*.bcebos.com/*';
    } else {
      const regUrl = await getRegUrl(serviceId, {
        url: source.url,
        method: source.method
      });
      source.regUrl = regUrl;
    }
    if (source.responseData || source.isError) {
      try {
        resData = isObject(source.responseData)
          ? source.responseData
          : JSON.parse((source.responseData as string) || '{}');
      } catch {
        console.error(
          `responseData 【${source.responseData}】 is not a serialized object`
        );
      } finally {
        const rules = await getRules(serviceId, env);
        const currentPathrule = rules?.filter(rule =>
          isUrlMatch(source.url, rule.path)
        );
        const target = {
          code: resData?.code,
          href: baseInfo._href,
          region:
            source.requestHeader?.['X-Region'] ||
            source.requestHeader?.['region'],
          user: baseInfo.userId,
          message:
            resData?.message?.global ||
            resData?.message?.noSession ||
            resData?.message?.description
        };
        const exempted = exemptionValidate(currentPathrule, target);
        // 度小满region的请求报错屏蔽掉
        const isFsgRegion = (
          source.requestHeader?.['X-Region'] ||
          source.requestHeader?.['region'] ||
          ''
        ).includes('fsg');
        let isRedirectLogin = false;
        // 响应返回重定向到登录页面的接口，报错屏蔽
        if (
          resData?.message?.redirect &&
          (/login.bce(test)?.baidu.com/.test(resData?.message?.redirect) ||
            /console.bce(test)?.baidu.com\/iam\/access/.test(
              resData?.message?.redirect
            ))
        ) {
          isRedirectLogin = true;
        }

        const ignoredMethod = !['GET', 'POST', 'PUT', 'DELETE'].includes(
          source.method?.toUpperCase()
        );
        source.responseError = {
          code: (resData?.code || '') + '',
          collect:
            resData?.collect === false ||
            resData?.code === 'MFARequired' ||
            exempted ||
            isFsgRegion ||
            ignoredMethod ||
            isRedirectLogin
              ? false
              : true,
          message: {
            global:
              // 兼容千帆产品部分接口，千帆产品code字段为空，message_raw字段为报错信息，message.global是个数字
              resData?.message_raw ||
              resData?.message?.global ||
              resData?.message?.noSession ||
              resData?.message?.description ||
              ''
          }
        };
        isObject(source.responseData) &&
          (source.responseData = JSON.stringify(source.responseData));
      }
    }
    return source;
  }
  static async save(esType: string, rootDirName: string, body: any) {
    const esClient = this.getClient(esType);
    try {
      const logInfo = (
        typeof body === 'string' ? JSON.parse(body) : body
      ) as LogInfo;

      // 报错页面全路径和去掉query以后的路径
      const baseInfo = logInfo['monitor-base-info'] as BaseInfo;
      const monitorApi = logInfo['monitor-api'] as Array<ApiInfo>;
      const PageError = logInfo['monitor-page-error'] as PageError;
      const PageErrorCancel = logInfo['monitor-page-error-cancel'] as PageError;
      const PageLog = logInfo['monitor-pv'] as PV;
      const monitorEvent: EventAction = logInfo['monitor-event'] as EventAction;
      const appPerformance: AppPerformance | AppPerformanceFid = logInfo[
        'monitor-page-performance'
      ] as AppPerformance | AppPerformanceFid;

      if (!isEmpty(baseInfo)) {
        // 保留一个没有query信息的url用于聚合
        baseInfo._href = urlHandler(baseInfo?.href ?? '', 'page');
        // TODO 控制台目前只有中英文，先暂时这样判断
        baseInfo.locale = baseInfo?.locale?.includes('en') ? 'en-US' : 'zh-CN';
        const {valid, error} = await validateAuthToken(
          baseInfo.uuid,
          baseInfo.serviceId,
          baseInfo.signature,
          baseInfo.authToken
        );
        if (!valid) {
          console.error(baseInfo.serviceId, '参数校验校验失败');
          console.log(
            `baseInfo.uuid: ${baseInfo.uuid}  baseInfo.signature:${baseInfo.signature}  baseInfo.authToken:${baseInfo.authToken}`
          );
          throw new Error(error || '参数校验校验失败');
        }
      }
      // api信息落盘
      monitorApi?.forEach(async (item: ApiInfo) => {
        // 过滤灰度的api请求
        const isGrayscale =
          item.requestHeader['X-Bce-Access-Version'] ||
          item.responseHeader['requestId']?.includes('bcecanarytag');
        if (!isGrayscale) {
          item = await this.apiHandle(item, baseInfo);
          if (item.isError) {
            apiErrorLoggers(rootDirName).error({
              ...item,
              type: 'monitor-api-error',
              timeStamp: item.timeStamp || Date.now(),
              baseInfo
            });
          } else {
            apiInfoLoggers(rootDirName).info({
              ...item,
              type: 'monitor-api',
              isError: false,
              timeStamp: item.timeStamp || Date.now(),
              baseInfo
            });
          }
        }
      });
      // 页面白屏落盘
      if (PageError) {
        const {type, error, snapshotEvent, errorId} = PageError || {};
        PageErrorHandle(baseInfo);
        const uuid: string = uuidv4();
        !isEmpty(snapshotEvent) &&
          snapshotErrorLoggers(uuid, rootDirName).error({
            uuid,
            type,
            snapshotEvent,
            timeStamp: Date.now()
          });

        !isEmpty(error) &&
          pageErrorLoggers(rootDirName).error({
            uuid,
            type,
            errorId,
            error,
            timeStamp: Date.now(),
            baseInfo
          });
      }
      // 页面白屏cancel追加
      if (PageErrorCancel) {
        const errorId = PageErrorCancel.errorId;
        await esClient.updateByQuery({
          index: PAGE_INDEX,
          body: {
            query: {
              term: {
                'errorId.keyword': errorId
              }
            },
            script: {
              source:
                'ctx._source.type="monitor-page-error-cancel";ctx._source.error={}',
              lang: 'painless'
            }
          }
        });
      }
      // PV落盘
      if (PageLog) {
        pageInfoLoggers(rootDirName).info({
          ...logInfo['monitor-pv'],
          timeStamp: (logInfo['monitor-pv'] as PV).timeStamp || Date.now(),
          baseInfo
        });
      }
      // 接口报错弹窗工单追加
      if (monitorEvent?.eventType === ticketAction) {
        if (monitorEvent?.eventData?.requestId) {
          await esClient.updateByQuery({
            index: API_INDEX,
            body: {
              query: {
                term: {
                  'responseHeader.requestId': monitorEvent?.eventData?.requestId
                }
              },
              script: {
                source: 'ctx._source.jumpTicket = true',
                lang: 'painless'
              }
            }
          });
        }
      }
      // 用户行为信息
      if (monitorEvent) {
        if (Array.isArray(monitorEvent)) {
          monitorEvent.forEach(item => {
            actionLoggers(rootDirName).info({
              ...item,
              eventData: JSON.stringify(item.eventData),
              timeStamp: item.timeStamp || Date.now(),
              baseInfo
            });
          });
        } else {
          actionLoggers(rootDirName).info({
            ...monitorEvent,
            eventData: JSON.stringify(monitorEvent.eventData),
            timeStamp: monitorEvent.timeStamp || Date.now(),
            baseInfo
          });
        }
      }
      // 应用性能信息
      if (appPerformance) {
        if (appPerformance.type === 'page-performance') {
          performanceLoggers(rootDirName).info({
            ...appPerformance,
            resourceTiming: JSON.stringify(appPerformance.resourceTiming),
            baseInfo
          });
        } else if (appPerformance.type === 'page-performance-fid') {
          await esClient.updateByQuery({
            index: PERFORMANCE_INDEX,
            body: {
              query: {
                term: {
                  'baseInfo.signature': baseInfo?.signature
                }
              },
              script: {
                source:
                  'ctx._source.fid = params.fid;ctx._source.lcp = params.lcp',
                lang: 'painless',
                params: {
                  fid: appPerformance.fid,
                  lcp: appPerformance.lcp
                }
              }
            }
          });
        }
      }
      return;
    } catch (err) {
      console.error(err);
      throw new Error('上报数据异常，上报失败');
    }
  }

  static async getBaseInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const {startTime, endTime, serviceId, env} = query;
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          // 目前只有@timestamp在mapping中是date类型，所以只能这样过滤
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const pvRes = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          pv: {
            filter: {
              match: {
                type: 'monitor-pv'
              }
            }
          }
        }
      }
    });
    const uvRes = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: {
          bool: {
            filter: esquery.bool.filter,
            must_not: [
              {
                term: {
                  'baseInfo.userId': ''
                }
              }
            ]
          }
        },
        aggs: {
          uv: {
            cardinality: {
              field: 'baseInfo.userId'
            }
          }
        }
      }
    });

    const ticketsRes = await esClient.search(ACTION_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          tickets: {
            filter: {
              match: {
                eventType: 'ticket-jump'
              }
            }
          }
        }
      }
    });
    const uv = uvRes?.body?.aggregations?.uv?.value;
    const pv = pvRes?.body?.aggregations?.pv?.doc_count;
    const tickets = ticketsRes?.body?.aggregations?.tickets?.doc_count;

    return {
      pv,
      uv,
      tickets
    };
  }

  static async getBaseInfoGap(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfoGap = {
      pvGap: '',
      pvLast: '',
      uvGap: '',
      uvLast: '',
      ticketsGap: '',
      ticketsLast: ''
    };
    const baseInfo = await this.getBaseInfo(esType, query);
    // 计算环比
    if (startTime) {
      const _baseInfo = await this.getBaseInfo(esType, {
        ...query,
        startTime: 2 * startTime - endTime,
        endTime: startTime
      });
      const {pv, uv, tickets} = _baseInfo;
      baseInfoGap.pvGap = pv ? ((baseInfo.pv - pv) / pv).toFixed(6) : '';
      baseInfoGap.uvGap = uv ? ((baseInfo.uv - uv) / uv).toFixed(6) : '';
      baseInfoGap.ticketsGap = tickets
        ? ((baseInfo.tickets - tickets) / tickets).toFixed(6)
        : '';
      baseInfoGap.pvLast = pv;
      baseInfoGap.uvLast = uv;
      baseInfoGap.ticketsLast = tickets;
    }
    return {
      ...baseInfo,
      ...baseInfoGap
    };
  }

  static async getBaseInfoDaily(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const {serviceId, env, startTime, endTime} = query;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const daily_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          group_by_user: {
            cardinality: {
              field: 'baseInfo.userId'
            }
          },
          pv: {
            filter: {
              match: {
                type: 'monitor-pv'
              }
            }
          }
        }
      }
    };

    const action_daily_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          tickets: {
            filter: {
              match: {
                eventType: 'ticket-jump'
              }
            }
          }
        }
      }
    };

    // 查询日粒度的pv、uv
    const daily_result: any = await esClient.search(
      `${PAGE_INDEX},${API_INDEX}`,
      {
        size: 0,
        body: {
          query: esquery,
          track_total_hits: true,
          aggs: daily_aggs
        }
      }
    );
    const action_daily_result: any = await esClient.search(`${ACTION_INDEX}`, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: action_daily_aggs
      }
    });
    const items: Array<any> =
      daily_result.body.aggregations.group_by_time.buckets.map((item: any) => {
        const pv = item.pv.doc_count;
        const uv = item.group_by_user.value;
        return {
          pv,
          uv,
          time: item.key_as_string
        };
      });
    const actionItems: Array<any> =
      action_daily_result.body.aggregations.group_by_time.buckets.map(
        (item: any) => {
          const tickets = item.tickets.doc_count;
          return {
            tickets,
            time: item.key_as_string
          };
        }
      );
    const res = handlerMergeArr('time', items, actionItems);
    return {
      items: res
    };
  }

  static async getSnapshotLogs(query: any) {
    const {id} = query;
    return getSnapshotLog(id);
  }

  static async getPerformanceStatistics(esType: string, body: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        should: [],
        minimum_should_match: 1
      }
    };
    const {
      appIds,
      env,
      startTime,
      endTime,
      pagePerfType = 'fmp',
      perfType
    } = body;

    let serviceIds: any = [];
    let serviceKeyMap: any = {};
    if (appIds && typeof appIds === 'string') {
      serviceIds = appIds.split(',');
    } else if (Array.isArray(appIds) && appIds.length) {
      serviceIds = appIds.map((item: any) => item.value);
      appIds.forEach((item: any) => {
        serviceKeyMap[item.value] = item.label;
        serviceIds.push(item.value);
      });
    }

    if (serviceIds.length) {
      serviceIds.forEach((serviceId: any) => {
        esquery.bool.should.push({
          term: {
            'baseInfo.serviceId': serviceId
          }
        });
      });
    } else {
      return {
        items: [],
        total: 0
      };
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    const handlerRes = (res: any) => {
      const perfArr: any = [];
      res?.body?.aggregations?.group_by?.buckets?.forEach(
        (serviceItem: any) => {
          const percentLast: any = {};
          const lastObj = serviceItem?.time_range?.buckets.filter(
            (item: any) => item.key === 'last'
          )[0];
          const lastBuckets = lastObj?.performance.buckets;
          const lastValArr = lastBuckets.map((item: any) => item.doc_count);
          lastBuckets.forEach((item: any, index: number) => {
            percentLast[item.key] = item.doc_count
              ? (handlerPercent(lastValArr, index, 3) * 100).toFixed(1)
              : '';
          });

          const percent: any = {};
          const nowObj = serviceItem?.time_range?.buckets.filter(
            (item: any) => item.key === 'now'
          )[0];
          const nowBuckets = nowObj?.performance.buckets;
          const nowValArr = nowBuckets.map((item: any) => item.doc_count);
          nowBuckets.forEach((item: any, index: number) => {
            percent[item.key] = item.doc_count
              ? (handlerPercent(nowValArr, index, 3) * 100).toFixed(1)
              : '';
          });

          const percentGap: any = {};
          Object.keys(percent).map(key => {
            percentGap[key] =
              percentLast[key] > 0
                ? (
                    ((percent[key] - percentLast[key]) / percentLast[key]) *
                    100
                  ).toFixed(1)
                : '';
          });

          const totalCount = nowObj?.doc_count;
          const totalCountLast = lastObj?.doc_count;
          const totalCountGap = totalCountLast
            ? (((totalCount - totalCountLast) / totalCountLast) * 100).toFixed(
                1
              )
            : '';
          const obj: any = {
            name: serviceKeyMap[serviceItem.key],
            key: serviceItem.key,
            totalCount,
            percent,
            totalCountLast,
            percentLast,
            percentGap,
            totalCountGap
          };
          perfArr.push(obj);
        }
      );
      return perfArr;
    };

    let mergedArray: any[] = [];
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (perfType === 'page') {
      // 页面性能
      const pagePerfRes: any = await esClient.search(PERFORMANCE_INDEX, {
        size: 0,
        body: {
          query: esquery,
          track_total_hits: true,
          aggs: {
            group_by: {
              terms: {
                field: 'baseInfo.serviceId',
                size: serviceIds.length
              },
              aggs: {
                time_range: {
                  range: {
                    field: timeField,
                    ranges: [
                      {
                        key: 'last',
                        from: 2 * startTime - endTime,
                        to: startTime
                      },
                      {
                        key: 'now',
                        from: startTime,
                        to: endTime
                      }
                    ]
                  },
                  aggs: {
                    performance: {
                      range: {
                        field: pagePerfType,
                        ranges: [
                          {
                            key: '0-3s',
                            from: 0,
                            to: 3000
                          },
                          {
                            key: '3-8s',
                            from: 3000,
                            to: 8000
                          },
                          {
                            key: '>8s',
                            from: 8000
                          }
                        ]
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });
      mergedArray = handlerRes(pagePerfRes);
    } else if (perfType === 'api') {
      // api性能
      const apiPerfRes: any = await esClient.search(API_INDEX, {
        size: 0,
        body: {
          query: esquery,
          track_total_hits: true,
          aggs: {
            group_by: {
              terms: {
                field: 'baseInfo.serviceId',
                size: serviceIds.length
              },
              aggs: {
                time_range: {
                  range: {
                    field: timeField,
                    ranges: [
                      {
                        key: 'last',
                        from: 2 * startTime - endTime,
                        to: startTime
                      },
                      {
                        key: 'now',
                        from: startTime,
                        to: endTime
                      }
                    ]
                  },
                  aggs: {
                    performance: {
                      range: {
                        field: 'responseTime',
                        ranges: [
                          {
                            key: '0-1s',
                            from: 0,
                            to: 1000
                          },
                          {
                            key: '1-5s',
                            from: 1000,
                            to: 5000
                          },
                          {
                            key: '>5s',
                            from: 5000
                          }
                        ]
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });
      mergedArray = handlerRes(apiPerfRes);
    }

    return {
      items: mergedArray
    };
  }

  static sortResultItems(items: any[], sortBy: string, order: string) {
    let a = items.sort((a, b) => {
      if (order === 'asc') {
        return a[sortBy] - b[sortBy];
      }
      return b[sortBy] - a[sortBy];
    });
    return a;
  }

  static async getOverviewCompareData(
    esType: string,
    comparEsType: string,
    body: any
  ) {
    const {compareStartTime, compareEndTime, orderBy, orderDir} = body;
    const reqList = [this.getOverviewList(esType, body)];
    if (compareStartTime && compareEndTime) {
      const newBody = {
        ...body,
        startTime: compareStartTime,
        endTime: compareEndTime
      };
      reqList.push(this.getOverviewList(comparEsType, newBody));
    }
    const result = await Promise.all(reqList);
    const previousItems: any = result[1]?.items;
    const currentItems: any = result[0]?.items;
    if (previousItems) {
      currentItems.forEach((currentItem: Item) => {
        const previousItem = previousItems.find(
          (item: Item) => item.serviceId === currentItem.serviceId
        );

        if (previousItem) {
          const {
            totalCount: prevTotalCount,
            errCount: prevErrCount,
            errPercent: prevErrPercent
          } = previousItem;
          const {
            totalCount: currTotalCount,
            errCount: currErrCount,
            errPercent: currErrPercent
          } = currentItem;

          currentItem.totalCountLast = prevTotalCount;
          currentItem.errCountLast = prevErrCount;
          currentItem.errPercentLast = prevErrPercent;

          currentItem.totalCountGap = prevTotalCount
            ? (
                ((currTotalCount - prevTotalCount) / prevTotalCount) *
                100
              ).toFixed(1)
            : '';

          currentItem.errCountGap = prevErrCount
            ? (((currErrCount - prevErrCount) / prevErrCount) * 100).toFixed(1)
            : '';

          currentItem.errPercentGap = prevErrPercent
            ? (
                ((currErrPercent - prevErrPercent) / prevErrPercent) *
                100
              ).toFixed(1)
            : '';
        }
      });
    }
    const resultItems = this.sortResultItems(currentItems, orderBy, orderDir);
    return resultItems;
  }

  // 数据总览接口
  static async getOverviewList(esType: string, body: any) {
    const newEsClient = this.getClient(esType);
    const oldEsClient = this.getClient();
    const newESquery: any = {
      bool: {
        filter: [],
        should: [],
        minimum_should_match: 1
      }
    };
    const oldEsQuery: any = {
      bool: {
        filter: [],
        should: [],
        minimum_should_match: 1
      }
    };
    const {
      apps,
      env,
      startTime,
      endTime,
      aggType,
      pageNo,
      pageSize,
      order,
      orderBy,
      keyword,
      isFullData
    } = body;

    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;
    const allService = allServiceIdList;
    let newEsService: any = [];
    let oldEsService: any = [];
    const otherChangeTime =
      env === 'consoleOffline'
        ? SANDBOX_OTHER_CHANGE_TIME
        : ONLINE_OTHER_CHANGE_TIME;
    const esChangeTime = new Date(otherChangeTime).getTime();
    if (startTime >= esChangeTime && endTime > esChangeTime) {
      newEsService = apps;
    } else {
      newEsService = intersection(apps, allService);
      oldEsService = apps.filter(
        (item: string) => !newEsService.includes(item)
      );
    }

    if (env) {
      newESquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
      oldEsQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      newESquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
      oldEsQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (Array.isArray(newEsService) && newEsService.length > 0) {
      newEsService.forEach(app => {
        newESquery.bool.should.push({
          term: {
            'baseInfo.serviceId': app
          }
        });
      });
    }
    if (Array.isArray(oldEsService) && oldEsService.length > 0) {
      oldEsService.forEach(app => {
        oldEsQuery.bool.should.push({
          term: {
            'baseInfo.serviceId': app
          }
        });
      });
    }
    if (keyword) {
      newESquery.bool.filter.push({
        wildcard: {
          'baseInfo.serviceId': `*${keyword}*`
        }
      });
      oldEsQuery.bool.filter.push({
        wildcard: {
          'baseInfo.serviceId': `*${keyword}*`
        }
      });
    }

    const newEsAgg: any = {
      product: {
        terms: {
          field: 'baseInfo.serviceId',
          size: newEsService.length
        }
      },
      total: {
        cardinality: {
          field: 'baseInfo.serviceId'
        }
      }
    };
    const oldEsAgg: any = {
      product: {
        terms: {
          field: 'baseInfo.serviceId',
          size: oldEsService.length
        }
      },
      total: {
        cardinality: {
          field: 'baseInfo.serviceId'
        }
      }
    };

    let LOG_INDEX = '';
    let sortBy = '';
    let handlerItem = (item: any) => {};
    switch (aggType) {
      case 1:
        LOG_INDEX = PERFORMANCE_INDEX;
        sortBy = (
          {
            fpAvg: 'fpAvg',
            fmpAvg: 'fmpAvg',
            fidAvg: 'fidAvg',
            openCount: '_count'
          } as any
        )[orderBy || 'fpAvg'];
        const performanceAggs = {
          fpAvg: {
            avg: {
              field: 'fp'
            }
          },
          fmpAvg: {
            avg: {
              field: 'fmp'
            }
          },
          fidAvg: {
            avg: {
              field: 'fid'
            }
          },
          total_buckets: {
            cardinality: {
              field: 'baseInfo.serviceId'
            }
          }
        };
        newEsAgg.product.aggs = performanceAggs;
        oldEsAgg.product.aggs = performanceAggs;
        handlerItem = (item: any) => {
          return {
            serviceId: item.key,
            openCount: item.doc_count,
            fpAvg: item.fpAvg.value,
            fidAvg: item.fidAvg.value,
            fmpAvg: item.fmpAvg.value
          };
        };
        break;
      case 2:
        LOG_INDEX = API_INDEX;
        sortBy = (
          {
            total: 'count',
            averageResponseTime: 'averageResponseTime',
            maxResponseTime: 'maxResponseTime',
            minResponseTime: 'minResponseTime'
          } as any
        )[orderBy || 'averageResponseTime'];
        const appPerformanceAgg = {
          apiCount: {
            value_count: {
              field: 'regUrl.keyword'
            }
          },
          averageResponseTime: {
            avg: {
              field: 'responseTime'
            }
          },
          maxResponseTime: {
            max: {
              field: 'responseTime'
            }
          },
          minResponseTime: {
            min: {
              field: 'responseTime'
            }
          }
        };
        newEsAgg.product.aggs = appPerformanceAgg;
        oldEsAgg.product.aggs = appPerformanceAgg;

        handlerItem = (item: any) => {
          return {
            serviceId: item.key,
            apiCount: item.apiCount.value,
            averageResponseTime: item.averageResponseTime.value,
            maxResponseTime: item.maxResponseTime.value,
            minResponseTime: item.minResponseTime.value
          };
        };
        break;
      case 3:
        LOG_INDEX = PAGE_INDEX;
        sortBy = (
          {
            pv: 'pv._count',
            errCount: 'errCount'
          } as any
        )[orderBy || 'pv'];
        const pageAggs = {
          errCount: {
            value_count: {field: 'error.message'}
          },
          pv: {
            filter: {
              match: {
                type: 'monitor-pv'
              }
            }
          }
        };
        newEsAgg.product.aggs = pageAggs;
        oldEsAgg.product.aggs = pageAggs;
        handlerItem = (item: any) => {
          return {
            serviceId: item.key,
            pv: item.pv.doc_count,
            errCount: item.errCount.value
          };
        };
        break;
      case 4:
        LOG_INDEX = API_INDEX;
        sortBy = (
          {
            totalCount: '_count',
            errCount: 'errCount.sum',
            errPercent: 'errPercent'
          } as any
        )[orderBy || 'totalCount'];
        const apiAggs = {
          filter_collect_true: {
            filter: {
              term: {
                'responseError.collect': true
              }
            },
            aggs: {
              errCount: {
                stats: {field: 'isError'}
              }
            }
          }
        };
        newEsAgg.product.aggs = apiAggs;
        oldEsAgg.product.aggs = apiAggs;
        handlerItem = (item: any) => {
          return {
            serviceId: item.key,
            totalCount: item.doc_count,
            errPercent: item.filter_collect_true.errCount.sum / item.doc_count,
            errCount: item.filter_collect_true.errCount.sum
          };
        };
        break;
    }

    let newEsServiceRes = null;
    let oldEsServiceRes = null;
    if (newEsService.length) {
      newEsServiceRes = await newEsClient.search(`${LOG_INDEX}`, {
        size: 0,
        body: {
          query: newESquery,
          track_total_hits: true,
          aggs: newEsAgg
        }
      });
    }
    if (oldEsService.length) {
      oldEsServiceRes = await oldEsClient.search(`${LOG_INDEX}`, {
        size: 0,
        body: {
          query: oldEsQuery,
          track_total_hits: true,
          aggs: oldEsAgg
        }
      });
    }

    const newEsServiceTotal = newEsServiceRes
      ? newEsServiceRes.body.aggregations.total.value
      : 0;
    const oldEsServiceTotal = oldEsServiceRes
      ? oldEsServiceRes.body.aggregations.total.value
      : 0;
    const newEsServiceItems = newEsServiceRes
      ? newEsServiceRes.body.aggregations.product.buckets.map((item: any) =>
          handlerItem(item)
        )
      : [];
    const oldEsServiceItem = oldEsServiceRes
      ? oldEsServiceRes.body.aggregations.product.buckets.map((item: any) =>
          handlerItem(item)
        )
      : [];
    // 后端合并新旧集群数据后，手动排序
    const result = this.sortResultItems(
      [...newEsServiceItems, ...oldEsServiceItem],
      sortBy,
      order
    );
    return {
      items: result,
      total: newEsServiceTotal + oldEsServiceTotal
    };
  }

  static async feedbackSave(rootDirName: string, body: any) {
    try {
      const feedback = (
        typeof body === 'string' ? JSON.parse(body) : body
      ) as FeedbackInfo;

      const baseInfo = feedback['feedback-base-info'] as BaseInfo;
      const satisfyInfo = feedback['feedback-satisfy-info'] as SatisfyInfo;
      satisfyLoggers(rootDirName).info({
        ...satisfyInfo,
        timeStamp: Date.now(),
        baseInfo
      });
    } catch (err) {
      console.log(err);
    }
  }
}
