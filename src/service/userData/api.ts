import {mapping} from '../../mapping/api-log';
import {ESClient, redisClient} from '../../client';
import axios from 'axios';
import {API_INDEX, PAGE_INDEX, DEFAULT_CLIENT} from '../../utils';
import {cloneDeep} from 'lodash';
import moment from 'moment';
import {Op} from 'sequelize';
import {ApiExemptionRules, AppManage as AppManageDTO} from '../../model';
import {
  getRules,
  exemptionValidate,
  refreshRedisRules,
  isUrlMatch
} from '../../utils/apiExemptionRulesUtil';

interface ApiQuery {
  page?: string | number;
  perPage?: string | number;
  apiPath?: string;
  pagePath?: string;
  reqId?: string;
  userId?: string;
  status?: number | string;
  method?: string;
  region?: string;
  apiPrefix?: string;
  pagePrefix?: string;
  id?: string;
  error?: boolean | string;
  orderBy?: string;
  order?: string;
  serviceId?: string;
  env?: string;
  startTime?: string;
  endTime?: string;
  errorMessage?: string;
  jumpTicket?: boolean;
  [prop: string]: any;
}
interface ApiAggQuery {
  aggType: string;
  perPage?: string | number;
  page?: string | number;
  serviceId?: string;
  keyword?: string;
  orderBy?: string;
  order?: string;
  env?: string;
  value?: string;
  startTime?: number;
  endTime?: number;
  [prop: string]: any;
}

export class Api {
  // 根据服务，获取对应的Client类
  static getClient(esType: string) {
    if (esType) {
      return ESClient[esType];
    }
    return ESClient[DEFAULT_CLIENT];
  }
  /*
    获取api索引的mapping
  */
  static async getMapping(esType: string) {
    const esClient = this.getClient(esType);
    const mapping: any = await esClient.getMapping(API_INDEX);
    const setting: any = await esClient.getSetting(API_INDEX);
    return {
      mapping: mapping.body[API_INDEX],
      setting
    };
  }

  static async clearIndex(esType: string) {
    const esClient = this.getClient(esType);
    await esClient.clear(API_INDEX);
  }
  /*
    获取api索引
  */
  static async setMapping(esType: string) {
    await this.clearIndex(esType);
    const result: any = await axios({
      url: 'http://************:8903/' + API_INDEX,
      method: 'PUT',
      data: {
        mappings: {
          ...mapping
        }
      }
    });
    return result;
  }

  /*
    api日志条件查询
  */
  static async getData(esType: string, query: ApiQuery) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        must: [],
        filter: [],
        must_not: []
      }
    };
    let {
      page,
      perPage,
      startTime,
      endTime,
      apiPrefix,
      pagePrefix,
      apiPath,
      pagePath,
      reqId,
      userId,
      status,
      method,
      region,
      id,
      error,
      errorMessage,
      jumpTicket,
      orderBy,
      order,
      serviceId,
      env,
      exempted,
      locale
    } = query;
    page = parseInt(page as string, 10) || 1;
    perPage = parseInt(perPage as string, 10) || 10;
    status && (status = parseInt(status as string, 10));

    // 豁免的api
    if (exempted !== undefined) {
      esquery.bool.must_not.push({term: {'responseError.collect': exempted}});
    }

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (locale) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.locale': locale
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    if (errorMessage !== undefined) {
      // 查询语句同时包含must（filter、must_not）和should时，可以不满足should的条件，因为must条件优先级高于should
      // 有两种方法可以解决，一种是用mustd对should进行包裹，另一种是使用minimum_should_match 参数
      esquery.bool.should = [
        {
          term: {
            'responseError.message.global.keyword': errorMessage
          }
        }
      ];
      esquery.bool.minimum_should_match = 1;
    }
    // 指定reqid查询，同时忽略除了时间以外其它字段的查询
    if (reqId) {
      esquery.bool.filter.push({
        term: {
          'responseHeader.requestId': reqId
        }
      });
    } else if (id) {
      // 指定id查询
      esquery.bool.filter.push({
        // 分词查询
        term: {
          _id: id
        }
      });
    } else {
      // 指定api路径前缀查询
      if (apiPrefix) {
        esquery.bool.filter.push({
          // 分词查询
          prefix: {
            'completeUrl.keyword': apiPrefix
          }
        });
      }
      // 指定api路径查询
      if (apiPath) {
        esquery.bool.filter.push({
          // 分词查询
          term: {
            'regUrl.keyword': apiPath
          }
        });
      }
      // 页面路径前缀查询
      if (pagePrefix) {
        esquery.bool.filter.push({
          // 分词查询
          prefix: {
            'baseInfo.href.keyword': pagePrefix
          }
        });
      }
      // 指定页面路径查询
      if (pagePath) {
        esquery.bool.filter.push({
          // 分词查询
          term: {
            'baseInfo._href.keyword': pagePath
          }
        });
      }
      // 指定用户查询
      if (userId) {
        esquery.bool.filter.push({
          term: {
            'baseInfo.userId': userId
          }
        });
      }
      // 指定请求method查询
      if (method) {
        esquery.bool.filter.push({
          term: {
            method: method
          }
        });
      }
      // 根据成功or失败查询
      if (error === true || error === 'true') {
        esquery.bool.filter.push({
          term: {
            isError: true
          }
        });
      } else if (error === false || error === 'false') {
        esquery.bool.filter.push({
          term: {
            isError: false
          }
        });
      }
      // 根据是否跳转工单查询
      if (jumpTicket) {
        esquery.bool.filter.push({
          term: {
            jumpTicket: jumpTicket
          }
        });
      }
      // 指定请求状态码查询
      if (status) {
        esquery.bool.filter.push({
          term: {
            status: status
          }
        });
      }
      // 指定地域查询
      if (region) {
        esquery.bool.filter.push({
          // 分词查询
          term: {
            'requestHeader.X-Region': region
          }
        });
      }
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const sortBy = (
      {
        timeStamp: timeField,
        responseTime: 'responseTime'
      } as any
    )[orderBy || 'timeStamp'];
    const result: any = await esClient.search(API_INDEX, {
      size: perPage,
      from: (page - 1) * perPage,
      body: {
        query: esquery,
        track_total_hits: true,
        sort: {
          [sortBy]: {
            order: order || 'desc'
          }
        }
      }
    });
    const items: Array<any> = result.body.hits.hits.map((item: any) => ({
      id: item._id,
      ...item._source
    }));
    let total = result.body.hits.total;
    if (typeof total !== 'number' && typeof total?.value === 'number') {
      total = total.value;
    }
    return {
      total: total > 10000 ? 10000 : total,
      items
    };
  }

  /*
    api日志聚合查询
  */
  static async getAggsData(esType: string, query: ApiAggQuery) {
    const esClient = this.getClient(esType);
    // 聚合类型
    const esquery: any = {
      bool: {
        must: [],
        filter: []
      }
    };
    let {
      aggType,
      perPage,
      page,
      serviceId,
      keyword,
      orderBy,
      order,
      env,
      locale,
      startTime,
      endTime,
      errStatus,
      isFullData,
      userId,
      apiUrl,
      apiPage,
      exempted,
      errorMessage
    } = query;
    // 增加查询被豁免的接口. 默认是false,查未被豁免的，可以传true 查询被豁免的。
    if (exempted === undefined || exempted === 'false') {
      exempted = false;
    }

    page = parseInt(page as string, 10) || 1;
    perPage = parseInt(perPage as string, 10) || 10;

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (locale) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.locale': locale
        }
      });
    }

    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esquery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }

    if (errorMessage !== undefined) {
      esquery.bool.filter.push({
        wildcard: {
          'responseError.message.global.keyword': `*${errorMessage}*`
        }
      });
    }

    let aggsTotalField;
    switch (aggType) {
      case 'api':
        aggsTotalField = 'regUrl.keyword';
        break;
      case 'page':
        aggsTotalField = 'baseInfo._href.keyword';
        break;
      case 'region':
        aggsTotalField = 'requestHeader.X-Region';
        break;
      case 'user':
        aggsTotalField = 'baseInfo.userId';
        break;
      case 'error':
        aggsTotalField = 'responseError.message.global.keyword';
        break;
      default:
        aggsTotalField = 'regUrl.keyword';
        break;
    }

    if (keyword) {
      esquery.bool.filter.push({
        wildcard: {
          [aggsTotalField]: `*${keyword}*`
        }
      });
    }

    // errApiQuery用于查询报错数据，exempted不传默认查未豁免的，传true查被豁免的
    const errApiQuery = cloneDeep(esquery);
    errApiQuery.bool.must.push({
      term: {
        isError: true
      }
    });
    errApiQuery.bool.must.push({
      term: {
        'responseError.collect': !exempted
      }
    });

    if (aggType === 'error') {
      const item = {
        regexp: {
          'responseError.message.global': '[\u4e00-\u9fa5]'
        }
      };
      // 报错聚合时，处理错误信息是否合理的状态
      if (
        (errStatus === 'normal' && locale === 'zh-CN') ||
        (errStatus === 'abnormal' && locale === 'en-US')
      ) {
        esquery.bool.must.push(item);
        errApiQuery.bool.must.push(item);
      }

      if (
        (errStatus === 'abnormal' && locale === 'zh-CN') ||
        (errStatus === 'normal' && locale === 'en-US')
      ) {
        esquery.bool.must_not = [item];
        errApiQuery.bool.must_not = [item];
      }
    }

    // 获取总数
    const errorApiRes = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: errApiQuery,
        aggs: {
          total: {
            cardinality: {
              field: aggsTotalField
            }
          }
        }
      }
    });
    const totalNum = errorApiRes?.body?.aggregations?.total?.value;
    if (!totalNum) {
      return {
        items: [],
        total: 0
      };
    }

    const sortBy = (
      {
        count: 'count',
        errCount: 'err_count>collect_err.value',
        errPercent: 'err_percent',
        tickets: 'tickets.sum',
        userCount: 'user_count>users.value'
      } as any
    )[orderBy || 'errCount'];

    // 查询条件中未添加isError、collect条件，保证聚合时候查询的count是全部数据（如果聚合条件是apiPath，那么count就是该接口的所有请求数量）,这样算出来的errPercent才是准确的
    // errCount查询时添加过滤条件，默认查询报错且未被豁免的数据，同时也支持查询报错且被豁免的数据
    // todo: aggsFilter应该包含responseError.collect条件，但是暂时未找到同时写两个过滤条件的写法，后续优化
    const aggsFilter = {
      bool: {
        must: [
          {
            term: {
              isError: true
            }
          },
          {
            term: {
              'responseError.collect': !exempted
            }
          }
        ]
      }
    };
    const aggsContent: any = {
      // 该维度下当前Key的报错率
      err_percent: {
        bucket_script: {
          buckets_path: {
            err_count: 'err_count>collect_err.value',
            count: 'count'
          },
          script: 'params.err_count / params.count'
        }
      },
      // 该维度下当前Key的总数
      count: {
        value_count: {
          field: 'regUrl.keyword'
        }
      },
      // 该维度下当前Key的报错数，默认查询未豁免数据
      err_count: {
        filter: {
          term: {
            'responseError.collect': !exempted
          }
        },
        aggs: {
          collect_err: {
            sum: {
              field: 'isError'
            }
          }
        }
      },
      // 去除该维度下没有错误的数据
      having: {
        bucket_selector: {
          buckets_path: {
            err_count: 'err_count>collect_err.value'
          },
          script: {
            source: 'params.err_count > 0'
          }
        }
      },
      tickets: {
        filter: aggsFilter,
        aggs: {
          errorTicket: {
            sum: {
              field: 'jumpTicket'
            }
          }
        }
      },
      user_count: {
        filter: aggsFilter,
        aggs: {
          users: {
            cardinality: {
              field: 'baseInfo.userId'
            }
          }
        }
      },
      err_code: {
        filter: aggsFilter,
        aggs: {
          codes: {
            terms: {
              field: 'responseError.code.keyword',
              size: 100 // 暂时设置为100
            }
          }
        }
      },
      page_url:
        aggType === 'error'
          ? {
              filter: aggsFilter,
              aggs: {
                urls: {
                  terms: {
                    field: 'baseInfo._href.keyword',
                    size: 100
                  }
                }
              }
            }
          : undefined,
      api_path:
        aggType === 'error'
          ? {
              filter: aggsFilter,
              aggs: {
                paths: {
                  terms: {
                    field: 'regUrl.keyword',
                    size: 1000
                  },
                  aggs: {
                    api_error_code: {
                      terms: {
                        field: 'responseError.code.keyword',
                        size: 100
                      }
                    }
                  }
                }
              }
            }
          : undefined,
      sort: {
        bucket_sort: {
          sort: [
            {
              [sortBy]: {
                order: order || 'desc'
              }
            }
          ]
        }
      }
    };

    if (!isFullData) {
      aggsContent.sort.bucket_sort['from'] = perPage * (page - 1);
      aggsContent.sort.bucket_sort['size'] = perPage;
    }

    const resultRes: any = await esClient.search(API_INDEX, {
      body: {
        query: esquery,
        aggs: {
          group_by: {
            terms: {
              field: aggsTotalField,
              size: 1000 // 这里不能用total，因为query条件不同，设置成total可能会查不全
            },
            aggs: aggsContent
          }
        }
      }
    });
    const resultList = resultRes.body.aggregations.group_by.buckets || [];
    let items: Array<any> = resultList.map((item: any) => {
      const errCode = (item.err_code.codes.buckets || [])
        .map((item: any) => item.key)
        .filter((item: any) => item)
        .join('，');

      const apiInfo = (item.api_path?.paths?.buckets || []).map((path: any) => {
        return {
          key: path.key,
          errCodes: (path?.api_error_code?.buckets || []).map(
            (el: any) => el.key
          )
        };
      });

      return {
        userCount: item.user_count.users.value,
        value: item.key,
        count: item.count.value,
        errCount: item.err_count?.collect_err?.value,
        errPercent: item.err_percent.value.toFixed(6),
        errCode,
        apiInfo,
        pageInfo: (item.page_url?.urls?.buckets || []).map((el: any) => el.key),
        tickets: item.tickets.errorTicket.value
      };
    });

    return {
      items,
      total: totalNum
    };
  }

  static async getApiTrend(esType: string, query: any) {
    const {serviceId, env, startTime, endTime, errorMessage, apiUrl, exempted} =
      query;

    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [
          {
            term: {
              isError: true
            }
          }
        ]
      }
    };

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (errorMessage !== undefined) {
      esquery.bool.should = [
        {
          term: {
            'responseError.message.global.keyword': errorMessage
          }
        }
      ];
      esquery.bool.minimum_should_match = 1;
    }

    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }

    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    if (exempted !== undefined) {
      esquery.bool.must_not = [{term: {'responseError.collect': exempted}}];
    }

    const res = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          group_by_time: {
            date_histogram: {
              field: timeField,
              interval: 'day',
              time_zone: 'Asia/Shanghai',
              format: 'yyyy-MM-dd',
              min_doc_count: 0
            }
          }
        }
      }
    });

    const dailyCount = res?.body?.aggregations?.group_by_time?.buckets || [];

    return dailyCount.map((el: any) => {
      return {
        date: el.key_as_string,
        dateKey: el.key,
        value: el.doc_count
      };
    });
  }

  static async getMessageApiInfoData(esType: string, query: any) {
    const {serviceId, env, startTime, endTime, errorMessage, apiUrl, exempted} =
      query;

    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (errorMessage !== undefined) {
      esquery.bool.should = [
        {
          term: {
            'responseError.message.global.keyword': errorMessage
          }
        }
      ];
      esquery.bool.minimum_should_match = 1;
    }

    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }

    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    if (exempted !== undefined) {
      esquery.bool.must_not = [{term: {'responseError.collect': exempted}}];
    }

    const res = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          group_by: {
            terms: {
              field: 'url.keyword',
              size: 1000
            },
            aggs: {
              group_by: {
                terms: {
                  field: 'responseError.code.keyword',
                  size: 1000
                }
              }
            }
          }
        }
      }
    });

    const dailyCount = res?.body?.aggregations?.group_by?.buckets || [];

    return dailyCount.map((el: any) => {
      return {
        api: el.key,
        codes: el.group_by?.buckets?.map((item: any) => item.key),
        count: el.doc_count
      };
    });
  }
  /*
    api调用量、报错量、报错率
  */
  static async getApiInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const {serviceId, env, startTime, endTime, userId, apiUrl, apiPage} = query;

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esquery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }
    // 只有isError是true才认为是报错接口
    const aggs: any = {
      count_error: {
        filter: {
          term: {'responseError.collect': true}
        },
        aggs: {
          count: {
            stats: {field: 'isError'}
          }
        }
      },
      no_msg_count: {
        filter: {
          term: {'responseError.message.global.keyword': ''}
        },
        aggs: {
          count: {
            stats: {field: 'isError'}
          }
        }
      }
    };
    const result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    const errCount = result?.body?.aggregations?.count_error?.count?.sum;
    const no_msg_count = result?.body?.aggregations?.no_msg_count?.count?.sum;
    let totalCount = result?.body?.hits?.total;
    if (
      typeof totalCount !== 'number' &&
      typeof totalCount?.value === 'number'
    ) {
      totalCount = totalCount.value;
    }
    const tippedErrCount = errCount - no_msg_count;
    const errPercent = Number(errCount / totalCount || 0).toFixed(6);
    const tippedErrPercent = Number(tippedErrCount / errCount).toFixed(6);
    return {
      errCount,
      totalCount,
      tippedErrCount,
      tippedErrPercent,
      errPercent
    };
  }

  static async getApiInfoGap(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfoGap = {
      totalCountGap: '',
      totalCountLast: '',
      errCountGap: '',
      errCountLast: '',
      errPercentGap: '',
      errPercentLast: '',
      tippedErrPercentLast: '',
      tippedErrCountLast: '',
      tippedErrPercentGap: '',
      tippedErrCountGap: ''
    };
    const baseInfo = await this.getApiInfo(esType, query);
    // 计算环比
    if (startTime) {
      const _baseInfo = await this.getApiInfo(esType, {
        ...query,
        startTime: 2 * startTime - endTime,
        endTime: startTime
      });
      const {
        totalCount,
        errCount,
        errPercent,
        tippedErrCount,
        tippedErrPercent
      } = _baseInfo;
      baseInfoGap.totalCountGap = totalCount
        ? ((baseInfo.totalCount - totalCount) / totalCount).toFixed(6)
        : '';
      baseInfoGap.errCountGap = errCount
        ? ((baseInfo.errCount - errCount) / errCount).toFixed(6)
        : '';
      baseInfoGap.errPercentGap = Number(errPercent)
        ? (
            (Number(baseInfo.errPercent) - Number(errPercent)) /
            Number(errPercent)
          ).toFixed(6)
        : '';
      baseInfoGap.totalCountLast = totalCount;
      baseInfoGap.errCountLast = errCount;
      baseInfoGap.errPercentLast = errPercent;
      baseInfoGap.tippedErrCountLast = tippedErrCount + '';
      baseInfoGap.tippedErrPercentLast = tippedErrPercent;

      baseInfoGap.tippedErrCountGap = Number(tippedErrCount)
        ? (
            (Number(baseInfo.tippedErrCount) - Number(tippedErrCount)) /
            Number(tippedErrCount)
          ).toFixed(6)
        : '';

      baseInfoGap.tippedErrPercentGap = Number(tippedErrPercent)
        ? (
            (Number(baseInfo.tippedErrPercent) - Number(tippedErrPercent)) /
            Number(tippedErrPercent)
          ).toFixed(6)
        : '';
    }
    return {
      ...baseInfo,
      ...baseInfoGap
    };
  }

  /*
    api天粒度的调用量、报错量、报错率
  */
  static async getDailyApiInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const {serviceId, env, startTime, endTime, userId, apiUrl, apiPage} = query;

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esquery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }
    // 基于日期聚合后再基于isError字段聚合
    const daily_error_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          count_error: {
            filter: {
              term: {'responseError.collect': true}
            },
            aggs: {
              count: {
                stats: {field: 'isError'}
              }
            }
          },
          no_msg_count: {
            filter: {
              term: {'responseError.message.global.keyword': ''}
            },
            aggs: {
              count: {
                stats: {field: 'isError'}
              }
            }
          }
        }
      }
    };

    // 一段时间内每天的报错信息
    const daily_error_result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: daily_error_aggs,
        track_total_hits: true
      }
    });
    const items: Array<any> =
      daily_error_result.body.aggregations.group_by_time.buckets.map(
        (item: any) => {
          const errCount = item.count_error.count.sum;
          const noMSgCount = item.no_msg_count.count.sum;

          const totalCount = item.doc_count;
          const time = item.key_as_string;
          const errPercent = Number(errCount / totalCount || 0).toFixed(6);
          const tippedErrCount = errCount - noMSgCount;
          const tippedErrPercent = Number(
            tippedErrCount / errCount || 0
          ).toFixed(6);

          return {
            time,
            errCount,
            totalCount,
            tippedErrCount,
            tippedErrPercent,
            errPercent
          };
        }
      );
    return {
      items
    };
  }

  /*
    api日志按报错聚合查询
  */
  static async getApiErrorAggsData(esType: string, query: ApiAggQuery) {
    const esClient = this.getClient(esType);
    // 聚合类型
    const esquery: any = {
      bool: {
        filter: [
          {
            term: {
              isError: true
            }
          }
        ],
        must_not: []
      }
    };
    let {
      value,
      startTime,
      endTime,
      aggType,
      perPage,
      page,
      serviceId,
      orderBy,
      order,
      env,
      userId,
      apiUrl,
      apiPage,
      exempted
    } = query;

    page = parseInt(page as string, 10) || 1;
    perPage = parseInt(perPage as string, 10) || 10;

    // 只查询豁免的api
    if (exempted !== undefined) {
      esquery.bool.must_not.push({term: {'responseError.collect': exempted}});
    }

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esquery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }

    let aggsTotalField;
    switch (aggType) {
      case 'api':
        aggsTotalField = 'regUrl.keyword';
        break;
      case 'page':
        aggsTotalField = 'baseInfo._href.keyword';
        break;
      case 'region':
        aggsTotalField = 'requestHeader.X-Region';
        break;
      case 'user':
        aggsTotalField = 'baseInfo.userId';
        break;
      case 'error':
        aggsTotalField = 'responseError.message.global.keyword';
        break;
      default:
        aggsTotalField = 'regUrl.keyword';
        break;
    }
    esquery.bool.filter.push({
      term: {
        [aggsTotalField]: value
      }
    });

    const totalRes: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          total: {
            cardinality: {
              field: 'responseError.message.global.keyword'
            }
          }
        }
      }
    });

    const total = totalRes.body?.aggregations?.total?.value;

    if (!total) {
      return {
        items: [],
        total: 0
      };
    }

    const sortBy = (
      {
        errCount: 'err_count.sum',
        tickets: 'tickets.sum'
      } as any
    )[orderBy || 'errCount'];

    const aggsContent = {
      err_count: {
        stats: {
          field: 'isError'
        }
      },
      tickets: {
        stats: {
          field: 'jumpTicket'
        }
      },
      sort: {
        bucket_sort: {
          sort: [
            {
              [sortBy]: {
                order: order || 'desc'
              }
            }
          ],
          from: perPage * (page - 1),
          size: perPage
        }
      }
    };

    const resultRes: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          group_by: {
            terms: {
              field: 'responseError.message.global.keyword',
              size: total
            },
            aggs: aggsContent
          }
        }
      }
    });

    const resultList = resultRes.body?.aggregations?.group_by?.buckets || [];

    const items: Array<any> = resultList.map((item: any) => {
      return {
        value: item.key,
        errCount: item.err_count.sum,
        tickets: item.tickets.sum
      };
    });
    return {
      items,
      total
    };
  }

  /*
    api接口报错信息 不同语言环境下的报错数、不合理报错数、不合理报错率
  */
  static async getApiLocaleInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [
          {
            term: {
              'responseError.collect': true
            }
          },
          {
            term: {
              isError: true
            }
          }
        ],
        should: [],
        must: []
      }
    };
    const {
      serviceId,
      env,
      startTime,
      endTime,
      locale,
      userId,
      apiUrl,
      apiPage
    } = query;

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esquery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }

    // 语言环境下不合理报错数
    const locale_result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: {
          lang: {
            terms: {
              field: 'baseInfo.locale'
            },
            aggs: {
              count: {
                filter: {
                  regexp: {
                    // todo 目前根据中文进行正则匹配过滤
                    'responseError.message.global': '[\u4e00-\u9fa5]'
                  }
                }
              },
              empty_count: {
                filter: {
                  term: {
                    'responseError.message.global.keyword': ''
                  }
                }
              }
            }
          }
        }
      }
    });

    const localObj: any = {};
    // todo 目前聚合条件只能算出中文和英文环境不合理报错数
    locale_result?.body?.aggregations?.lang?.buckets?.forEach((item: any) => {
      if (item.key === 'zh-CN') {
        const errCount =
          item.doc_count - item.count.doc_count - item.empty_count.doc_count;
        localObj[item.key] = {
          totalCount: item.doc_count,
          errCount,
          errPercent: Number(errCount / item.doc_count || 0).toFixed(6)
        };
      }
      if (item.key === 'en-US') {
        const errCount = item.count.doc_count - item.empty_count.doc_count;
        localObj[item.key] = {
          totalCount: item.doc_count,
          errCount: errCount,
          errPercent: Number(errCount / item.doc_count || 0).toFixed(6)
        };
      }
    });

    return {
      ...(localObj[locale]
        ? localObj[locale]
        : {totalCount: 0, errCount: 0, errPercent: 0})
    };
  }

  static async getApiLocaleInfoGap(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfoGap = {
      totalCountGap: '',
      totalCountLast: '',
      errCountGap: '',
      errCountLast: '',
      errPercentGap: '',
      errPercentLast: ''
    };
    const baseInfo = await this.getApiLocaleInfo(esType, query);
    // 计算环比
    if (startTime) {
      const _baseInfo = await this.getApiLocaleInfo(esType, {
        ...query,
        startTime: 2 * startTime - endTime,
        endTime: startTime
      });
      const {totalCount, errCount, errPercent} = _baseInfo;
      baseInfoGap.totalCountGap = totalCount
        ? ((baseInfo.totalCount - totalCount) / totalCount).toFixed(6)
        : '';
      baseInfoGap.errCountGap = errCount
        ? ((baseInfo.errCount - errCount) / errCount).toFixed(6)
        : '';
      baseInfoGap.errPercentGap = Number(errPercent)
        ? (
            (Number(baseInfo.errPercent) - Number(errPercent)) /
            Number(errPercent)
          ).toFixed(6)
        : '';
      baseInfoGap.totalCountLast = totalCount;
      baseInfoGap.errCountLast = errCount;
      baseInfoGap.errPercentLast = errPercent;
    }
    return {
      ...baseInfo,
      ...baseInfoGap
    };
  }

  /*
    api接口报错信息 不同语言环境下的天粒度的报错数、不合理报错数、不合理报错率
  */
  static async getDailyApiLocaleInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [
          {
            term: {
              'responseError.collect': true
            }
          },
          {
            term: {
              isError: true
            }
          }
        ],
        must: []
      }
    };
    const {
      serviceId,
      env,
      startTime,
      endTime,
      locale,
      userId,
      apiUrl,
      apiPage
    } = query;

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esquery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esquery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }
    // 基于日期聚合后再基于isError字段聚合
    const daily_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          lang: {
            terms: {
              field: 'baseInfo.locale'
            },
            aggs: {
              count: {
                filter: {
                  regexp: {
                    // todo 目前根据中文进行正则匹配过滤
                    'responseError.message.global': '[\u4e00-\u9fa5]'
                  }
                }
              },
              empty_count: {
                filter: {
                  term: {
                    'responseError.message.global.keyword': ''
                  }
                }
              }
            }
          }
        }
      }
    };

    // 一段时间内每天的报错信息
    const daily_locale_result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: daily_aggs,
        track_total_hits: true
      }
    });
    const items: Array<any> =
      daily_locale_result.body.aggregations.group_by_time.buckets.map(
        (timeItem: any) => {
          let localObj: any = {};
          timeItem.lang?.buckets?.forEach((item: any) => {
            if (item.key === 'zh-CN') {
              const errCount =
                item.doc_count -
                item.count.doc_count -
                item.empty_count.doc_count;
              localObj[item.key] = {
                totalCount: item.doc_count,
                errCount,
                errPercent: Number(errCount / item.doc_count || 0).toFixed(6)
              };
            }
            if (item.key === 'en-US') {
              const errCount =
                item.count.doc_count - item.empty_count.doc_count;
              localObj[item.key] = {
                totalCount: item.doc_count,
                errCount: item.count.doc_count - item.empty_count.doc_count,
                errPercent: Number(errCount / item.doc_count || 0).toFixed(6)
              };
            }
          });
          return {
            time: timeItem.key_as_string,
            ...(localObj[locale]
              ? localObj[locale]
              : {totalCount: 0, errCount: 0, errPercent: 0})
          };
        }
      );
    return {
      items
    };
  }

  static async getStatisticsInfo(esType: string, body: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        should: [],
        minimum_should_match: 1
      }
    };
    let {
      appIds,
      env,
      locale = 'zh-CN',
      startTime,
      endTime,
      orderBy,
      orderDir
    } = body;

    let serviceIds: any = [];
    let serviceKeyMap: any = {};
    if (appIds && typeof appIds === 'string') {
      serviceIds = appIds.split(',');
    } else if (Array.isArray(appIds) && appIds.length) {
      serviceIds = appIds.map((item: any) => item.value);
      appIds.forEach((item: any) => {
        serviceKeyMap[item.value] = item.label;
        serviceIds.push(item.value);
      });
    }

    if (serviceIds.length) {
      serviceIds.forEach((serviceId: any) => {
        esquery.bool.should.push({
          term: {
            'baseInfo.serviceId': serviceId
          }
        });
      });
    } else {
      return {
        items: [],
        total: 0
      };
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    // 语言环境下不合理报错数

    const timeField = esType ? 'timeStamp' : '@timestamp';
    const locale_result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: {
          bool: {
            ...esquery.bool,
            must: [
              {
                exists: {
                  field: 'responseError.message.global.keyword'
                }
              }
            ],
            must_not: [
              {
                term: {
                  'responseError.message.global.keyword': ''
                }
              },
              {
                term: {
                  'responseError.collect': false
                }
              }
            ]
          }
        },
        track_total_hits: true,
        aggs: {
          group_by: {
            terms: {
              field: 'baseInfo.serviceId',
              size: serviceIds.length
            },
            aggs: {
              time_range: {
                range: {
                  field: timeField,
                  ranges: [
                    {
                      key: 'last',
                      from: 2 * startTime - endTime,
                      to: startTime
                    },
                    {
                      key: 'now',
                      from: startTime,
                      to: endTime
                    }
                  ]
                },
                aggs: {
                  lang: {
                    terms: {
                      field: 'baseInfo.locale'
                    },
                    aggs: {
                      count: {
                        filter: {
                          regexp: {
                            // todo 目前根据中文进行正则匹配过滤
                            'responseError.message.global': '[\u4e00-\u9fa5]'
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    });

    const baseInfo: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: {
          group_by: {
            terms: {
              field: 'baseInfo.serviceId',
              size: serviceIds.length
            },
            aggs: {
              time_range: {
                range: {
                  field: timeField,
                  ranges: [
                    {
                      key: 'last',
                      from: 2 * startTime - endTime,
                      to: startTime
                    },
                    {
                      key: 'now',
                      from: startTime,
                      to: endTime
                    }
                  ]
                },
                aggs: {
                  lang: {
                    terms: {
                      field: 'baseInfo.locale'
                    },
                    aggs: {
                      uv: {
                        cardinality: {
                          field: 'baseInfo.userId'
                        }
                      },
                      pv: {
                        filter: {
                          match: {
                            type: 'monitor-pv'
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    });

    const localeArr: any = [];
    // todo 目前聚合条件只能算出中文和英文环境不合理报错数
    locale_result?.body?.aggregations?.group_by?.buckets?.forEach(
      (serviceItem: any) => {
        const lastBuckets = serviceItem?.time_range?.buckets.filter(
          (item: any) => item.key === 'last'
        )[0]?.lang?.buckets;

        const nowBuckets = serviceItem?.time_range?.buckets.filter(
          (item: any) => item.key === 'now'
        )[0]?.lang?.buckets;

        lastBuckets?.forEach((lastItem: any) => {
          nowBuckets?.forEach((nowItem: any) => {
            if (lastItem.key === nowItem.key) {
              let obj: any = {
                name: serviceKeyMap[serviceItem.key],
                key: serviceItem.key
              };
              if (lastItem.key === 'zh-CN') {
                const errPercentLast = Number(
                  (lastItem.doc_count - lastItem.count.doc_count) /
                    lastItem.doc_count || 0
                ).toFixed(6);
                const errPercent = Number(
                  (nowItem.doc_count - nowItem.count.doc_count) /
                    nowItem.doc_count || 0
                ).toFixed(6);
                const errPercentGap = Number(errPercentLast)
                  ? (
                      (Number(errPercent) - Number(errPercentLast)) /
                      Number(errPercentLast)
                    ).toFixed(6)
                  : '';
                const errCountLast =
                  lastItem.doc_count - lastItem.count.doc_count;
                const errCount = nowItem.doc_count - nowItem.count.doc_count;
                const errCountGap = errCountLast
                  ? ((errCount - errCountLast) / errCountLast).toFixed(6)
                  : '';
                const totalCountGap = lastItem.doc_count
                  ? (
                      (nowItem.doc_count - lastItem.doc_count) /
                      lastItem.doc_count
                    ).toFixed(6)
                  : '';
                obj = {
                  ...obj,
                  totalCountLast: lastItem.doc_count,
                  totalCount: nowItem.doc_count,
                  totalCountGap,
                  errCountLast,
                  errCount,
                  errCountGap,
                  errPercentLast,
                  errPercent,
                  errPercentGap,
                  locale: lastItem.key
                };
              } else if (lastItem.key === 'en-US') {
                const errPercentLast = Number(
                  lastItem.count.doc_count / lastItem.doc_count || 0
                ).toFixed(6);
                const errPercent = Number(
                  nowItem.count.doc_count / nowItem.doc_count || 0
                ).toFixed(6);
                const errPercentGap = Number(errPercentLast)
                  ? (
                      (Number(errPercent) - Number(errPercentLast)) /
                      Number(errPercentLast)
                    ).toFixed(6)
                  : '';
                const errCountGap = lastItem.count.doc_count
                  ? (
                      (nowItem.count.doc_count - lastItem.count.doc_count) /
                      lastItem.count.doc_count
                    ).toFixed(6)
                  : '';
                const totalCountGap = lastItem.doc_count
                  ? (
                      (nowItem.doc_count - lastItem.doc_count) /
                      lastItem.doc_count
                    ).toFixed(6)
                  : '';
                obj = {
                  ...obj,
                  totalCountLast: lastItem.doc_count,
                  totalCount: nowItem.doc_count,
                  totalCountGap,
                  errCountLast: lastItem.count.doc_count,
                  errCount: nowItem.count.doc_count,
                  errCountGap,
                  errPercentLast,
                  errPercent,
                  errPercentGap,
                  locale: lastItem.key
                };
              }
              localeArr.push(obj);
            }
          });
        });
      }
    );

    const baseInfoArr: any = [];
    baseInfo?.body?.aggregations?.group_by?.buckets?.forEach(
      (serviceItem: any) => {
        const lastBuckets = serviceItem?.time_range?.buckets.filter(
          (item: any) => item.key === 'last'
        )[0]?.lang?.buckets;

        const nowBuckets = serviceItem?.time_range?.buckets.filter(
          (item: any) => item.key === 'now'
        )[0]?.lang?.buckets;

        lastBuckets?.forEach((lastItem: any) => {
          nowBuckets?.forEach((nowItem: any) => {
            if (lastItem.key === nowItem.key) {
              const pv = nowItem.pv.doc_count;
              const pvLast = lastItem.pv.doc_count;
              const uv = nowItem.uv.value;
              const uvLast = lastItem.uv.value;
              let obj: any = {
                name: serviceKeyMap[serviceItem.key],
                key: serviceItem.key,
                pv,
                pvLast,
                pvGap: pvLast ? ((pv - pvLast) / pvLast).toFixed(6) : '',
                uv,
                uvLast,
                uvGap: uvLast ? ((uv - uvLast) / uvLast).toFixed(6) : '',
                locale: lastItem.key
              };
              baseInfoArr.push(obj);
            }
          });
        });
      }
    );

    const mergedArray = localeArr.concat(baseInfoArr);
    const result = mergedArray.reduce((acc: any, current: any) => {
      const found = acc.find(
        (item: any) =>
          item['key'] === current['key'] && item['locale'] === current['locale']
      );
      if (found) {
        Object.assign(found, current);
        return acc;
      }
      return acc.concat([current]);
    }, []);

    const sortBy = (
      {
        uvLast: 'uvLast',
        pvLast: 'pvLast',
        totalCountLast: 'totalCountLast',
        errCountLast: 'errCountLast',
        errPercentLast: 'errPercentLast',
        uv: 'uv',
        pv: 'pv',
        totalCount: 'totalCount',
        errCount: 'errCount',
        errPercent: 'errPercent',
        uvGap: 'uvGap',
        pvGap: 'pvGap',
        totalCountGap: 'totalCountGap',
        errCountGap: 'errCountGap',
        errPercentGap: 'errPercentGap'
      } as any
    )[orderBy || 'errPercent'];

    const items = result
      .filter((item: any) => item.locale === locale)
      .sort((next: any, current: any) =>
        orderDir === 'asc'
          ? next[sortBy] - current[sortBy]
          : current[sortBy] - next[sortBy]
      );

    return {
      items
    };
  }

  static async getApiExemptionRules(query: any) {
    let {
      serviceId,
      env,
      pageNo,
      pageSize,
      isFullData,
      href,
      path,
      code,
      message,
      orderBy,
      order,
      keyword
    } = query;
    pageNo = parseInt(pageNo as any, 10) || 1;
    pageSize = parseInt(pageSize as any, 10) || 10;
    switch (orderBy) {
      case 'createdAt':
        orderBy = 'created_at';
        break;
      case 'updatedAt':
        orderBy = 'updated_at';
        break;
      default:
        orderBy = 'created_at';
    }

    switch (order) {
      case 'desc':
        order = 'desc';
        break;
      case 'asc':
        order = 'asc';
        break;
      default:
        order = 'desc';
    }

    let andWhere: any = [
      {
        deleted_at: null
      }
    ];

    serviceId && andWhere.push({service_id: serviceId});
    env && andWhere.push({env});
    href && andWhere.push({href});
    path &&
      andWhere.push({
        path: {
          [Op.like]: path.replace('*', '%').replace('?', '_')
        }
      });
    code && andWhere.push({code});
    message && andWhere.push({message});

    if (keyword) {
      andWhere.push({
        [Op.or]: {
          code: {
            [Op.like]: `%${keyword}%`
          },
          message: {
            [Op.like]: `%${keyword}%`
          },
          path: {
            [Op.like]: `%${keyword}%`
          },
          href: {
            [Op.like]: `%${keyword}%`
          },
          reason: {
            [Op.like]: `%${keyword}%`
          }
        }
      });
    }

    const {count, rows} = await ApiExemptionRules.findAndCountAll({
      where: {
        [Op.and]: andWhere
      },
      order: [[orderBy, order]],
      offset: isFullData ? undefined : (pageNo - 1) * pageSize,
      limit: isFullData ? undefined : pageSize,
      attributes: [
        'id',
        ['service_id', 'serviceId'],
        'href',
        'path',
        'code',
        'message',
        'reason',
        'region',
        'user',
        ['created_by', 'createBy'],
        ['created_at', 'createdAt']
      ]
    });

    return {
      total: count,
      items: rows
    };
  }

  static async addApiExemptionRule(body: any) {
    const {serviceId, env, createdBy, reason, path, href, code, message, user, region} = body;

    if (!path || !env || !serviceId) {
      return {
        success: false,
        msg: '请检查必填参数'
      };
    }

    let rules = await ApiExemptionRules.findAll({
      where: {
        service_id: serviceId,
        env,
        deleted_at: null
      }
    });

    rules = rules.filter(rule => {
      const el = rule.toJSON();
      return isUrlMatch(path, el.path);
    });

    if (exemptionValidate(rules, {href, code, message, region, user})) {
      return {
        success: false,
        msg: '当前规则已在其他规则中生效，无需重复设置。'
      };
    }

    await ApiExemptionRules.create({
      service_id: serviceId,
      env,
      href,
      path,
      code,
      message,
      reason,
      region,
      user,
      created_by: createdBy,
      created_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    await refreshRedisRules(serviceId, env);

    return {
      msg: '添加成功',
      success: true
    };
  }

  static async removeApiExemptionRule(id: number, deletedBy: string) {
    const rule = await ApiExemptionRules.findOne({
      where: {
        id,
        deleted_at: null
      }
    });

    if (!rule) {
      return {
        msg: '该规则不存在或已删除',
        success: false
      };
    }

    rule.update({
      deleted_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      deleted_by: deletedBy
    });

    await rule.save();
    const ruleInfo = rule.get();
    await refreshRedisRules(ruleInfo.service_id, ruleInfo.env);

    return {
      success: true,
      msg: '删除成功'
    };
  }

  static async getErrorMessageCompareDetail(
    esType: string,
    query: ApiAggQuery
  ) {
    const {
      serviceId,
      env,
      startTime,
      endTime,
      errMessageNum = 100,
      compareStartTime,
      compareEndTime
    } = query;

    const curQuery = {
      aggType: 'error',
      serviceId,
      env,
      startTime,
      endTime,
      perPage: errMessageNum,
      page: 1
    };
    const compareQuery = {
      aggType: 'error',
      serviceId,
      env,
      startTime: compareStartTime,
      endTime: compareEndTime,
      perPage: errMessageNum,
      page: 1
    };
    const compareRes = await Promise.all([
      this.getAggsData(esType, curQuery),
      this.getAggsData(esType, compareQuery)
    ]);
    // 对比上周期新出现的报错
    const newError = [];
    // 对比上周期上升的报错
    const riseError = [];
    // 对比上周期下降或者报错数量无变化的的报错
    const fallError = [];
    const curTimeRangeErrorList = compareRes[0].items;
    const lastTimeRangeErrorList = compareRes[1].items;
    for (let i = 0; i < curTimeRangeErrorList.length; i++) {
      const item = curTimeRangeErrorList[i];
      const lastTimeRangeErrorIndex = lastTimeRangeErrorList.findIndex(
        errorInfo => errorInfo.value === item.value
      );
      const lastTimeRangeError =
        lastTimeRangeErrorList[lastTimeRangeErrorIndex];
      // 上周期也存在该报错
      if (lastTimeRangeError) {
        lastTimeRangeErrorList.splice(lastTimeRangeErrorIndex, 1);
        if (lastTimeRangeError.errCount >= item.errCount) {
          // 上周期报错数量大于当前周期报错数量，表示本周期下降了或者没变化
          fallError.push({
            type: 'fall',
            value: item.value,
            cur: item.errCount,
            last: lastTimeRangeError.errCount
          });
        } else {
          // 上周期报错数量小于当前周期报错数量，表示本周期上升了
          riseError.push({
            type: 'rise',
            value: item.value,
            cur: item.errCount,
            last: lastTimeRangeError.errCount
          });
        }
      } else {
        newError.push({
          type: 'new',
          value: item.value,
          cur: item.errCount,
          last: 0
        });
      }
    }
    // 循环结束后，剩余的报错都是上周报错但是本周消失的
    const disappearError = lastTimeRangeErrorList.map(item => {
      return {
        type: 'disappear',
        value: item.value,
        cur: 0,
        last: item.errCount
      };
    });
    const result = [...newError, ...riseError, ...fallError, ...disappearError];

    return result;
  }
  static async getApiTodayInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const { startTime, endTime, env, serviceId } = query;
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: Number(startTime),
            lte: Number(endTime)
          }
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    const ApiAgg = {
      count: {
        value_count: {
          field: 'regUrl.keyword'
        }
      },
      err_count: {
        filter: {
          term: { 'responseError.collect': true }
        },
        aggs: {
          count: {
            stats: { field: 'isError' }
          }
        }
      }
    };

    const PageAgg = {
      pv: {
        filter: {
          match: {
            type: 'monitor-pv'
          }
        }
      },
      uv: {
        cardinality: {
          field: 'baseInfo.userId'
        }
      },
    };

    const ApiResult: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: ApiAgg
      }
    });

    const PageResult: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: PageAgg
      }
    });
    const errRate = (ApiResult.body.aggregations.err_count.count.sum * 100 / ApiResult.body.hits.total.value) .toFixed(6);
    const result = {
      total: ApiResult.body.hits.total.value,
      errCount: ApiResult.body.aggregations.err_count.count.sum,
      pv: PageResult.body.aggregations.pv.doc_count,
      uv: PageResult.body.aggregations.uv.value,
      errRate
    }

    return {
      result,
      ApiResult
    };
  }

  static async getApiInfoTrend(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const { startTime, endTime, env, serviceId } = query;
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: Number(startTime),
            lte: Number(endTime)
          }
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeType = endTime - startTime > 24 * 60 * 60 * 1000 ? 'day' : 'hour';
    const aggs = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: timeType,
          time_zone: 'Asia/Shanghai',
          format: timeType === 'day' ? 'yyyy-MM-dd' : 'HH',
        },
        aggs: {
          count: {
            value_count: {
              field: 'regUrl.keyword'
            }
          },
          err_count: {
            filter: {
              term: { 'responseError.collect': true }
            },
            aggs: {
              count: {
                stats: { field: 'isError' }
              }
            }
          }
        }
      }
    };

    const result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });

    const items = result.body.aggregations.group_by_time.buckets.map((item: any) => ({
      key: item.key_as_string,
      count: item.count.value,
      errCount: item.err_count.count.sum,
      errRate: (item.err_count.count.sum / item.count.value).toFixed(6),
    }));

    return {
      result: items
    };
  }

  static async getApiInfoTrendTotalSearch(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const { startTime, endTime, env, serviceId, appTags } = query;
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: Number(startTime),
            lte: Number(endTime)
          }
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const andWhere = [];
    if (appTags && appTags.length) {
      const tagsOp = appTags.split(',') || [];

      andWhere.push({
        tags: { [Op.in]: tagsOp }
      });
    }
    const appResult = await AppManageDTO.findAndCountAll({
      attributes: [
        ['service_id', 'serviceId'],
      ],
      where: {
        [Op.and]: andWhere
      },
    });
    const serviceIds = appResult?.rows?.map((item: any) => item.dataValues.serviceId);
    esquery.bool.filter.push({
      terms: {
        'baseInfo.serviceId': serviceIds
      }
    });
    const aggs = {
      count: {
        value_count: {
          field: 'regUrl.keyword'
        }
      },
      err_count: {
        filter: {
          term: { 'responseError.collect': true }
        },
        aggs: {
          count: {
            stats: { field: 'isError' }
          }
        }
      }
    };

    const result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    const count = result.body.hits.total.value;
    const errCount = result.body.aggregations.err_count.count.sum;
    return {
      count,
      errCount,
      errRate: (100 * errCount / count).toFixed(4)
    }
  }

  static async getApiInfoTrendTotal(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfo: any = await this.getApiInfoTrendTotalSearch(esType, query);
    // 计算环比
    const _baseInfo: any = await this.getApiInfoTrendTotalSearch(esType, {
      ...query,
      startTime: 2 * startTime - endTime,
      endTime: startTime
    });
    return {
      count: {
        current: baseInfo.count,
        compare: _baseInfo.count,
        compareRate: Number((100 * (baseInfo.count - _baseInfo.count) / _baseInfo.count).toFixed(2))
      },
      errCount: {
        current: baseInfo.errCount,
        compare: _baseInfo.errCount,
        compareRate: Number((100 * (baseInfo.errCount - _baseInfo.errCount) / _baseInfo.errCount).toFixed(2))
      },
      errRate: {
        current: baseInfo.errRate,
        compare: _baseInfo.errRate,
        compareRate: Number((100 * (baseInfo.errRate - _baseInfo.errRate) / _baseInfo.errRate).toFixed(2))
      }
    };
  }

  static async getApiTop(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: []
      }
    };
    const { startTime, endTime, env, type, appTags } = query;
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: Number(startTime),
            lte: Number(endTime)
          }
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const andWhere = [];
    if (appTags && appTags.length) {
      const tagsOp = appTags.split(',') || [];

      andWhere.push({
        tags: { [Op.in]: tagsOp }
      });
    }
    const appResult = await AppManageDTO.findAndCountAll({
      attributes: [
        ['service_id', 'serviceId'],
      ],
      where: {
        [Op.and]: andWhere
      },
    });
    const serviceIds = appResult?.rows?.map((item: any) => item.dataValues.serviceId);
    esquery.bool.filter.push({
      terms: {
        'baseInfo.serviceId': serviceIds
      }
    });

    const typeAgg: any = {
      count: {
        value: {
          value_count: {
            field: 'regUrl.keyword'
          }
        }
      },
      errPercent: {
        value: {
          bucket_script: {
            buckets_path: {
              err_count: 'err_count>collect_err.value',
              count: 'count'
            },
            script: 'params.err_count / params.count'
          }
        },
        err_count: {
          filter: {
            term: {
              'responseError.collect': true
            }
          },
          aggs: {
            collect_err: {
              sum: {
                field: 'isError'
              }
            }
          }
        },
        count: {
          value_count: {
            field: 'regUrl.keyword'
          }
        }
      },
    }

    const totalRes: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          total: {
            cardinality: {
              field: 'baseInfo.serviceId'
            }
          }
        }
      }
    });
    const total = totalRes.body?.aggregations?.total?.value;
    const aggs = {
      group_by: {
        terms: {
          field: 'baseInfo.serviceId',
          size: total
        },
        aggs: {
          ...typeAgg[type],
          sort: {
            bucket_sort: {
              sort: [
                {
                  value: {
                    order: 'desc'
                  }
                }
              ],
              from: 0,
              size: 10
            }
          }
        }
      }
    };

    const result: any = await esClient.search(API_INDEX, {
      body: {
        query: esquery,
        track_total_hits: true,
        aggs,
      }
    });
    let items = [];
    items = result.body.aggregations.group_by.buckets.map((item: any) => ({
      key: item.key,
      value: type === 'errPercent' ? (item.value.value*100).toFixed(6) : item.value.value
    }));

    return {
      result: items
    };
  }

}
