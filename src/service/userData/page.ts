import {ESClient} from '../../client';
import {mapping} from '../../mapping/page-log';
import axios from 'axios';
import {
  API_INDEX,
  PAGE_INDEX,
  DEFAULT_CLIENT,
  timestampToSecond
} from '../../utils';
import {cloneDeep} from 'lodash';
import {
  PageManage as PageManageDTO,
  PageTag as PageTagDTO,
  PageTagRef as PageTagRefDTO,
  AppManage as AppManageDTO,
} from '../../model';
import moment from 'moment';
import {Op} from 'sequelize';

interface PageDetailQuery {
  id: string;
  [propsName: string]: any;
}

export class Page {
  // 根据服务，获取对应的Client类
  static getClient(esType: string) {
    if (esType) {
      return ESClient[esType];
    }
    return ESClient[DEFAULT_CLIENT];
  }
  /*
    获取api索引的mapping
  */
  static async getMapping(esType: string) {
    const esClient = this.getClient(esType);
    const mapping: any = await esClient.getMapping(PAGE_INDEX);
    const setting: any = await esClient.getSetting(PAGE_INDEX);
    return {
      mapping: mapping.body[PAGE_INDEX],
      setting: setting
    };
  }

  static async setMapping(esType: string) {
    const esClient = this.getClient(esType);
    await esClient.clear(PAGE_INDEX);
    const result: any = await axios({
      url: 'http://10.27.218.69:8903/' + PAGE_INDEX,
      method: 'PUT',
      data: {
        mappings: {
          ...mapping
        }
      }
    });
    return result;
  }

  static async getData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        must: [],
        filter: []
      }
    };
    const esSort: any = [];
    let {
      pageNo,
      pageSize,
      startTime,
      endTime,
      pagePrefix,
      pagePath,
      browser,
      device,
      userId,
      order,
      orderBy,
      isError,
      serviceId,
      env
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    // 页面路径前缀查询
    if (pagePrefix) {
      esQuery.bool.filter.push({
        // 分词查询
        prefix: {
          'baseInfo.href.keyword': pagePrefix
        }
      });
    }
    // 指定页面路径查询
    if (pagePath) {
      esQuery.bool.filter.push({
        // 分词查询
        term: {
          'baseInfo._href.keyword': pagePath
        }
      });
    }
    // 指定用户查询
    if (userId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    // 指定浏览器查询
    if (browser) {
      esQuery.bool.filter.push({
        prefix: {
          'baseInfo.browser': browser
        }
      });
    }
    // 指定设备查询
    if (device) {
      esQuery.bool.filter.push({
        prefix: {
          'baseInfo.platform': device
        }
      });
    }
    // 是否只查询异常页面
    if (isError === true || isError === 'true') {
      esQuery.bool.must.push({
        exists: {
          field: 'error.message'
        }
      });
    }
    // 处理排序
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (order) {
      switch (orderBy) {
        case 'timeStamp':
          esSort.push({
            [timeField]: {
              order: order === 'asc' ? order : 'desc'
            }
          });
          break;
        default:
          break;
      }
    }
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const result: any = await esClient.search(PAGE_INDEX, {
      size: perPage,
      from: (page - 1) * perPage,
      body: {
        query: esQuery,
        track_total_hits: true,
        sort: esSort
      }
    });

    // url映射
    const urlMappings =
      (
        await this.getUrlMappingList({
          // 必填，服务id
          serviceId,
          // 必填，环境
          env,
          pageSize: 10000
        })
      )?.data?.items || [];

    const items: Array<any> = result.body.hits.hits.map((item: any) => {
      // 页面名称
      const urlMap = urlMappings.filter(
        urlInfo => urlInfo.get('url') == item._source.baseInfo.href
      );
      const urlName = urlMap?.length ? urlMap[0].get('name') : null;
      const tags = urlMap?.[0]?.get('tags') ?? '';
      item._source.baseInfo.urlName = urlName;
      item._source.baseInfo.tags = tags;
      return item._source;
    });

    let total = result.body.hits.total;
    if (typeof total !== 'number' && typeof total?.value === 'number') {
      total = total.value;
    }
    return {
      total: total > 10000 ? 10000 : total,
      items
    };
  }

  static async getDataDetail(esType: string, query: PageDetailQuery) {
    const esClient = this.getClient(esType);
    const {id, serviceId, env} = query;

    const result: any = await esClient.search(PAGE_INDEX, {
      body: {
        query: {
          match: {
            uuid: id
          }
        }
      }
    });

    // url映射
    const urlMappings =
      (
        await this.getUrlMappingList({
          // 必填，服务id
          serviceId,
          // 必填，环境
          env,
          pageSize: 10000
        })
      )?.data?.items || [];

    const detail: Array<any> = result.body.hits.hits.map((item: any) => ({
      ...item._source,
      id: item._id
    }));
    // 页面名称
    const urlMap = urlMappings.filter(
      urlInfo => urlInfo.get('url') == detail[0]._source.href
    );
    const name = urlMap?.length ? urlMap[0].get('name') : null;
    const tags = urlMap?.[0]?.get('tags') ?? '';

    return {...detail[0], name, tags};
  }

  /**
   * 页面日志聚合查询
   */
  static async getAggsData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        must: [],
        filter: []
      }
    };
    let {
      startTime,
      endTime,
      aggType,
      perPage,
      page,
      serviceId,
      keyword,
      orderBy,
      orderDir,
      env,
      isFullData
    } = query;
    page = parseInt(page as string, 10) || 1;
    perPage = parseInt(perPage as string, 10) || 10;

    const showAllPage = aggType === 'allPage';
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    let aggsTotalField;
    switch (aggType) {
      case 'page':
        aggsTotalField = 'baseInfo._href.keyword';
        break;
      case 'browser':
        aggsTotalField = 'baseInfo.browser';
        break;
      default:
        aggsTotalField = 'baseInfo._href.keyword';
        break;
    }

    let _esquery = cloneDeep(esquery);
    _esquery.bool.filter.push({
      exists: {
        field: 'error.message'
      }
    });

    let allPvQuery = cloneDeep(esquery);
    if (keyword) {
      esquery.bool.filter.push({
        wildcard: {
          [aggsTotalField]: `*${keyword}*`
        }
      });
      _esquery.bool.filter.push({
        wildcard: {
          [aggsTotalField]: `*${keyword}*`
        }
      });
    }

    const totalRes = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          total: {
            cardinality: {
              field: aggsTotalField
            }
          }
        }
      }
    });
    const _totalRes = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: _esquery,
        aggs: {
          total: {
            cardinality: {
              field: aggsTotalField
            }
          }
        }
      }
    });

    const total = totalRes?.body?.aggregations?.total?.value;
    const _total = _totalRes?.body?.aggregations?.total?.value;
    if ((showAllPage && !total) || (!showAllPage && !_total)) {
      return {
        items: [],
        total: 0
      };
    }
    const sortBy = (
      {count: 'count', errCount: 'err_count', errPercent: 'err_percent'} as any
    )[orderBy || 'errPercent'];
    const aggsContent = {
      err_percent: {
        bucket_script: {
          buckets_path: {
            err_count: 'err_count',
            _count: '_count'
          },
          script: 'params.err_count / (params._count - params.err_count)'
        }
      },
      count: {
        bucket_script: {
          buckets_path: {
            err_count: 'err_count',
            _count: '_count'
          },
          script: 'params._count - params.err_count'
        }
      },
      // 判断下是否要去除没有错误的页面数据
      having: showAllPage
        ? undefined
        : {
            bucket_selector: {
              buckets_path: {
                err_count: 'err_count'
              },
              script: {
                source: 'params.err_count > 0'
              }
            }
          },
      _count: {
        value_count: {
          field: 'type'
        }
      },
      // 统计页面报错的数量
      err_count: {
        value_count: {
          field: 'error.message'
        }
      },
      sort: {
        bucket_sort: {
          sort: [
            {
              [sortBy]: {
                order: orderDir || 'desc'
              }
            }
          ],
          // 判断下是否要分页
          from: !isFullData ? perPage * (page - 1) : undefined,
          size: !isFullData ? perPage : undefined
        }
      }
    };

    const resultRes: any = await esClient.search(PAGE_INDEX, {
      body: {
        query: esquery,
        aggs: {
          group_by: {
            terms: {
              field: aggsTotalField,
              size: total
            },
            aggs: aggsContent
          }
        }
      }
    });

    const resultList = resultRes.body.aggregations.group_by.buckets || [];

    // 统计下应用的PV，用来算下单个页面的PV占比
    let allpv = 0;
    if (showAllPage) {
      const baseInfo: any = await esClient.search(PAGE_INDEX, {
        size: 0,
        body: {
          query: allPvQuery,
          aggs: {
            group_by: {
              terms: {
                field: 'baseInfo.serviceId',
                size: 1
              },
              aggs: {
                pv: {
                  filter: {
                    match: {
                      type: 'monitor-pv'
                    }
                  }
                }
              }
            }
          }
        }
      });
      allpv = baseInfo.body.aggregations.group_by.buckets[0].pv.doc_count;
    }

    // url映射
    const urlMappings =
      (
        await this.getUrlMappingList({
          // 必填，服务id
          serviceId,
          // 必填，环境
          env,
          pageSize: 10000
        })
      )?.data?.items || [];

    const items: Array<any> = resultList.map((item: any) => {
      const urlMap = urlMappings.filter(
        urlInfo => urlInfo.get('url') == item.key
      );
      return {
        pvPercent: showAllPage
          ? Number((item.count.value || 0) / allpv).toFixed(6)
          : undefined,
        value: item.key,
        name: urlMap?.length ? urlMap[0].get('name') : null,
        tags: urlMap?.[0]?.get('tags') ?? '',
        count: item.count.value,
        errCount: item.err_count.value,
        errPercent: (item.err_percent.value || 0).toFixed(6)
      };
    });
    return {
      items,
      total: showAllPage ? total : _total
    };
  }

  /*
    页面访问量、异常量、报错率
  */
  static async getPageInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env} = query;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    // 有error字段才认为是页面报错
    const aggs: any = {
      count_error: {
        value_count: {field: 'error.message'}
      },
      pv: {
        filter: {
          match: {
            type: 'monitor-pv'
          }
        }
      }
    };
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    const errCount = result.body.aggregations.count_error.value;
    const totalCount = result.body.aggregations.pv.doc_count;
    const errPercent = Number(errCount / totalCount || 0).toFixed(6);

    return {
      errCount,
      totalCount,
      errPercent
    };
  }

  static async getPageInfoGap(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfoGap = {
      errCountGap: '',
      errCountLast: '',
      totalCountGap: '',
      totalCountLast: '',
      errPercentGap: '',
      errPercentLast: ''
    };
    const baseInfo = await this.getPageInfo(esType, query);
    // 计算环比
    if (startTime) {
      const _baseInfo = await this.getPageInfo(esType, {
        ...query,
        startTime: 2 * startTime - endTime,
        endTime: startTime
      });
      const {errCount, totalCount, errPercent} = _baseInfo;
      baseInfoGap.errCountGap = errCount
        ? ((baseInfo.errCount - errCount) / errCount).toFixed(6)
        : '';
      baseInfoGap.totalCountGap = totalCount
        ? ((baseInfo.totalCount - totalCount) / totalCount).toFixed(6)
        : '';
      baseInfoGap.errPercentGap = Number(errPercent)
        ? (
            (Number(baseInfo.errPercent) - Number(errPercent)) /
            Number(errPercent)
          ).toFixed(6)
        : '';
      baseInfoGap.totalCountLast = totalCount;
      baseInfoGap.errCountLast = errCount;
      baseInfoGap.errPercentLast = errPercent;
    }
    return {
      ...baseInfo,
      ...baseInfoGap
    };
  }

  /*
    页面天粒度的访问量、报错量、报错率、弹窗数
  */
  static async getDailyPageInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env} = query;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    // 基于日期聚合后再基于isError字段聚合
    const daily_error_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          count_error: {
            value_count: {field: 'error.message'}
          },
          pv: {
            filter: {
              match: {
                type: 'monitor-pv'
              }
            }
          }
        }
      }
    };
    // 一段时间内每天的报错信息
    const daily_error_result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: daily_error_aggs
      }
    });

    const items: Array<any> =
      daily_error_result.body.aggregations.group_by_time.buckets.map(
        (item: any) => {
          const errCount = item.count_error.value;
          const totalCount = item.pv.doc_count;
          const time = item.key_as_string;
          const errPercent = Number(errCount / totalCount || 0).toFixed(6);
          return {
            time,
            errCount,
            totalCount,
            errPercent
          };
        }
      );

    return {
      items
    };
  }
  /*
    根据url获取页面退出率
  */
  static async getExitRatio(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must_not: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {startTime, endTime, serviceId, env, href} = query;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    let totalQuery = cloneDeep(esquery);
    totalQuery.bool.filter.push({
      term: {
        'baseInfo._href.keyword': href
      }
    });
    // 排除页面内重复上报
    esquery.bool.filter.push({
      term: {
        'from.keyword': href
      }
    });
    const totalResult: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: totalQuery,
        track_total_hits: true
      }
    });
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true
      }
    });
    // 总的pv
    const totalCount = totalResult.body.hits.total.value;
    // 本产品内跳转  总的-本产品内跳转 = 跳出的
    const count = result.body.hits.total.value;
    // 百分比
    const ratio = Number(
      ((totalCount - count) * 100) / totalCount || 0
    ).toFixed(2);
    return Math.max(0, Number(ratio));
  }

  /*
    页面每天相关指标趋势
  */
  static async getTrendDailyData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ],
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env, href} = query;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const timeEsQuery = cloneDeep(esquery);
    if (href?.length) {
      esquery.bool.filter.push({
        terms: {
          'baseInfo._href.keyword': href
        }
      });
      timeEsQuery.bool.filter.push({
        terms: {
          'from.keyword': href
        }
      });
    }

    // 聚合字段
    const aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          group_by_user: {
            cardinality: {
              field: 'baseInfo.userId'
            }
          },
          pv: {
            filter: {
              match: {
                type: 'monitor-pv'
              }
            }
          },
          view_count: {
            cardinality: {
              field: 'baseInfo.signature'
            }
          },
          view_total_sum: {
            sum: {
              field: 'stayTime'
            }
          }
        }
      }
    };
    const timeAggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        }
      }
    };
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    const timeResult: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: timeEsQuery,
        track_total_hits: true,
        aggs: timeAggs
      }
    });
    const timeObject: any = {};
    timeResult.body.aggregations.group_by_time.buckets.forEach(
      (element: any) => {
        timeObject[element.key_as_string] = {
          pvJumpCount: element.doc_count
        };
      }
    );

    const items: Array<any> =
      result.body.aggregations.group_by_time.buckets.map((item: any) => {
        // PV
        const pvCount = item.pv.doc_count;
        // UV
        const uvCount = item.group_by_user.value;
        // 页面访问次数
        const viewCount = item.view_count.value;
        // 统计时间
        const time = item.key_as_string;
        // 平均访问时长
        const viewAverageTime = viewCount
          ? Number((item.view_total_sum.value / viewCount).toFixed(2))
          : 0;
        // 平均访问页面数量
        const pvAvg = viewCount ? (pvCount / viewCount).toFixed(2) : 0;
        // 页面退出率 // 本产品内跳转  总的-本产品内跳转 = 跳出的
        const quitPercent = pvCount
          ? (
              (pvCount - (timeObject[time]?.pvJumpCount || 0)) /
              pvCount
            ).toFixed(6)
          : 0;
        return {
          time,
          uvCount,
          pvCount,
          viewCount,
          viewAverageTime: timestampToSecond(viewAverageTime),
          pvAvg,
          quitPercent
        };
      });
    return items;
  }

  /*
    页面相关指标趋势总记-访问页面数量
  */
  static async getTrendInfoOfPvTotalSum(
    esType: string,
    esquery: any,
    timeField: string
  ) {
    const esClient = this.getClient(esType);
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: {
          pv_no_repeat: {
            terms: {
              field: 'baseInfo.signature',
              size: 65535
            },
            aggs: {
              unique_value: {
                cardinality: {
                  field: 'baseInfo._href.keyword'
                }
              }
            }
          },
          pv_total_sum: {
            sum_bucket: {
              buckets_path: 'pv_no_repeat>unique_value'
            }
          }
        }
      }
    });
    return result.body.aggregations.pv_total_sum.value;
  }

  /*
    页面相关指标趋势总记-访问一次
  */
  static async getTrendInfoOfJumpOne(
    esType: string,
    esquery: any,
    timeField: string
  ) {
    const esClient = this.getClient(esType);
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: {
          jump_value_one_buckets: {
            terms: {
              field: 'baseInfo.signature',
              size: 65535,
              min_doc_count: 2
            }
          }
        }
      }
    });
    return result.body.aggregations.jump_value_one_buckets.buckets?.length;
  }

  static async getTrendInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {startTime, endTime, serviceId, env, href} = query;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const timeEsQuery = cloneDeep(esquery);
    if (href?.length) {
      esquery.bool.filter.push({
        terms: {
          'baseInfo._href.keyword': href
        }
      });
      timeEsQuery.bool.filter.push({
        terms: {
          'from.keyword': href
        }
      });
    }

    const aggs: any = {
      group_by_user: {
        cardinality: {
          field: 'baseInfo.userId'
        }
      },
      pv: {
        filter: {
          match: {
            type: 'monitor-pv'
          }
        }
      },
      view_count: {
        cardinality: {
          field: 'baseInfo.signature'
        }
      },
      view_total_sum: {
        sum: {
          field: 'stayTime'
        }
      }
    };
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    const timeResult: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: timeEsQuery,
        track_total_hits: true
      }
    });
    const totalData = result.body.aggregations;
    // PV
    const pvCount = totalData.pv.doc_count;
    // UV
    const uvCount = totalData.group_by_user.value;
    // 页面访问次数
    const viewCount = totalData.view_count.value;

    // 平均访问时长
    const viewAverageTime = viewCount
      ? Number((totalData.view_total_sum.value / viewCount).toFixed(2))
      : 0;
    // 平均访问页面数量
    const pvAvg = viewCount ? (pvCount / viewCount).toFixed(6) : 0;
    // 页面退出率 // 本产品内跳转  总的-本产品内跳转 = 跳出的
    const pvJumpCount = timeResult.body.hits.total.value;
    const quitPercent = pvCount
      ? ((pvCount - pvJumpCount) / pvCount).toFixed(6)
      : 0;
    return {
      uvCount,
      pvCount,
      viewCount,
      viewAverageTime: timestampToSecond(viewAverageTime),
      pvAvg,
      quitPercent: Math.max(0, Number(quitPercent))
    };
  }
  /*
    页面相关指标环比
  */
  static async getTrendInfoData(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfoGap = {
      uvCountGap: '',
      uvCountLast: '',
      pvCountGap: '',
      pvCountLast: '',
      viewCountGap: '',
      viewCountLast: '',
      viewAverageTimeGap: '',
      viewAverageTimeLast: '',
      pvAvgGap: '',
      pvAvgLast: '',
      quitPercentGap: '',
      quitPercentLast: ''
    };
    const baseInfo: any = await this.getTrendInfo(esType, query);
    // 计算环比
    if (startTime) {
      const _baseInfo: any = await this.getTrendInfo(esType, {
        ...query,
        startTime: 2 * startTime - endTime,
        endTime: startTime
      });
      const {uvCount, pvCount, viewCount, viewAverageTime, pvAvg, quitPercent} =
        _baseInfo;
      baseInfoGap.uvCountGap = uvCount
        ? ((baseInfo.uvCount - uvCount) / uvCount).toFixed(6)
        : '';
      baseInfoGap.pvCountGap = pvCount
        ? ((baseInfo.pvCount - pvCount) / pvCount).toFixed(6)
        : '';
      baseInfoGap.viewCountGap = viewCount
        ? ((baseInfo.viewCount - viewCount) / viewCount).toFixed(6)
        : '';
      baseInfoGap.viewAverageTimeGap = viewAverageTime
        ? (
            (baseInfo.viewAverageTime - viewAverageTime) /
            viewAverageTime
          ).toFixed(6)
        : '';
      baseInfoGap.pvAvgGap = pvAvg
        ? ((baseInfo.pvAvg - pvAvg) / pvAvg).toFixed(6)
        : '';
      baseInfoGap.quitPercentGap = quitPercent
        ? ((baseInfo.quitPercent - quitPercent) / quitPercent).toFixed(6)
        : '';
      baseInfoGap.quitPercentLast = quitPercent;
      baseInfoGap.uvCountLast = uvCount;
      baseInfoGap.pvCountLast = pvCount;
      baseInfoGap.viewCountLast = viewCount;
      baseInfoGap.viewAverageTimeLast = viewAverageTime;
      baseInfoGap.pvAvgLast = pvAvg;
    }
    return {
      ...baseInfo,
      ...baseInfoGap
    };
  }

  /*
    按用户统计页面pv，访问次数、平均访问时长、平均访问页面数量等数据
  */
  static async getUserTrendDailyData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {
      startTime,
      endTime,
      serviceId,
      env,
      href,
      pageNo,
      pageSize,
      order,
      userId,
      orderBy
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (href?.length) {
      esquery.bool.filter.push({
        terms: {
          'href.keyword': href
        }
      });
    }

    const sortBy = (
      {
        pvCount: '_count',
        viewCount: 'total_count',
        time: 'view_time_total_count.value',
        dailySum: 'group_by_time._bucket_count',
        pvAverageCount: 'pvAverageCount.value'
      } as any
    )[orderBy || 'pvCount'];

    // 聚合字段
    const aggs: any = {
      group_by_user: {
        terms: {
          field: 'baseInfo.userId',
          size: 100
        },
        aggs: {
          page: {
            bucket_sort: {
              size: perPage,
              from: (page - 1) * perPage,
              sort: [
                {
                  [sortBy]: {
                    order: order || 'desc'
                  }
                }
              ]
            }
          },
          group_by_sub_user: {
            terms: {
              field: 'baseInfo.subUserId',
              size: 100
            }
          },
          group_by_time: {
            date_histogram: {
              field: timeField,
              calendar_interval: 'day',
              time_zone: 'Asia/Shanghai',
              format: 'yyyy-MM-dd',
              min_doc_count: 1
            }
          },
          total_count: {
            cardinality: {
              field: 'baseInfo.signature'
            }
          },
          // 页面总访问时长
          view_time_total_count: {
            sum: {
              field: 'stayTime'
            }
          },
          pvAverageCount: {
            bucket_script: {
              buckets_path: {
                total_a: '_count',
                total_b: 'total_count'
              },
              script: 'params.total_a / params.total_b'
            }
          }
        }
      },
      total: {
        cardinality: {
          field: 'baseInfo.userId'
        }
      }
    };
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    const total = result?.body?.aggregations?.total.value;

    const items: Array<any> =
      result.body.aggregations.group_by_user.buckets.map((item: any) => {
        // userId
        const userId = item.key;
        // PV
        const pvCount = item.doc_count;
        // 页面访问次数
        const viewCount = item.total_count.value;
        // 页面总访问时长
        const timeViewTotal = item.view_time_total_count.value;
        // 平均访问时长
        const timeAverageCount = viewCount
          ? (timeViewTotal / viewCount).toFixed(6)
          : 0;
        // 活跃天数
        const dailySum = item.group_by_time.buckets.length;
        // 子用户数量
        const userSum = item.group_by_sub_user.buckets.length;
        // 平均访问页面数量
        const pvAverageCount = item.pvAverageCount.value
          ? item.pvAverageCount.value.toFixed(1)
          : 0;
        return {
          userId,
          userSum,
          pvCount,
          viewCount,
          timeViewTotal: timestampToSecond(Number(timeViewTotal)),
          timeAverageCount: timestampToSecond(Number(timeAverageCount)),
          dailySum,
          pvAverageCount
        };
      });
    return {items, total: Math.min(total, 100)};
  }

  /*
    按子用户统计页面pv，访问次数、平均访问时长、平均访问页面数量等数据
  */
  static async getSubUserTrendDailyData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {
      startTime,
      endTime,
      serviceId,
      env,
      href,
      pageNo,
      pageSize,
      order,
      userId,
      orderBy
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (href?.length) {
      esquery.bool.filter.push({
        terms: {
          'href.keyword': href
        }
      });
    }

    const sortBy = (
      {
        pvCount: '_count',
        viewCount: 'total_count',
        time: 'view_time_total_count.value',
        dailySum: 'group_by_time._bucket_count',
        pvAverageCount: 'pvAverageCount.value'
      } as any
    )[orderBy || 'pvCount'];

    // 聚合字段
    const aggs: any = {
      group_by_user: {
        terms: {
          field: 'baseInfo.subUserId',
          size: 100
        },
        aggs: {
          page: {
            bucket_sort: {
              size: perPage,
              from: (page - 1) * perPage,
              sort: [
                {
                  [sortBy]: {
                    order: order || 'desc'
                  }
                }
              ]
            }
          },
          group_by_time: {
            date_histogram: {
              field: timeField,
              calendar_interval: 'day',
              time_zone: 'Asia/Shanghai',
              format: 'yyyy-MM-dd',
              min_doc_count: 1
            }
          },
          total_count: {
            cardinality: {
              field: 'baseInfo.signature'
            }
          },
          // 页面总访问时长
          view_time_total_count: {
            sum: {
              field: 'stayTime'
            }
          },
          pvAverageCount: {
            bucket_script: {
              buckets_path: {
                total_a: '_count',
                total_b: 'total_count'
              },
              script: 'params.total_a / params.total_b'
            }
          }
        }
      },
      total: {
        cardinality: {
          field: 'baseInfo.subUserId'
        }
      }
    };
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    const total = result?.body?.aggregations?.total.value;

    const items: Array<any> =
      result.body.aggregations.group_by_user.buckets.map((item: any) => {
        // userId
        const userId = item.key;
        // PV
        const pvCount = item.doc_count;
        // 页面访问次数
        const viewCount = item.total_count.value;
        // 页面总访问时长
        const timeViewTotal = item.view_time_total_count.value;
        // 平均访问时长
        const timeAverageCount = viewCount
          ? (timeViewTotal / viewCount).toFixed(6)
          : 0;
        // 活跃天数
        const dailySum = item.group_by_time.buckets.length;
        // 平均访问页面数量
        const pvAverageCount = item.pvAverageCount.value
          ? item.pvAverageCount.value.toFixed(1)
          : 0;
        return {
          userId,
          pvCount,
          viewCount,
          timeViewTotal: timestampToSecond(Number(timeViewTotal)),
          timeAverageCount: timestampToSecond(Number(timeAverageCount)),
          dailySum,
          pvAverageCount
        };
      });
    return {items, total: Math.min(total, 100)};
  }

  /*
    按下级页面uv/pv，访问次数、平均访问时长、平均访问页面数量等数据
  */
  static async getChildPageTrendData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {
      startTime,
      endTime,
      serviceId,
      env,
      pageNo,
      pageSize,
      order,
      orderBy,
      href
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (href && href.length) {
      esquery.bool.filter.push({
        terms: {
          'baseInfo._href.keyword': href
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const sortBy = (
      {
        pvCount: '_count',
        uvCount: 'group_by_user.value',
        viewCount: 'total_count',
        time: 'view_time_total_count.value',
        averageTime: 'timeAverageCount.value',
        pvRatio: 'pvAverageCount.value'
      } as any
    )[orderBy || 'pvCount'];

    // 聚合字段
    const aggs: any = {
      group_by_href: {
        terms: {
          field: 'baseInfo._href.keyword',
          size: 10000
        },
        aggs: {
          page: {
            bucket_sort: {
              size: perPage,
              from: (page - 1) * perPage,
              sort: [
                {
                  [sortBy]: {
                    order: order || 'desc'
                  }
                }
              ]
            }
          },
          total_count: {
            cardinality: {
              field: 'baseInfo.signature'
            }
          },
          group_by_user: {
            cardinality: {
              field: 'baseInfo.userId'
            }
          },
          // 页面总访问时长
          view_time_total_count: {
            sum: {
              field: 'stayTime'
            }
          },
          // 页面平均访问时长
          timeAverageCount: {
            bucket_script: {
              buckets_path: {
                total_a: 'view_time_total_count',
                total_b: 'total_count'
              },
              script: 'params.total_a / params.total_b'
            }
          }
        }
      },
      total: {
        cardinality: {
          field: 'baseInfo._href.keyword'
        }
      }
    };
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 100,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });

    // 总PV
    const pvTotalCount = result.body.hits.total.value;
    const pageExitRatioReqs =
      result.body.aggregations.group_by_href.buckets.map((item: any) => {
        const href = item.key;
        return this.getExitRatio(esType, {...query, href});
      });
    const pageExitRatios = await Promise.all(pageExitRatioReqs).then(res => {
      return res;
    });

    // 选中页面的数据
    let filteredResult = result;
    if (href?.length) {
      esquery.bool.filter.push({
        terms: {
          'baseInfo._href.keyword': href
        }
      });

      filteredResult = await esClient.search(PAGE_INDEX, {
        size: 100,
        body: {
          query: esquery,
          track_total_hits: true,
          aggs
        }
      });
    }

    // url映射
    const urlMappings =
      (
        await this.getUrlMappingList({
          // 必填，服务id
          serviceId,
          // 必填，环境
          env,
          pageSize: 10000
        })
      )?.data?.items || [];
    try {
      const items: Array<any> =
        filteredResult.body.aggregations.group_by_href.buckets.map(
          (item: any, index: number) => {
            // url映射
            const urlMap = urlMappings.filter(
              urlInfo => urlInfo.get('url') == item.key
            )?.[0];
            // 页面名称
            const name = urlMap?.get('name') ?? null;
            const tags = urlMap?.get('tags') ?? '';
            // href
            const href = item.key;
            // PV
            const pvCount = item.doc_count;
            // UV
            const uvCount = item.group_by_user.value;
            // PV占比百分比
            const pvRatio = pvTotalCount
              ? ((pvCount * 100) / pvTotalCount).toFixed(2)
              : 0;
            // 页面访问次数
            const viewCount = item.total_count.value;
            // 页面总访问时长
            const timeViewTotal = item.view_time_total_count.value;
            // 平均访问时长
            const timeAverageCount = item.timeAverageCount.value;
            const pageExitRatio = pageExitRatios[index];
            return {
              name,
              href,
              tags,
              pvCount,
              pageExitRatio,
              pvRatio,
              uvCount,
              viewCount,
              timeViewTotal: timestampToSecond(Number(timeViewTotal)),
              timeAverageCount: timestampToSecond(Number(timeAverageCount))
            };
          }
        );

      const total = filteredResult?.body?.aggregations?.total.value;
      return {items, total: Math.min(total, 10000)};
    } catch (e) {
      return {items: [], total: 0};
    }
  }

  /*
    按下级页面uv/pv，访问次数、平均访问时长、平均访问页面数量等数据
  */
  static async getPageTrendDataByTag(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {
      startTime,
      endTime,
      serviceId,
      env,
      pageNo,
      pageSize,
      order,
      orderBy
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const sortBy = (
      {
        pvCount: '_count',
        uvCount: 'group_by_user.value',
        viewCount: 'total_count',
        time: 'view_time_total_count.value',
        averageTime: 'timeAverageCount.value',
        pvRatio: 'pvAverageCount.value'
      } as any
    )[orderBy || 'pvCount'];

    // 聚合字段
    const aggs: any = {
      group_by_href: {
        terms: {
          field: 'baseInfo._href.keyword',
          size: 10000
        },
        aggs: {
          page: {
            bucket_sort: {
              size: perPage,
              from: (page - 1) * perPage,
              sort: [
                {
                  [sortBy]: {
                    order: order || 'desc'
                  }
                }
              ]
            }
          },
          total_count: {
            cardinality: {
              field: 'baseInfo.signature'
            }
          },
          group_by_user: {
            cardinality: {
              field: 'baseInfo.userId'
            }
          },
          // 页面总访问时长
          view_time_total_count: {
            sum: {
              field: 'stayTime'
            }
          },
          // 页面平均访问时长
          timeAverageCount: {
            bucket_script: {
              buckets_path: {
                total_a: 'view_time_total_count',
                total_b: 'total_count'
              },
              script: 'params.total_a / params.total_b'
            }
          }
        }
      },
      total: {
        cardinality: {
          field: 'baseInfo._href.keyword'
        }
      }
    };
    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    // 总PV
    const pvTotalCount = result.body.hits.total.value;
    const pageExitRatioReqs =
      result.body.aggregations.group_by_href.buckets.map((item: any) => {
        const href = item.key;
        return this.getExitRatio(esType, {...query, href});
      });
    const pageExitRatios = await Promise.all(pageExitRatioReqs).then(res => {
      return res;
    });

    const total = result?.body?.aggregations?.total.value;

    // url映射
    const urlMappings =
      (
        await this.getUrlMappingList({
          // 必填，服务id
          serviceId,
          // 必填，环境
          env,
          pageSize: 10000
        })
      )?.data?.items || [];

    const tagMap = {} as any;

    try {
      result.body.aggregations.group_by_href.buckets.forEach(
        (item: any, index: number) => {
          // url映射
          const urlMap = urlMappings.filter(
            urlInfo => urlInfo.get('url') == item.key
          )?.[0];
          // 页面名称
          const name = urlMap?.get('name') ?? null;
          const tags = urlMap?.get('tags') ?? ('' as string);

          // href
          const href = item.key;
          // PV
          const pvCount = item.doc_count;
          // UV
          const uvCount = item.group_by_user.value;
          // PV占比百分比
          const pvRatio = pvTotalCount
            ? ((pvCount * 100) / pvTotalCount).toFixed(2)
            : 0;
          // 页面访问次数
          const viewCount = item.total_count.value;
          // 页面总访问时长
          const timeViewTotal = item.view_time_total_count.value;
          // 平均访问时长
          const timeAverageCount = item.timeAverageCount.value;
          // 退出率
          const pageExitRatio = pageExitRatios[index];
          try {
            const _tags = tags && (tags as string).length ? tags : '-';

            (_tags as string).split(',').forEach(tag => {
              if (tagMap[tag] || !tag || !tag.length) {
                Object.assign(tagMap[tag], {
                  pvCount: tagMap[tag].pvCount + pvCount,
                  pvRatio: (tagMap[tag].pvRatio + pvRatio).toFixed(2),
                  pageExitRatio: tagMap[tag].pageExitRatio + pageExitRatio,
                  uvCount: tagMap[tag].uvCount + uvCount,
                  viewCount: tagMap[tag].viewCount + viewCount,
                  timeViewTotal:
                    tagMap[tag].timeViewTotal +
                    timestampToSecond(Number(timeViewTotal)),
                  timeAverageCount:
                    tagMap[tag].timeAverageCount +
                    timestampToSecond(Number(timeAverageCount))
                });
              } else {
                tagMap[tag] = {
                  pvCount,
                  pageExitRatio,
                  pvRatio,
                  uvCount,
                  viewCount,
                  timeViewTotal: timestampToSecond(Number(timeViewTotal)),
                  timeAverageCount: timestampToSecond(Number(timeAverageCount))
                };
              }
            });
          } catch (err) {}
        }
      );
    } catch (err) {}
    const itemsNum = Math.min(Object.keys(tagMap).length, 10000);
    const items = Object.keys(tagMap).map(item => ({
      ...tagMap[item],
      pageExitRatio: itemsNum
        ? (tagMap[item].pageExitRatio / itemsNum).toFixed(2)
        : 0,
      timeAverageCount: tagMap[item].viewCount
        ? Number(
            (tagMap[item].timeViewTotal / tagMap[item].viewCount).toFixed(2)
          )
        : 0,
      tag: item
    }));

    return {items, total: itemsNum};
  }

  /*
    按页面来源统计分析
  */
  static async getSourcePageTrendData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {
      startTime,
      endTime,
      serviceId,
      env,
      pageNo,
      pageSize,
      href,
      order,
      orderBy
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;
    const aggs: any = {
      total: {
        terms: {
          field: 'from.keyword',
          exclude: `.*/${serviceId}/.*`,
          size: 65535
        },
        aggs: {
          page: {
            bucket_sort: {
              size: perPage,
              from: (page - 1) * perPage,
              sort: [
                {
                  _count: {
                    order: order || 'desc'
                  }
                }
              ]
            }
          }
        }
      }
    };
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (href?.length) {
      esquery.bool.filter.push({
        terms: {
          'href.keyword': href
        }
      });
      // 页面级的不需要排除当前应用
      delete aggs.total.terms.exclude;
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    // 计算总数据
    const totalAggs = cloneDeep(aggs);
    delete totalAggs.total.aggs;
    const totalResult: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: totalAggs
      }
    });

    // url映射
    const urlMappings =
      (
        await this.getUrlMappingList({
          // 必填，服务id
          serviceId,
          // 必填，环境
          env,
          pageSize: 10000
        })
      )?.data?.items || [];

    const items: Array<any> = result.body.aggregations.total.buckets.map(
      (item: any) => {
        // url映射
        const urlMap = urlMappings.filter(
          urlInfo => urlInfo.get('url') == item.key
        );
        // 页面名称
        const name = urlMap?.length ? urlMap[0].get('name') : null;
        // 页面标签
        const tags = urlMap?.[0]?.get('tags') ?? '';
        return {...item, name, tags};
      }
    );

    return {
      items,
      total: totalResult.body.aggregations.total.buckets.length
    };
  }
  /*
    按页面去向统计分析
  */
  static async getDirectionPageTrendData(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: [],
        must: [
          {
            term: {
              type: 'monitor-pv'
            }
          }
        ]
      }
    };
    const {
      startTime,
      endTime,
      serviceId,
      env,
      pageNo,
      pageSize,
      href,
      order,
      orderBy
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;
    const aggs: any = {
      total: {
        terms: {
          field: 'href.keyword',
          exclude: `.*/${serviceId}/.*`,
          size: 65535
        },
        aggs: {
          page: {
            bucket_sort: {
              size: perPage,
              from: (page - 1) * perPage,
              sort: [
                {
                  _count: {
                    order: order || 'desc'
                  }
                }
              ]
            }
          }
        }
      }
    };
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    // 去向的数据from全局查
    if (href?.length) {
      esquery.bool.filter.push({
        terms: {
          'from.keyword': href
        }
      });
      // 页面级的不需要排除当前应用
      delete aggs.total.terms.exclude;
    }
    if (!href?.length && serviceId) {
      esquery.bool.filter.push({
        regexp: {
          'from.keyword': `.*/${serviceId}/.*`
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });
    // 计算总数据
    const totalAggs = cloneDeep(aggs);
    delete totalAggs.total.aggs;
    const totalResult: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: totalAggs
      }
    });

    // url映射
    const urlMappings =
      (
        await this.getUrlMappingList({
          // 必填，服务id
          serviceId,
          // 必填，环境
          env,
          pageSize: 10000
        })
      )?.data?.items || [];

    const items: Array<any> = result.body.aggregations.total.buckets.map(
      (item: any) => {
        // url映射
        const urlMap = urlMappings.filter(
          urlInfo => urlInfo.get('url') == item.key
        );
        // 页面名称
        const name = urlMap?.length ? urlMap[0].get('name') : null;
        // 页面标签
        const tags = urlMap?.[0]?.get('tags') ?? '';
        return {...item, name, tags};
      }
    );

    return {
      items,
      total: totalResult.body.aggregations.total.buckets.length
    };
  }

  /*
   * 添加url映射
   */
  static async addUrlMapping(body: AddUrlMappingItem) {
    const {serviceId, env, items, creator = DEFAULT_CLIENT} = body;
    if (!serviceId || !env || !items || !items.length) {
      return {
        msg: '添加url映射缺少参数，请检查',
        success: false
      };
    }

    let existedUrls = [] as any[];
    try {
      await Promise.all(
        items.map(async (item, index) => {
          let urlMapping = await PageManageDTO.findOne({
            where: {
              url: item.url,
              env,
              deleted_at: null,
              service_id: serviceId
            }
          });

          if (urlMapping) {
            existedUrls.push(urlMapping);
          }
          return Promise.resolve(urlMapping);
        })
      );
    } catch (err) {}

    if (existedUrls.length) {
      return {
        msg: '存在url已创建映射，请勿重复创建',
        success: false
      };
    }

    await Promise.all(
      items.map(async item => {
        if (item.tags && item.tags.length) {
          // 添加tag
          const res = await this.addPageTag({
            tags: item.tags,
            env,
            creator,
            serviceId
          });
          const tagInfo = res.data?.tagInfo as Array<{
            tagId: string;
            tagName: string;
            isCreated: boolean;
          }>;
          // 添加tag映射
          this.addPageTagRef({
            env,
            tags: tagInfo,
            serviceId,
            name: item.name,
            url: item.url
          });
        }
      })
    );

    // 添加页面映射
    const data = items.map(item => ({
      service_id: serviceId,
      env,
      url: item.url,
      name: item.name,
      description: item.description,
      tags: (item.tags || []).join(','),
      creator,
      created_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    }));

    await PageManageDTO.bulkCreate(data);

    return {
      msg: '添加成功',
      success: true
    };
  }

  /*
   * 编辑url映射
   */
  static async editUrlMapping(body: EditUrlMappingItem) {
    const {
      id,
      name,
      description,
      tags,
      updater = DEFAULT_CLIENT,
      env,
      serviceId,
      url
    } = body;

    if (!id || !name || !url) {
      return {
        msg: '编辑url映射缺少参数，请检查',
        success: false
      };
    }

    const urlMapping = await PageManageDTO.findOne({
      where: {
        id: id,
        deleted_at: null
      }
    });
    if (!urlMapping) {
      return {
        msg: '该映射不存在，请重试',
        success: false
      };
    }
    urlMapping.update({
      name,
      updater,
      tags: (tags || []).join(','),
      description,
      updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    await urlMapping.save();

    // 更新tag
    if (tags && tags.length) {
      const res = await this.addPageTag({
        tags,
        env,
        creator: updater,
        serviceId
      });
      const tagInfo = res.data?.tagInfo as Array<{
        tagId: string;
        tagName: string;
        isCreated: boolean;
      }>;
      // 更新tag映射
      this.updatePageTagRef({
        env,
        tags: tagInfo,
        serviceId,
        name,
        url
      });
    }

    return {
      msg: '更新成功',
      success: true
    };
  }

  /*
   * 删除url映射
   */
  static async deleteUrlMapping(body: DeleteUrlMappingItem) {
    const {id} = body;
    if (!id) {
      return {
        msg: '删除url映射缺少参数，请检查',
        success: false
      };
    }

    const urlMapping = await PageManageDTO.findOne({
      where: {
        id: id,
        deleted_at: null
      }
    });
    if (!urlMapping) {
      return {
        msg: '该映射不存在，请重试',
        success: false
      };
    }

    urlMapping.update({
      deleted_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    await urlMapping.save();

    return {
      msg: '删除成功',
      success: true
    };
  }

  /*
   * 获取页面映射列表
   */
  static async getUrlMappingList(query: GetUrlMappingListItem) {
    const {
      serviceId,
      env,
      pageNo,
      pageSize,
      order,
      orderBy,
      keyword,
      keywordType
    } = query;

    const _pageNo = parseInt(pageNo as any, 10) || 1;
    const _pageSize = parseInt(pageSize as any, 10) || 10;
    let _orderBy = orderBy;
    let _order = order;

    switch (_orderBy) {
      case 'createdAt':
        _orderBy = 'created_at';
        break;
      case 'updatedAt':
        _orderBy = 'updated_at';
        break;
      default:
        _orderBy = 'created_at';
    }

    switch (_order) {
      case 'desc':
        _order = 'desc';
        break;
      case 'asc':
        _order = 'asc';
        break;
      default:
        _order = 'desc';
    }

    let andWhere: any = [
      {
        deleted_at: null
      }
    ];

    if (serviceId) {
      andWhere.push({
        service_id: serviceId
      });
    }

    if (env) {
      andWhere.push({
        env: env
      });
    }

    if (keywordType && ['url', 'name'].includes(keywordType)) {
      andWhere.push({
        [keywordType]: {
          [Op.like]: `%${keyword}%`
        }
      });
    }
    const {count, rows} = await PageManageDTO.findAndCountAll({
      where: {
        [Op.and]: andWhere
      },
      offset: (_pageNo - 1) * _pageSize,
      limit: _pageSize,
      order: [[_orderBy, _order]],
      attributes: [
        'id',
        'url',
        'name',
        'creator',
        'updater',
        'description',
        ['created_at', 'createdAt'],
        ['updated_at', 'updatedAt'],
        'tags'
      ]
    });

    return {
      data: {
        total: count,
        items: rows
      },
      msg: '获取列表成功',
      success: true
    };
  }
  /**
   * 添加标签
   */
  static async addPageTag(body: {
    env: string;
    serviceId: string;
    tags: string[];
    creator: string;
  }) {
    const {env, tags, creator, serviceId} = body;
    if (!tags || !tags.length || !serviceId) {
      return {
        msg: !serviceId
          ? '添加标签缺少serviceId，请检查'
          : '添加标签缺少tags，请检查',
        success: false
      };
    }

    // 添加tag
    const tagInfo: Array<{tagId: string; tagName: string; isCreated: boolean}> =
      [];
    tags &&
      tags.length &&
      (await Promise.all(
        tags.map(async (tag: string) => {
          const [tagRes, created] = await PageTagDTO.findOrCreate({
            where: {tag_name: tag, service_id: serviceId, env},
            defaults: {
              env,
              service_id: serviceId,
              tag_name: tag,
              creator: creator ?? DEFAULT_CLIENT
            }
          });
          tagInfo.push({
            tagId: String(tagRes.get('tag_id')),
            tagName: String(tagRes.get('tag_name') ?? ''),
            isCreated: created
          });
          return Promise.resolve();
        })
      ));
    return {
      msg: '添加成功',
      data: {tagInfo},
      success: true
    };
  }

  /**
   * 插入url - tag 映射
   */
  static async addPageTagRef(body: {
    env: string;
    tags: Array<{tagId: string; tagName: string}>;
    url: string;
    name: string;
    serviceId: string;
  }) {
    const {env, tags, serviceId, url, name} = body;
    if (!tags || !tags.length || !serviceId || !url) {
      const params = !serviceId ? 'serviceId' : !url ? 'url' : 'tags';

      return {
        msg: `插入url-tag映射缺少参数${params}，请检查`,
        success: false
      };
    }

    try {
      const pageTagRefArr = tags.map(tag => {
        return {
          env,
          tag_id: tag.tagId,
          tag_name: tag.tagName,
          service_id: serviceId,
          name,
          url,
          created_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        };
      });
      await PageTagRefDTO.bulkCreate(pageTagRefArr);
    } catch (err) {}
    return {
      msg: '更新成功',
      success: true
    };
  }

  /**
   * 更新url对应的tag映射
   */
  static async updatePageTagRef(body: {
    env: string;
    tags: Array<{tagId: string; tagName: string; isCreated: boolean}>;
    url: string;
    name: string;
    serviceId: string;
  }) {
    const {env, tags, url, name, serviceId} = body;
    if (!tags || !serviceId || !url) {
      const params = !serviceId ? 'serviceId' : !url ? 'url' : 'tags';

      return {
        msg: `更新url-tag映射缺少参数${params}，请检查`,
        success: false
      };
    }
    // 查找tagName对应的id[Op.in]: [1, 2],
    const allTagAction = await PageTagDTO.findAll({
      where: {
        tag_name: {
          [Op.in]: tags.map(item => item.tagName)
        },
        service_id: serviceId,
        env
      }
    });

    // 查找并删除url对应的tag映射
    await PageTagRefDTO.destroy({
      where: {
        service_id: serviceId,
        url: url,
        env
      }
    });

    tags &&
      tags.length &&
      this.addPageTagRef({
        env,
        serviceId,
        url,
        name,
        tags: allTagAction.map(item => ({
          tagId: item.get('tag_id') as string,
          tagName: item.get('tag_name') as string
        }))
      });

    return {
      msg: '更新成功',
      success: true
    };
  }

  /**
   * 获取tag列表
   */
  static async queryPageTagList(query: {serviceId: string; env?: string}) {
    const {serviceId, env} = query;
    if (!serviceId) {
      return {
        msg: '缺少serviceId，请检查',
        success: false
      };
    }
    const {rows, count} = await PageTagDTO.findAndCountAll({
      where: {
        service_id: serviceId
      },
      attributes: [
        'env',
        'creator',
        ['tag_name', 'tagName'],
        ['tag_id', 'tagId'],
        ['service_id', 'serviceId'],
        ['created_at', 'createdAt']
      ]
    });

    return {
      data: {
        total: count,
        items: rows
      },
      msg: '获取列表成功',
      success: true
    };
  }
  static async getPageInfoTrend(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const { startTime, endTime, env, serviceId } = query;
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: Number(startTime),
            lte: Number(endTime)
          }
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeType = endTime - startTime > 24 * 60 * 60 * 1000 ? 'day' : 'hour';
    const aggs = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: timeType,
          time_zone: 'Asia/Shanghai',
          format: timeType === 'day' ? 'yyyy-MM-dd' : 'HH',
        },
        aggs: {
          uv: {
            cardinality: {
              field: 'baseInfo.userId'
            }
          },
          pv: {
            filter: {
              match: {
                type: 'monitor-pv'
              }
            }
          }
        }
      }
    };

    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });

    const items = result.body.aggregations.group_by_time.buckets.map((item: any) => ({
      key: item.key_as_string,
      uv: item.uv.value,
      pv: item.pv.doc_count
    }));

    return {
      result: items
    };
  }

  static async getPageInfoTrendSearch(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const { startTime, endTime, env, serviceId, appTags } = query;
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: Number(startTime),
            lte: Number(endTime)
          }
        }
      });
    }
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const andWhere = [];
    if (appTags && appTags.length) {
      const tagsOp = appTags.split(',') || [];

      andWhere.push({
        tags: { [Op.in]: tagsOp }
      });
    }
    const appResult = await AppManageDTO.findAndCountAll({
      attributes: [
        ['service_id', 'serviceId'],
      ],
      where: {
        [Op.and]: andWhere
      },
    });
    const serviceIds = appResult?.rows?.map((item: any) => item.dataValues.serviceId);
    esquery.bool.filter.push({
      terms: {
        'baseInfo.serviceId': serviceIds
      }
    });
    const aggs = {
      uv: {
        cardinality: {
          field: 'baseInfo.userId'
        }
      },
      pv: {
        filter: {
          match: {
            type: 'monitor-pv'
          }
        }
      }
    };

    const result: any = await esClient.search(PAGE_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });

    const pv = result.body.aggregations.pv.doc_count;
    const uv = result.body.aggregations.uv.value;
    return {
      pv,
      uv
    }
  }

  static async getPageInfoTrendTotal(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfo: any = await this.getPageInfoTrendSearch(esType, query);
    // 计算环比
    const _baseInfo: any = await this.getPageInfoTrendSearch(esType, {
      ...query,
      startTime: 2 * startTime - endTime,
      endTime: startTime
    });
    return {
      pv: {
        current: baseInfo.pv,
        compare: _baseInfo.pv,
        compareRate: Number((100 * (baseInfo.pv - _baseInfo.pv) / _baseInfo.pv).toFixed(2))
      },
      uv: {
        current: baseInfo.uv,
        compare: _baseInfo.uv,
        compareRate: Number((100 * (baseInfo.uv - _baseInfo.uv) / _baseInfo.uv).toFixed(2))
      },
    };
  }

  static async getPageTop(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const { startTime, endTime, env, type, appTags } = query;
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: Number(startTime),
            lte: Number(endTime)
          }
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const andWhere = [];
    if (appTags && appTags.length) {
      const tagsOp = appTags.split(',') || [];

      andWhere.push({
        tags: { [Op.in]: tagsOp }
      });
    }
    const appResult = await AppManageDTO.findAndCountAll({
      attributes: [
        ['service_id', 'serviceId'],
      ],
      where: {
        [Op.and]: andWhere
      },
    });
    const serviceIds = appResult?.rows?.map((item: any) => item.dataValues.serviceId);
    esquery.bool.filter.push({
      terms: {
        'baseInfo.serviceId': serviceIds
      }
    });
    const typeAgg: any = {
      pv: {
        value: {
          filter: {
            match: {
              type: 'monitor-pv'
            }
          }
        },
        sort: {
          bucket_sort: {
            from: 0,
            size: 10
          }
        }
      },
      uv: {
        value: {
          cardinality: {
            field: 'baseInfo.userId'
          }
        },
        sort: {
          bucket_sort: {
            sort: [
              {
                value: {
                  order: 'desc'
                }
              }
            ],
            from: 0,
            size: 10
          }
        }
      }
    }
    const totalRes: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          total: {
            cardinality: {
              field: 'baseInfo.serviceId'
            }
          }
        }
      }
    });

    const total = totalRes.body?.aggregations?.total?.value;
    const aggs = {
      group_by: {
        terms: {
          field: 'baseInfo.serviceId',
          size: total
        },
        aggs: typeAgg[type]
      }
    };

    const result: any = await esClient.search(PAGE_INDEX, {
      body: {
        query: esquery,
        track_total_hits: true,
        aggs
      }
    });

    const items = result.body.aggregations.group_by.buckets.map((item: any) => ({
      key: item.key,
      value: item.value.doc_count || item.value.value || 0
    }));

    return {
      result: items,
      aa: result
    };
  }

}
