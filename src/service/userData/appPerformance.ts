import {mapping} from '../../mapping/appPerformance';
import {ESClient} from '../../client';
import axios from 'axios';
import {
  PERFORMANCE_INDEX,
  API_INDEX,
  handlerPercent,
  DEFAULT_CLIENT
} from '../../utils';
import { AppManage as AppManageDTO } from '../../model';
import { Op } from 'sequelize';

export class AppPerformance {
  // 根据服务，获取对应的Client类
  static getClient(esType: string) {
    if (esType) {
      return ESClient[esType];
    }
    return ESClient[DEFAULT_CLIENT];
  }
  /*
    获取api索引的mapping
  */
  static async getMapping(esType: string) {
    const esClient = this.getClient(esType);
    const mapping: any = await esClient.getMapping(PERFORMANCE_INDEX);
    const setting: any = await esClient.getSetting(PERFORMANCE_INDEX);
    return {
      mapping: mapping.body[PERFORMANCE_INDEX],
      setting
    };
  }

  static async clearIndex(esType: string) {
    const esClient = this.getClient(esType);
    await esClient.clear(PERFORMANCE_INDEX);
  }
  /*
    获取api索引
  */
  static async setMapping(esType: string) {
    try {
      await this.clearIndex(esType);
    } finally {
      const result: any = await axios({
        url: 'http://10.27.218.69:8903/' + PERFORMANCE_INDEX,
        method: 'PUT',
        data: {
          mappings: {
            ...mapping
          }
        }
      });
      return result;
    }
  }

  /*
    应用启动次数、超过3s启动次数、超过3s占比
  */
  static async getPagePerformanceInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env} = query;
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const performance_aggs: any = {
      timeoutCount: {
        range: {
          field: 'fmp',
          ranges: [
            {
              key: '>3s',
              from: 3000
            }
          ]
        }
      }
    };

    const performance_result: any = await esClient.search(PERFORMANCE_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        track_total_hits: true,
        aggs: performance_aggs
      }
    });

    const totalCount = performance_result.body.hits.total.value;

    const obj: any = {};
    performance_result.body.aggregations.timeoutCount.buckets.forEach(
      (timeItem: any) => {
        obj[timeItem.key] = {
          timeoutCount: timeItem.doc_count,
          timeoutPercent: Number(timeItem.doc_count / totalCount || 0).toFixed(
            6
          )
        };
      }
    );

    return {
      totalCount,
      ...obj['>3s']
    };
  }

  static async getPagePerformanceInfoGap(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfoGap = {
      totalCountGap: '',
      totalCountLast: '',
      timeoutCountGap: '',
      timeoutCountLast: '',
      timeoutPercentGap: '',
      timeoutPercentLast: ''
    };
    const baseInfo = await this.getPagePerformanceInfo(esType, query);
    // 计算环比
    if (startTime) {
      const _baseInfo = await this.getPagePerformanceInfo(esType, {
        ...query,
        startTime: 2 * startTime - endTime,
        endTime: startTime
      });
      const {totalCount, timeoutCount, timeoutPercent} = _baseInfo;
      baseInfoGap.totalCountGap = totalCount
        ? ((baseInfo.totalCount - totalCount) / totalCount).toFixed(6)
        : '';
      baseInfoGap.timeoutCountGap = timeoutCount
        ? ((baseInfo.timeoutCount - timeoutCount) / timeoutCount).toFixed(6)
        : '';
      baseInfoGap.timeoutPercentGap = Number(timeoutPercent)
        ? (
            (Number(baseInfo.timeoutPercent) - Number(timeoutPercent)) /
            Number(timeoutPercent)
          ).toFixed(6)
        : '';
      baseInfoGap.totalCountLast = totalCount;
      baseInfoGap.timeoutCountLast = timeoutCount;
      baseInfoGap.timeoutPercentLast = timeoutPercent;
    }
    return {
      ...baseInfo,
      ...baseInfoGap
    };
  }

  /*
    应用启动次数、超过3s启动次数、超过3s占比趋势
  */
  static async getDailyPagePerformanceInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env} = query;
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const daily_performance_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          calendar_interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          timeoutCount: {
            range: {
              field: 'fmp',
              ranges: [
                {
                  key: '>3s',
                  from: 3000
                }
              ]
            }
          }
        }
      }
    };

    const performance_result: any = await esClient.search(PERFORMANCE_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        track_total_hits: true,
        aggs: daily_performance_aggs
      }
    });

    const items: Array<any> =
      performance_result.body.aggregations.group_by_time.buckets.map(
        (item: any) => {
          const totalCount = item.doc_count;
          const time = item.key_as_string;
          const obj: any = {};
          item.timeoutCount.buckets.forEach((timeItem: any) => {
            obj[timeItem.key] = {
              timeoutCount: timeItem.doc_count,
              timeoutPercent: Number(
                timeItem.doc_count / totalCount || 0
              ).toFixed(6)
            };
          });
          return {
            time,
            totalCount,
            ...obj['>3s']
          };
        }
      );

    return {
      items
    };
  }

  static async getPerformanceInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    const esSort: any = [];
    let {
      id,
      pageNo,
      pageSize,
      startTime,
      endTime,
      browser,
      device,
      userId,
      order,
      orderBy,
      serviceId,
      fpTime,
      fpType,
      fmpTime,
      fmpType,
      fidTime,
      fidType,
      env
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const handlerRange = (type: string, range: string, time: number) => {
      if (time && type) {
        let obj: any = {
          range: {
            [type]: {}
          }
        };
        switch (range) {
          // 大于
          case 'greater':
            obj.range[type].gt = time;
            break;
          // 等于
          case 'equal':
            obj = {
              term: {
                [type]: time
              }
            };
            break;
          // 小于
          case 'less':
            obj.range[type].lt = time;
            break;
          default:
            obj.range[type].gt = time;
            break;
        }
        esQuery.bool.filter.push(obj);
      }
    };
    handlerRange('fp', fpType, fpTime);
    handlerRange('fmp', fmpType, fmpTime);
    handlerRange('fid', fidType, fidTime);

    // 指定用户查询
    if (userId) {
      esQuery.bool.filter.push({
        wildcard: {
          'baseInfo.userId': `*${userId}*`
        }
      });
    }

    // 指定id查询
    if (id) {
      esQuery.bool.filter.push({
        term: {
          _id: id
        }
      });
    }

    // 指定浏览器查询
    if (browser) {
      esQuery.bool.filter.push({
        wildcard: {
          'baseInfo.browser': `*${browser}*`
        }
      });
    }
    // 指定设备查询
    if (device) {
      esQuery.bool.filter.push({
        wildcard: {
          'baseInfo.platform': `*${device}*`
        }
      });
    }
    // 处理排序
    if (order) {
      switch (orderBy) {
        case 'timestamp':
          esSort.push({
            [timeField]: {
              order: order === 'asc' ? order : 'desc'
            }
          });
          break;
        case 'fmp':
          esSort.push({
            fmp: {
              order: order === 'asc' ? order : 'desc'
            }
          });
          break;
        case 'fp':
          esSort.push({
            fp: {
              order: order === 'asc' ? order : 'desc'
            }
          });
          break;
        case 'fid':
          esSort.push({
            fid: {
              order: order === 'asc' ? order : 'desc'
            }
          });
          break;
        default:
          break;
      }
    }

    const result: any = await esClient.search(PERFORMANCE_INDEX, {
      size: perPage,
      from: (page - 1) * perPage,
      body: {
        query: esQuery,
        track_total_hits: true,
        sort: esSort
      }
    });
    const items: Array<any> = result.body.hits.hits.map((item: any) => ({
      ...item._source,
      id: item._id
    }));
    let total = result.body.hits.total;
    if (typeof total !== 'number' && typeof total?.value === 'number') {
      total = total.value;
    }
    return {
      total: total > 10000 ? 10000 : total,
      items
    };
  }

  /*
    获取每日性能分层数据
  */
  static async getPerformanceInfoLaminationDaily(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env, type = 'fmp'} = query;
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const daily_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          time: {
            range: {
              field: type,
              ranges: [
                {
                  key: '0-3000ms',
                  from: 0,
                  to: 3000
                },
                {
                  key: '3000-5000ms',
                  from: 3000,
                  to: 5000
                },
                {
                  key: '5000-7000ms',
                  from: 5000,
                  to: 7000
                },
                {
                  key: '7000-10000ms',
                  from: 7000,
                  to: 10000
                },
                {
                  key: '>10000ms',
                  from: 10000
                }
              ]
            }
          }
        }
      }
    };

    const result: any = await esClient.search(PERFORMANCE_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        track_total_hits: true,
        aggs: daily_aggs
      }
    });

    const items: Array<any> =
      result.body.aggregations.group_by_time.buckets.map((item: any) => {
        const time = item.key_as_string;
        const obj: any = {};
        const valArr = item.time.buckets.map((item: any) => item.doc_count);
        item.time.buckets.forEach((timeItem: any, index: number) => {
          obj[timeItem.key] = handlerPercent(valArr, index, 6);
        });
        return {
          time,
          ...obj
        };
      });

    return {
      items
    };
  }

  /*
    获取性能分布：1分钟内，1s为一个梯度，所有应用首屏耗时的梯度分布
  */
  static async getPerformanceInfoDistributionDaily(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env, type} = query;
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    let rangeType;
    switch (type) {
      case 'fmp':
        rangeType = 'fmp';
        break;
      case 'fp':
        rangeType = 'fp';
        break;
      case 'fid':
        rangeType = 'fid';
        break;
      default:
        rangeType = 'fmp';
        break;
    }

    const daily_aggs: any = {
      group_by_time: {
        range: {
          field: rangeType,
          ranges: [
            {
              key: '0-3000ms',
              from: 0,
              to: 3000
            },
            {
              key: '3000-5000ms',
              from: 3000,
              to: 5000
            },
            {
              key: '5000-7000ms',
              from: 5000,
              to: 7000
            },
            {
              key: '7000-10000ms',
              from: 7000,
              to: 10000
            },
            {
              key: '>10000ms',
              from: 10000
            }
          ]
        }
      }
    };

    const result: any = await esClient.search(PERFORMANCE_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        track_total_hits: true,
        aggs: daily_aggs
      }
    });

    const valArr = result.body.aggregations.group_by_time.buckets.map(
      (item: any) => item.doc_count
    );
    const items: Array<any> =
      result.body.aggregations.group_by_time.buckets.map(
        (item: any, index: number) => {
          const timeRange = item.key;
          const percent = handlerPercent(valArr, index, 6);
          return {
            timeRange,
            percent
          };
        }
      );

    return {
      items
    };
  }

  static async getPerformanceAggsInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    let {
      serviceId,
      env,
      startTime,
      endTime,
      pageNo,
      pageSize,
      aggType,
      order,
      orderBy,
      keyword,
      isFullData
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    let aggsTotalField;
    switch (aggType) {
      case 'user':
        aggsTotalField = 'baseInfo.userId';
        break;
      case 'device':
        aggsTotalField = 'baseInfo.platform';
        break;
      case 'browser':
        aggsTotalField = 'baseInfo.browser';
        break;
      default:
        aggsTotalField = 'baseInfo.userId';
        break;
    }

    if (keyword) {
      esQuery.bool.filter.push({
        wildcard: {
          [aggsTotalField]: `*${keyword}*`
        }
      });
    }

    const totalRes = await esClient.search(PERFORMANCE_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        aggs: {
          total: {
            cardinality: {
              field: aggsTotalField
            }
          }
        }
      }
    });

    const total = totalRes?.body?.aggregations?.total?.value;
    if (!total) {
      return {
        items: [],
        total: 0
      };
    }

    const sortBy = (
      {
        fmpAvg: 'fmpAvg',
        fpAvg: 'fpAvg',
        fidAvg: 'fidAvg',
        openCount: '_count'
      } as any
    )[orderBy || 'fmpAvg'];
    const aggsContent: any = {
      fmpAvg: {
        avg: {
          field: 'fmp'
        }
      },
      fpAvg: {
        avg: {
          field: 'fp'
        }
      },
      fidAvg: {
        avg: {
          field: 'fid'
        }
      },
      sort: {
        bucket_sort: {
          sort: [
            {
              [sortBy]: {
                order: order || 'desc'
              }
            }
          ]
        }
      }
    };

    if (!isFullData) {
      aggsContent.sort.bucket_sort['from'] = perPage * (page - 1);
      aggsContent.sort.bucket_sort['size'] = perPage;
    }

    const result: any = await esClient.search(PERFORMANCE_INDEX, {
      body: {
        query: esQuery,
        aggs: {
          group_by: {
            terms: {
              field: aggsTotalField,
              size: total
            },
            aggs: aggsContent
          }
        }
      }
    });

    const resultList = result.body.aggregations.group_by.buckets || [];
    const resultTotal = result.body.hits.total.value;
    if (!resultTotal) {
      return {
        items: [],
        total: 0
      };
    }
    const items: Array<any> = resultList.map((item: any) => {
      return {
        value: item.key,
        fpAvg: item.fpAvg.value, // 首字节绘制时间（ms）平均数
        fmpAvg: item.fmpAvg.value, // 首屏绘制时间（ms）平均数
        fidAvg: item.fidAvg.value, // 首次可交互时间（ms）平均数
        openCount: item.doc_count, // 应用打开次数
        percent: (item.doc_count / resultTotal).toFixed(6) // 样本量占比
      };
    });

    return {
      items,
      total: total
    };
  }

  /**
   * 获取API性能聚合信息
   *
   * @param query 查询参数
   * @returns 返回包含聚合信息的结果
   */
  static async getApiPerformanceInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: [],
        must_not: [
          {
            term: {
              'responseTime.keyword': ''
            }
          }
        ]
      }
    };
    let {
      serviceId,
      env,
      startTime,
      endTime,
      aggType,
      pageNo,
      pageSize,
      order,
      orderBy,
      keyword,
      isFullData,
      userId,
      apiUrl,
      apiPage,
    } = query;
    const page = parseInt(pageNo, 10) || 1;
    const perPage = parseInt(pageSize, 10) || 10;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    if (userId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esQuery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }

    let aggsTotalField;
    switch (aggType) {
      case 'api':
        aggsTotalField = 'regUrl.keyword';
        break;
      default:
        aggsTotalField = 'regUrl.keyword';
        break;
    }

    if (keyword) {
      esQuery.bool.filter.push({
        wildcard: {
          [aggsTotalField]: `*${keyword}*`
        }
      });
    }

    const totalRes = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        aggs: {
          total: {
            cardinality: {
              field: 'regUrl.keyword'
            }
          }
        }
      }
    });

    const total = totalRes?.body?.aggregations?.total?.value;
    if (!total) {
      return {
        items: [],
        total: 0
      };
    }

    const sortBy = (
      {
        count: 'count',
        averageResponseTime: 'averageResponseTime',
        maxResponseTime: 'maxResponseTime',
        minResponseTime: 'minResponseTime'
      } as any
    )[orderBy || 'averageResponseTime'];

    const aggsContent: any = {
      count: {
        value_count: {
          field: 'regUrl.keyword'
        }
      },
      averageResponseTime: {
        avg: {
          field: 'responseTime'
        }
      },
      maxResponseTime: {
        max: {
          field: 'responseTime'
        }
      },
      minResponseTime: {
        min: {
          field: 'responseTime'
        }
      },
      sort: {
        bucket_sort: {
          sort: [
            {
              [sortBy]: {
                order: order || 'desc'
              }
            }
          ]
        }
      }
    };

    if (!isFullData) {
      aggsContent.sort.bucket_sort['from'] = perPage * (page - 1);
      aggsContent.sort.bucket_sort['size'] = perPage;
    }

    const result: any = await esClient.search(API_INDEX, {
      body: {
        query: esQuery,
        aggs: {
          group_by: {
            terms: {
              field: 'regUrl.keyword',
              size: total
            },
            aggs: aggsContent
          }
        }
      }
    });
    const resultList = result.body.aggregations?.group_by?.buckets || [];
    const items: Array<any> = resultList.map((item: any) => {
      const averageResponseTime = Number(
        item.averageResponseTime.value
      ).toFixed();
      const maxResponseTime = item.maxResponseTime.value;
      const minResponseTime = item.minResponseTime.value;
      const count = item.count.value;
      return {
        value: item.key,
        averageResponseTime,
        maxResponseTime,
        minResponseTime,
        count
      };
    });

    return {
      items,
      total: total
    };
  }

  /**
   * 获取API基础信息性能信息
   *
   * @param query - 查询参数
   * @returns 返回API基础信息性能信息结果集
   */
  static async getApiBaseInfoPerformanceInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: [],
        must_not: [
          {
            term: {
              'responseTime.keyword': ''
            }
          }
        ]
      }
    };
    const {serviceId, env, startTime, endTime, userId, apiUrl, apiPage} = query;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (userId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    if (apiUrl) {
      esQuery.bool.filter.push({
        term: {
          'url.keyword': apiUrl
        }
      });
    }
    if (apiPage) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': apiPage
        }
      });
    }
    // 只有isError是true才认为是报错接口
    const aggs: any = {
      averageResponseTime: {
        avg: {
          field: 'responseTime'
        }
      }
    };
    const result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        track_total_hits: true,
        aggs
      }
    });
    const averageResponseTime =
      result?.body?.aggregations?.averageResponseTime?.value;
    let totalCount = result?.body?.hits?.total;
    if (
      typeof totalCount !== 'number' &&
      typeof totalCount?.value === 'number'
    ) {
      totalCount = totalCount.value;
    }
    return {
      totalCount,
      averageResponseTime
    };
  }

  static async getApiBaseInfoPerformanceInfoGap(esType: string, query: any) {
    const {startTime, endTime} = query;
    const baseInfoGap = {
      totalCountGap: '',
      totalCountLast: '',
      averageResponseTimeGap: '',
      averageResponseTimeLast: ''
    };
    const baseInfo = await this.getApiBaseInfoPerformanceInfo(esType, query);
    // 计算环比
    if (startTime) {
      const _baseInfo = await this.getApiBaseInfoPerformanceInfo(esType, {
        ...query,
        startTime: 2 * startTime - endTime,
        endTime: startTime
      });
      const {totalCount, averageResponseTime} = _baseInfo;
      baseInfoGap.totalCountGap = totalCount
        ? ((baseInfo.totalCount - totalCount) / totalCount).toFixed(6)
        : '';

      baseInfoGap.averageResponseTimeGap = Number(averageResponseTime)
        ? (
            (Number(baseInfo.averageResponseTime) -
              Number(averageResponseTime)) /
            Number(averageResponseTime)
          ).toFixed(6)
        : '';
      baseInfoGap.totalCountLast = totalCount;
      baseInfoGap.averageResponseTimeLast = averageResponseTime;
    }
    return {
      ...baseInfo,
      ...baseInfoGap
    };
  }

  static async getDailyApiBaseInfoPerformanceInfo(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: [],
        must_not: [
          {
            term: {
              'responseTime.keyword': ''
            }
          }
        ]
      }
    };
    const {serviceId, env, startTime, endTime} = query;

    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    // 基于日期聚合后再基于isError字段聚合
    const daily_error_aggs: any = {
      group_by_time: {
        date_histogram: {
          field: timeField,
          interval: 'day',
          time_zone: 'Asia/Shanghai',
          format: 'yyyy-MM-dd'
        },
        aggs: {
          averageResponseTime: {
            avg: {
              field: 'responseTime'
            }
          }
        }
      }
    };

    // 一段时间内每天的报错信息
    const daily_error_result: any = await esClient.search(API_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        aggs: daily_error_aggs,
        track_total_hits: true
      }
    });
    const items: Array<any> =
      daily_error_result.body.aggregations.group_by_time.buckets.map(
        (item: any) => {
          const averageResponseTime = Number(
            item.averageResponseTime.value
          ).toFixed();
          const totalCount = item.doc_count;
          const time = item.key_as_string;
          return {
            time,
            averageResponseTime,
            totalCount
          };
        }
      );
    return {
      items
    };
  }

  /*
    【页面性能】首屏时间中位数请求资源list
  */
  static async getMedianResourceList(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    const {startTime, endTime, serviceId, env} = query;
    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    const performance_aggs: any = {
      fmpMedian: {
        percentiles: {
          field: 'fmp',
          percents: [50]
        }
      }
    };

    const performance_result: any = await esClient.search(PERFORMANCE_INDEX, {
      size: 0,
      body: {
        query: esQuery,
        track_total_hits: true,
        aggs: performance_aggs
      }
    });

    const fmpMedian = Number(
      performance_result.body.aggregations.fmpMedian.values['50.0']
    );

    const result: any = await esClient.search(PERFORMANCE_INDEX, {
      size: 1,
      body: {
        query: esQuery,
        track_total_hits: true,
        sort: {
          _script: {
            type: 'number',
            script: {
              source: "Math.abs(doc['fmp'].value - params.median)",
              lang: 'painless',
              params: {
                median: fmpMedian
              }
            }
          }
        }
      }
    });

    const item = result.body.hits.hits[0]?._source || {};

    return {
      item
    };
  }

  static async getPerformanceTop(esType: string, query: any) {
    const esClient = this.getClient(esType);
    const esQuery: any = {
      bool: {
        filter: []
      }
    };
    let {
      env,
      startTime,
      endTime,
      isFullData,
      appTags
    } = query;

    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const andWhere = [];
    if (appTags && appTags.length) {
      const tagsOp = appTags.split(',') || [];

      andWhere.push({
        tags: { [Op.in]: tagsOp }
      });
    }
    const appResult = await AppManageDTO.findAndCountAll({
      attributes: [
        ['service_id', 'serviceId'],
      ],
      where: {
        [Op.and]: andWhere
      },
    });
    const serviceIds = appResult?.rows?.map((item: any) => item.dataValues.serviceId);
    esQuery.bool.filter.push({
      terms: {
        'baseInfo.serviceId': serviceIds
      }
    });

    const aggsContent: any = {
      fmpAvg: {
        avg: {
          field: 'fmp'
        }
      },
      sort: {
        bucket_sort: {
          sort: [
            {
              fmpAvg: {
                order: 'desc'
              }
            }
          ]
        }
      }
    };

    const result: any = await esClient.search(PERFORMANCE_INDEX, {
      body: {
        query: esQuery,
        aggs: {
          group_by: {
            terms: {
              field: 'baseInfo.serviceId'
            },
            aggs: aggsContent
          }
        }
      }
    });

    const items = result.body.aggregations?.group_by?.buckets?.map((item: any) => {
      return {
        key: item.key,
        value: item.fmpAvg?.value || 0
      }
    })

    return {
      result: items
    };
  }

}
