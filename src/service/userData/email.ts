import {Container} from 'typedi';
import moment from 'moment';
import {Moment} from 'moment';

import ejs from 'ejs';
import path from 'path';
import fs from 'fs';

import {MailClient} from '../../client/email';
import {Base} from './base';
import {Api} from './api';
import {isProd} from '../../utils';
import {Emails as EmailsDTO} from '../../model';
import {BosClient} from '../../client';

import {
  allServiceList,
  specialProjectServiceList,
  newEsServiceListPhase1,
  CONSOLE_CLIENT,
  mailInfoMap
} from '../../utils/const';
import {getAllTicketList} from './ticket';

interface MailOptions {
  to: string | Array<string>;
  subject: string;
  html?: string;
  text?: string;
  bcc?: string | Array<string>;
}
const IaasMailSchema = [
  {
    title: `所有产品页面api调用异常情况`,
    th: [
      'API调用量',
      'API异常量',
      'API异常率',
      '上周API调用量',
      '上周API异常量',
      '上周API异常率',
      '环比'
    ],
    td: [
      'totalCount',
      'totalErrCount',
      'totalErrPercentStr',
      'totalCountLast',
      'totalErrCountLast',
      'totalErrPercentLastStr',
      'totalErrGapStr'
    ],
    sourceDataIndex: 'completeData' // 对应sourceDataErrTop
  },
  {
    title: `产品页面api调用异常率top5`,
    th: ['产品名称', '页面API异常量', 'API异常率'],
    td: ['serviceNameTag', 'errCount', 'errPercentStr'],
    sourceDataIndex: 'sourceDataErrTop5' // 对应sourceDataErrTop
  },
  {
    title: `产品页面api调用稳定性top5`,
    th: ['产品名称', ' API调用量', 'API异常率'],
    td: ['serviceNameTag', 'totalCount', 'errPercentStr'],
    sourceDataIndex: 'sourceDataErrRise5' // 对应sourceDataErrRise
  },
  {
    title: '产品页面api调用异常率上升top5',
    th: ['产品名称', 'API异常率', '上周API异常率', '环比'],
    td: [
      'serviceNameTag',
      'errPercentStr',
      'errPercentLastStr',
      'errPercentGapStr'
    ],
    sourceDataIndex: 'sourceDataErrGapTop5' // sourceDataErrGapTop
  },
  {
    title: '产品页面api调用异常率下降top5',
    th: ['产品名称', 'API异常率', '上周API异常率', '环比'],
    td: [
      'serviceNameTag',
      'errPercentStr',
      'errPercentLastStr',
      'errPercentGapStr'
    ],
    sourceDataIndex: 'sourceDataErrGapRise5' // sourceDataErrGapRise
  },
  {
    title: '产品页面API稳定性全量数据',
    th: [
      '产品名称',
      'API异常量',
      'API调用量',
      'API异常率',
      '异常率环比上周',
      'CTS工单',
      '工单环比上周'
    ],
    td: [
      'serviceNameTag',
      'errCount',
      'totalCount',
      'errPercentStr',
      'errPercentGapStr',
      'ticketCount',
      'ticketGapStr'
    ],
    sourceDataIndex: 'allData' // globalData
  }
];

const specialProjectSchema = [
  {
    title: `所有产品页面api调用异常情况`,
    th: [
      '调用量',
      '报错量',
      '报错率',
      '环比上周',
      '非中文报错数量',
      '非中文报错占比',
      '环比上周'
    ],
    td: [
      'totalCount',
      'totalErrCount',
      'totalErrPercentStr',
      'totalErrGapStr',
      'totalEnErrCount',
      'totalEnErrPercentStr',
      'totalEnErrGapStr'
    ],
    sourceDataIndex: 'completeData' // 对应sourceDataErrTop
  },
  {
    title: '各业务方向产品页面API稳定性数据',
    th: [
      '业务方向',
      '调用量',
      '报错量',
      '报错率',
      '环比上周',
      '非中文报错数量',
      '非中文报错占比',
      '环比上周'
    ],
    td: [
      'groupName',
      'totalCount',
      'totalErrCount',
      'totalErrPercentStr',
      'totalErrGapStr',
      'totalEnErrCount',
      'totalEnErrPercentStr',
      'totalEnErrGapStr'
    ],
    sourceDataIndex: 'groupSourceDataTotalByBusinessLineArr' // globalData
  },
  {
    title: `产品页面api调用异常率top5`,
    th: ['产品名称', '页面API异常量', 'API异常率'],
    td: ['serviceNameTag', 'errCount', 'errPercentStr'],
    sourceDataIndex: 'sourceDataErrTop5' // 对应sourceDataErrTop
  },
  {
    title: '产品页面api调用异常率上升top5',
    th: ['产品名称', 'API异常率', '上周API异常率', '环比'],
    td: [
      'serviceNameTag',
      'errPercentStr',
      'errPercentLastStr',
      'errPercentGapStr'
    ],
    sourceDataIndex: 'sourceDataErrGapTop5' // sourceDataErrGapTop
  },
  {
    title: '所有产品页面API非中文报错数据',
    th: [
      '产品名称',
      '基线',
      '优化目标',
      'API报错量',
      '非中文报错占比',
      '环比上周',
      '距目标的gap值'
    ],
    td: [
      'serviceNameTag',
      'baseEnErrorPercentStr',
      'targetEnErrorPercentStr',
      'errCount',
      'thisWeekEnErrorPercentStr',
      'lastEnErrorPercentGapStr',
      'targetEnErrorPercentGapStr'
    ],
    sourceDataIndex: 'allData' // globalData
  },
  {
    title: '所有产品页面API稳定性数据',
    th: [
      '产品名称',
      '基线',
      '优化目标',
      'API调用量',
      'API异常率',
      '环比上周',
      '距目标的gap值'
    ],
    td: [
      'serviceNameTag',
      'baseErrPercentStr',
      'targetErrorPercentStr',
      'totalCount',
      'errPercentStr',
      'errPercentGapStr',
      'targetErrPercentGapStr'
    ],
    sourceDataIndex: 'allData' // globalData
  }
];

const renderHtml = async (
  curTimeRange: string,
  lastTimeRange: string,
  sourceData: any[],
  type = 'iaas'
) => {
  // 根据产品报错率，降序排序
  const sourceDataErrTop = [...sourceData].sort((pre: any, cur: any) => {
    return cur.errPercent - pre.errPercent;
  });
  // 根据产品报错率变化，降序排序
  const sourceDataErrGapTop = [...sourceData].sort((pre: any, cur: any) => {
    return cur.errPercentGap - pre.errPercentGap;
  });

  // 所有产品的全量数据统计
  let totalErrCount: number = 0;
  let totalErrCountLast: number = 0;
  let totalEnErrCount: number = 0;
  let totalEnErrCountLast: number = 0;
  let totalCount: number = 0;
  let totalCountLast: number = 0;
  sourceData.forEach(item => {
    totalCount += item.totalCount || 0;
    totalErrCount += item.errCount || 0;
    totalEnErrCount += item.thisWeekEnErrorCount || 0;
    totalEnErrCountLast += item.laseWeekEnErrorCount || 0;
    totalCountLast += item.totalCountLast || 0;
    totalErrCountLast += item.errCountLast || 0;
  });
  const totalErrPercent = Number(
    ((totalErrCount * 100) / totalCount).toFixed(2)
  );
  const totalErrPercentStr = totalErrPercent + '%';
  const totalErrPercentLast = Number(
    ((totalErrCountLast * 100) / totalCountLast).toFixed(2)
  );
  const totalErrPercentLastStr = totalErrPercentLast + '%';

  const totalEnErrPercent = Number(
    ((totalEnErrCount * 100) / totalErrCount).toFixed(2)
  );
  const totalEnErrPercentStr = totalEnErrPercent + '%';
  const totalEnErrPercentLast = Number(
    ((totalEnErrCountLast * 100) / totalErrCountLast).toFixed(2)
  );
  const totalEnErrPercentLastStr = totalEnErrPercentLast + '%';
  const totalErrGap =
    (totalErrPercent - totalErrPercentLast) / totalErrPercentLast;
  const totalErrGapStr = totalErrGap
    ? `<span style="color: ${totalErrGap > 0 ? 'red' : 'green'}">${
        totalErrGap > 0 ? '+' : ''
      }${(totalErrGap * 100).toFixed(1)}%</span>`
    : totalErrPercentLast
    ? '<span style="color: gray;">数据无变化</span>'
    : '<span style="color: gray;">上周为0</span>';

  const totalEnErrGap = totalEnErrPercentLast
    ? (totalEnErrPercent - totalEnErrPercentLast) / totalEnErrPercentLast
    : null;
  const totalEnErrGapStr = totalEnErrGap
    ? `<span style="color: ${totalEnErrGap > 0 ? 'red' : 'green'}">${
        totalEnErrGap > 0 ? '+' : ''
      }${(totalEnErrGap * 100).toFixed(1)}%</span>`
    : totalEnErrPercentLast
    ? '<span style="color: gray;">数据无变化</span>'
    : '<span style="color: gray;">上周为0</span>';
  // 区分模板类型，渲染不同的模板
  const templateMap: any = {
    iaas: 'mail_iaas.ejs',
    iaaspm: 'mail_iaas_pm.ejs',
    special: 'mail_normal.ejs'
  };
  const templateName = templateMap[type] || 'mail_normal.ejs';
  const templateSchema =
    type === 'iaas' || type === 'iaaspm'
      ? IaasMailSchema
      : specialProjectSchema;

  // 产品按照所属业务线分组统计，只有优化专项的产品才会指定groupType、groupName
  const groupSourceDataTotalByBusinessLine = sourceData.reduce((res, item) => {
    if (item.groupType && res[item.groupType]) {
      res[item.groupType].totalCount += item.totalCount || 0;
      res[item.groupType].totalErrCount += item.errCount || 0;
      res[item.groupType].totalCountLast += item.totalCountLast || 0;
      res[item.groupType].totalErrCountLast += item.errCountLast || 0;
      res[item.groupType].totalEnErrCount += item.thisWeekEnErrorCount || 0;
      res[item.groupType].totalEnErrCountLast += item.laseWeekEnErrorCount || 0;
    } else if (item.groupType) {
      res[item.groupType] = {};
      res[item.groupType].totalCount = item.totalCount || 0;
      res[item.groupType].totalErrCount = item.errCount || 0;
      res[item.groupType].totalCountLast = item.totalCountLast || 0;
      res[item.groupType].totalErrCountLast = item.errCountLast || 0;
      res[item.groupType].totalEnErrCount = item.thisWeekEnErrorCount || 0;
      res[item.groupType].totalEnErrCountLast = item.laseWeekEnErrorCount || 0;
      res[item.groupType].groupName = item.groupName;
    }
    return res;
  }, {});
  const groupSourceDataTotalByBusinessLineArr: Array<any> = [];
  for (let key in groupSourceDataTotalByBusinessLine) {
    const item = groupSourceDataTotalByBusinessLine[key];
    // 本周报错率
    item.totalErrPercent = item.totalCount
      ? Number(((item.totalErrCount * 100) / item.totalCount).toFixed(4))
      : 0;
    item.totalErrPercentStr = item.totalErrPercent + '%';
    // 上周报错率
    item.totalErrPercentLast = item.totalCountLast
      ? Number(
          ((item.totalErrCountLast * 100) / item.totalCountLast).toFixed(4)
        )
      : 0;

    // 报错率环比上周
    item.totalErrGap = item.totalErrPercentLast
      ? (item.totalErrPercent - item.totalErrPercentLast) /
        item.totalErrPercentLast
      : null;
    const errTip = item.totalErrPercentLast === 0 ? '上周为0' : '报错率无变化';
    item.totalErrGapStr = item.totalErrGap
      ? `<span style="color: ${item.totalErrGap > 0 ? 'red' : 'green'}">${
          item.totalErrGap > 0 ? '+' : ''
        }${(item.totalErrGap * 100).toFixed(1)}%</span>`
      : `<span style="color: gray;">${errTip}</span>`;

    // 本周报错中文占比
    item.totalEnErrPercent = item.totalErrCount
      ? Number(((item.totalEnErrCount * 100) / item.totalErrCount).toFixed(2))
      : 0;
    item.totalEnErrPercentStr = item.totalEnErrPercent + '%';
    // 上周报错中文占比
    item.totalEnErrPercentLast = item.totalErrCountLast
      ? Number(
          ((item.totalEnErrCountLast * 100) / item.totalErrCountLast).toFixed(2)
        )
      : 0;

    // 中文报错占比环比上周
    item.totalEnErrGap = item.totalEnErrPercentLast
      ? (item.totalEnErrPercent - item.totalEnErrPercentLast) /
        item.totalEnErrPercentLast
      : null;
    const errEnTip =
      item.totalEnErrPercentLast === 0 ? '上周为0' : '报错率无变化';
    item.totalEnErrGapStr = item.totalEnErrGap
      ? `<span style="color: ${item.totalEnErrGap > 0 ? 'red' : 'green'}">${
          item.totalEnErrGap > 0 ? '+' : ''
        }${(item.totalEnErrGap * 100).toFixed(1)}%</span>`
      : `<span style="color: gray;">${errEnTip}</span>`;
    groupSourceDataTotalByBusinessLineArr.push(item);
  }
  // 产品按照所属业务线分组统计 end

  return new Promise((resolve, reject) => {
    fs.readFile(
      path.join(process.cwd(), `./src/template/${templateName}`),
      'utf8',
      (err, template) => {
        if (err) {
          reject(err);
          return;
        }
        try {
          const trendStartDate = moment()
            .subtract(8, 'weeks')
            .startOf('week')
            .add(4, 'days')
            .format('YYYYMMDD');
          const trendEndDate = moment()
            .subtract(0, 'weeks')
            .startOf('week')
            .add(4, 'days')
            .format('YYYYMMDD');
          const html = ejs.render(
            template,
            {
              trendTitle: `${trendStartDate}~${trendEndDate}`,
              curTimeRange,
              lastTimeRange,
              mailSchema: templateSchema,
              ticketInfo: sourceData.filter(item => {
                return item.ticketCount || item.ticketCountLast;
              }),
              sourceData: {
                completeData: [
                  {
                    totalCount,
                    totalCountLast,
                    totalErrCount,
                    totalErrCountLast,
                    totalEnErrCount,
                    totalEnErrCountLast,
                    totalErrPercentStr,
                    totalErrPercentLastStr,
                    totalEnErrPercentStr,
                    totalEnErrPercentLastStr,
                    totalErrGapStr,
                    totalEnErrGapStr
                  }
                ],
                groupSourceDataTotalByBusinessLineArr,
                sourceDataErrTop5: sourceDataErrTop.slice(0, 5),
                sourceDataErrRise5: sourceDataErrTop.reverse().slice(0, 5),
                sourceDataErrGapTop5: sourceDataErrGapTop
                  .slice(0, 5)
                  .filter(item => item.errPercentGap > 0),
                sourceDataErrGapRise5: sourceDataErrGapTop
                  .reverse()
                  .slice(0, 5)
                  .filter(item => item.errPercentGap < 0),
                // 所有原始数据
                allData: sourceData
              }
            },
            {}
          );
          resolve(html);
          // 生成一份html文件，方便调试
          // fs.writeFileSync(
          //   path.join(process.cwd(), './src/template/mail.html'),
          //   html
          // );
        } catch (error) {
          reject(error);
        }
      }
    );
  });
};
const renderTrendHtml = async (sourceData: any[], type: string) => {
  return new Promise((resolve, reject) => {
    fs.readFile(
      path.join(process.cwd(), `./src/template/mail_error_trend.ejs`),
      'utf8',
      (err, template) => {
        if (err) {
          reject(err);
          return;
        }
        try {
          const html = ejs.render(
            template,
            {
              serviceInfoList: JSON.stringify(sourceData)
            },
            {}
          );
          resolve(html);

          const startDate = moment()
            .subtract(8, 'weeks')
            .startOf('week')
            .add(4, 'days')
            .format('YYYYMMDD');
          const endDate = moment()
            .subtract(0, 'weeks')
            .startOf('week')
            .add(4, 'days')
            .format('YYYYMMDD');
          // 生成一份html文件，方便调试
          fs.writeFileSync(
            path.join(
              process.cwd(),
              `./src/template/${type}-${startDate}~${endDate}.html`
            ),
            html
          );
        } catch (error) {
          reject(error);
        }
      }
    );
  });
};
const utcFormatter = (time: Moment) => {
  return moment(time).utc().format('YYYY-MM-DDTHH:mm:ss') + 'Z';
};
export class BaiduMail {
  // 上周一
  private static defaultStartTime: Moment;
  // 上周日
  private static defaultEndTime: Moment;
  // 上上周一
  private static defaultStartTimeLast: Moment;
  // 上上周日
  private static defaultEndTimeLast: Moment;

  static async generateMailData(services: string[], type: string) {
    const startTimeMap: any = {
      iaas: moment()
        .subtract(1, 'weeks')
        .startOf('week')
        .add(4, 'days') // 上周四十二点
        .add(12, 'hours'),
      iaaspm: moment()
        .subtract(1, 'weeks')
        .startOf('week')
        .add(4, 'days') // 上周四十二点
        .add(12, 'hours'),
      special: moment()
        .subtract(1, 'weeks')
        .startOf('week')
        .add(1, 'days')
        .startOf('day')
    };

    const endimeMap: any = {
      iaas: moment()
        .startOf('week')
        .add(4, 'days') // 本周四十二点
        .add(12, 'hours'),
      iaaspm: moment()
        .startOf('week')
        .add(4, 'days') // 本周四十二点
        .add(12, 'hours'),
      special: moment()
        .subtract(1, 'weeks')
        .endOf('week')
        .add(1, 'days')
        .endOf('day')
    };

    this.defaultStartTime =
      startTimeMap[type] ||
      moment() // 上周五零点
        .subtract(1, 'weeks')
        .startOf('week')
        .add(5, 'days')
        .startOf('day');

    this.defaultEndTime =
      endimeMap[type] || moment().startOf('week').add(5, 'days').startOf('day'); // 本周五零点

    this.defaultStartTimeLast = moment(this.defaultStartTime).subtract(
      1,
      'weeks'
    );

    this.defaultEndTimeLast = moment(this.defaultEndTime).subtract(1, 'weeks');
    const specialProjectData: any = {};
    // 只有专项数据需要拉中文报错数据以及基线报错数据
    if (type === 'special') {
      for (let serviceId of services) {
        const serviceInfo = specialProjectServiceList.find(
          item => item.serviceId === serviceId
        );
        const baseTimeRange = serviceInfo?.baseTimeRange;
        // Q4以后数据都从新集群查
        const esType = CONSOLE_CLIENT;
        if (!serviceInfo || !baseTimeRange) {
          continue;
        }
        await Promise.all([
          // 查基线报错数据
          Api.getApiInfo(esType, {
            serviceId,
            env: 'consoleOnline',
            startTime: baseTimeRange[0] * 1000,
            endTime: baseTimeRange[1] * 1000
          }),
          // 查基线中文报错数据
          Api.getApiLocaleInfo(esType, {
            serviceId,
            env: 'consoleOnline',
            startTime: baseTimeRange[0] * 1000,
            endTime: baseTimeRange[1] * 1000,
            locale: 'zh-CN'
          }),
          // 查本周中文报错数据
          Api.getApiLocaleInfo(CONSOLE_CLIENT, {
            serviceId,
            env: 'consoleOnline',
            startTime: this.defaultStartTime.valueOf(),
            endTime: this.defaultEndTime.valueOf(),
            locale: 'zh-CN'
          }),
          // 查上周中文报错数据
          Api.getApiLocaleInfo(CONSOLE_CLIENT, {
            serviceId,
            env: 'consoleOnline',
            startTime: this.defaultStartTimeLast.valueOf(),
            endTime: this.defaultEndTimeLast.valueOf(),
            locale: 'zh-CN'
          })
        ]).then((res: any[]) => {
          specialProjectData[serviceId] = {
            groupName: serviceInfo.groupName,
            groupType: serviceInfo.groupType,
            baseErrCount: res[0].errCount,
            baseCount: res[0].totalCount,
            baseErrPercent: Number(res[0].errPercent),
            baseErrPercentStr: Number(res[0].errPercent * 100).toFixed(4) + '%',
            baseEnErrorCount: res[1].errCount,
            baseEnErrorPercent: Number(res[1].errPercent),
            baseEnErrorPercentStr:
              Number(res[1].errPercent * 100).toFixed(4) + '%',
            thisWeekEnErrorCount: res[2].errCount,
            thisWeekEnErrorPercent: Number(res[2].errPercent),
            thisWeekEnErrorPercentStr:
              Number(res[2].errPercent * 100).toFixed(4) + '%',
            laseWeekEnErrorCount: res[3].errCount,
            laseWeekEnErrorPercent: Number(res[3].errPercent),
            laseWeekEnErrorPercentStr:
              Number(res[3].errPercent * 100).toFixed(4) + '%',

            targetErrorPercent: serviceInfo.targetErrorPercent,
            targetErrorPercentStr: serviceInfo.targetErrorPercent
              ? Number(serviceInfo.targetErrorPercent * 100).toFixed(4) + '%'
              : '<span style="color: gray">未设定目标</span>',
            targetEnErrorPercent: serviceInfo.targetEnErrorPercent,
            targetEnErrorPercentStr: serviceInfo.targetEnErrorPercent
              ? Number(serviceInfo.targetEnErrorPercent * 100).toFixed(4) + '%'
              : '<span style="color: gray">未设定目标</span>'
          };
        });
      }
    }

    // 获取当前周期和上周的工单数据
    const tickets = await getAllTicketList({
      serviceTypes: services,
      startTime: utcFormatter(this.defaultStartTime),
      endTime: utcFormatter(this.defaultEndTime)
    });
    const ticketsLast = await getAllTicketList({
      serviceTypes: services,
      startTime: utcFormatter(this.defaultStartTimeLast),
      endTime: utcFormatter(this.defaultEndTimeLast)
    });
    // 获取当前周期和上周的稳定性数据
    const sourceData = await Base.getOverviewCompareData(
      CONSOLE_CLIENT,
      CONSOLE_CLIENT,
      {
        apps: services,
        env: 'consoleOnline',
        aggType: 4,
        startTime: this.defaultStartTime.valueOf(),
        endTime: this.defaultEndTime.valueOf(),
        compareStartTime: this.defaultStartTimeLast.valueOf(),
        compareEndTime: this.defaultEndTimeLast.valueOf(),
        orderBy: 'totalCount',
        order: 'desc'
      }
    );
    const findServiceTicketInfo = (source: any[], serviceId: string) => {
      return (
        source.find(
          (ticketInfo: any) =>
            ticketInfo.serviceType.toUpperCase() === serviceId.toUpperCase()
        ) || {}
      );
    };
    // 整理数据结构
    return {
      curTimeRange: `${this.defaultStartTime.format(
        'YYYY-MM-DD HH:mm:ss'
      )} ~ ${this.defaultEndTime.format('YYYY-MM-DD HH:mm:ss')}`,
      lastTimeRange: `${this.defaultStartTimeLast.format(
        'YYYY-MM-DD HH:mm:ss'
      )} ~ ${this.defaultEndTimeLast.format('YYYY-MM-DD HH:mm:ss')}`,
      sourceData: sourceData.map(item => {
        const ticketCount =
          findServiceTicketInfo(tickets, item.serviceId)?.total || 0;
        const ticketCountLast =
          findServiceTicketInfo(ticketsLast, item.serviceId)?.total || 0;
        const ticketGap = ticketCountLast
          ? (ticketCount - ticketCountLast) / ticketCountLast
          : 0;
        const ticketGapStr = ticketGap
          ? `<span style="color: ${ticketGap > 0 ? 'red' : 'green'}">${
              ticketGap > 0 ? '+' : ''
            }${(ticketGap * 100).toFixed(1)}%</span>`
          : ticketCountLast
          ? '<span style="color: gray;">数据无变化</span>'
          : '<span style="color: gray;">上周为0</span>';
        const serviceName =
          allServiceList.find(info => item.serviceId === info.serviceId)
            ?.serviceName || '';
        // const serviceNameTag = `<a href="https://pagemaker.baidu-int.com/company/console/app/-dev/design/monitor/stability-report">${serviceName}</a>`;
        const serviceNameTag = `<a href="https://yunqiao.baidu-int.com/analysis/${item.serviceId.toLowerCase()}/consoleOnline/stability-report?timeRange=${Math.floor(
          this.defaultStartTime.valueOf() / 1000
        )}%2C${Math.floor(
          this.defaultEndTime.valueOf() / 1000
        )}">${serviceName}</a>`;

        const specialServiceData = specialProjectData[item.serviceId] || {};
        if (specialServiceData) {
          // 先不用环比base数据
          // specialServiceData.baseErrPercentGap =
          //   specialServiceData.baseErrPercent
          //     ? (item.errPercent - specialServiceData.baseErrPercent) /
          //       specialServiceData.baseErrPercent
          //     : 0;
          // specialServiceData.baseErrPercentGapStr =
          //   specialServiceData.baseErrPercentGap
          //     ? `<span style="color: ${
          //         specialServiceData.baseErrPercentGap > 0 ? 'red' : 'green'
          //       }">${specialServiceData.baseErrPercentGap > 0 ? '+' : ''}${(
          //         specialServiceData.baseErrPercentGap * 100
          //       ).toFixed(1)}%</span>`
          //     : '--';

          // 相对优化目标，当前的完成度
          specialServiceData.targetErrPercentGap =
            specialServiceData.targetErrorPercent
              ? specialServiceData.targetErrorPercent / item.errPercent
              : null;
          // 距离优化目标的gap
          specialServiceData.targetErrPercentGapStr =
            specialServiceData.targetErrPercentGap === null
              ? (specialServiceData.targetErrPercentGapStr =
                  '<span style="color: gray">未设定目标</span>')
              : specialServiceData.targetErrPercentGap < 1
              ? `<span style="color: red">${(
                  (1 - specialServiceData.targetErrPercentGap) *
                  100
                ).toFixed(1)}%</span>`
              : '<span style="color: blue">目标已达成</span>';
          // 环比上周的中文报错提升
          specialServiceData.lastEnErrorPercentGap =
            specialServiceData.laseWeekEnErrorPercent
              ? (specialServiceData.thisWeekEnErrorPercent -
                  specialServiceData.laseWeekEnErrorPercent) /
                specialServiceData.laseWeekEnErrorPercent
              : 0;
          // 环比上周的中文报错提升
          specialServiceData.lastEnErrorPercentGapStr =
            specialServiceData.lastEnErrorPercentGap
              ? `<span style="color: ${
                  specialServiceData.lastEnErrorPercentGap > 0 ? 'red' : 'green'
                }">${specialServiceData.lastEnErrorPercentGap > 0 ? '+' : ''}${(
                  specialServiceData.lastEnErrorPercentGap * 100
                ).toFixed(1)}%</span>`
              : specialServiceData.laseWeekEnErrorPercent
              ? '<span style="color: gray;">数据无变化</span>'
              : '<span style="color: gray;">上周为0</span>';
          // 中文报错相对优化目标，当前的完成度
          specialServiceData.targetEnErrorPercentGap =
            specialServiceData.targetEnErrorPercent
              ? specialServiceData.targetEnErrorPercent /
                specialServiceData.thisWeekEnErrorPercent
              : null;
          // 中文报错相对优化目标，当前的gap
          specialServiceData.targetEnErrorPercentGapStr =
            specialServiceData.targetEnErrorPercentGap === null
              ? (specialServiceData.targetEnErrorPercentGapStr =
                  '<span style="color: gray">未设定目标</span>')
              : specialServiceData.targetEnErrorPercentGap < 1
              ? `<span style="color: red">${(
                  (1 - specialServiceData.targetEnErrorPercentGap) *
                  100
                ).toFixed(1)}%</span>`
              : '<span style="color: blue">目标已达成</span>';
        }
        return {
          ...item,
          ...specialServiceData,
          serviceName,
          serviceNameTag,
          serviceId: item.serviceId.toUpperCase(),
          ticketCount,
          ticketCountLast,
          ticketInfo:
            findServiceTicketInfo(tickets, item.serviceId)
              ?.featureCounts?.map((countItem: any) => {
                return `${countItem.featureName}: ${countItem.count}`;
              })
              ?.join('<br>') || '--',
          ticketInfoLast:
            findServiceTicketInfo(ticketsLast, item.serviceId)
              ?.featureCounts?.map((countItem: any) => {
                return `${countItem.featureName}: ${countItem.count}`;
              })
              ?.join('<br>') || '--',
          ticketGap,
          ticketGapStr,
          errPercentStr: Number(item.errPercent * 100).toFixed(4) + '%',
          errPercentLastStr: Number(item.errPercentLast * 100).toFixed(4) + '%',
          errPercentGapStr: item.errPercentGap
            ? `<span style="color: ${
                item.errPercentGap > 0 ? 'red' : 'green'
              }">${item.errPercentGap > 0 ? '+' : ''}${
                item.errPercentGap
              }%</span>`
            : item.errPercentLast
            ? '<span style="color: gray;">数据无变化</span>'
            : '<span style="color: gray;">上周为0</span>'
        };
      })
    };
  }
  static async sendEmail(
    mailOption: MailOptions,
    services: string[],
    type = 'iaas'
  ) {
    // 默认对比上周和上上周的数据
    let {sourceData, curTimeRange, lastTimeRange} =
      await BaiduMail.generateMailData(services, type);
    const mailService = Container.get(MailClient);
    // 渲染邮件内容
    const mailHtml = await renderHtml(
      curTimeRange,
      lastTimeRange,
      sourceData,
      type
    );
    // 发送邮件
    await mailService.sendMail({
      ...mailOption,
      html: mailHtml
    });
  }

  static async getErrorTrendData(
    services: string[],
    startTime: number,
    endTime: number
  ) {
    const result: any = {};
    // 查本周报错数据
    const errorList = await Base.getOverviewList(CONSOLE_CLIENT, {
      apps: services,
      env: 'consoleOnline',
      aggType: 4,
      startTime: startTime,
      endTime: endTime,
      orderBy: 'totalCount',
      order: 'desc'
    });
    errorList.items.forEach((item: any) => {
      result[item.serviceId] = {
        errorPercent: item.errPercent || 0,
        totalCount: item.totalCount || 0,
        errorCount: item.errCount || 0
      };
    });
    // 查中文报错数据不支持批量查询
    for (const serviceId of services) {
      const enError = await Api.getApiLocaleInfo(CONSOLE_CLIENT, {
        serviceId,
        env: 'consoleOnline',
        startTime: startTime,
        endTime: endTime,
        locale: 'zh-CN'
      });
      if (!result[serviceId]) {
        result[serviceId] = {errorPercent: 0};
      }
      result[serviceId].enErrorPercent = enError.errPercent || 0;
      result[serviceId].enErrorCount = enError.errCount || 0;
    }

    return result;
  }
  static async genTrendReport(services: string[], type: string) {
    const result: any = {};
    const sourceData: any = [];
    const timeRangeList: {
      startTime: moment.Moment;
      endTime: moment.Moment;
    }[] = [];
    const isIaas = type === 'iaas';
    for (let i = 0; i < 8; i++) {
      timeRangeList.push({
        startTime: isIaas
          ? moment()
              .subtract(i + 1, 'weeks')
              .startOf('week')
              .add(4, 'days')
              .add(12, 'hours')
          : moment()
              .subtract(i, 'weeks')
              .startOf('week')
              .add(1, 'days')
              .startOf('day'),
        endTime: isIaas
          ? moment()
              .subtract(i, 'weeks')
              .startOf('week')
              .add(4, 'days')
              .add(12, 'hours')
          : moment()
              .subtract(i, 'weeks')
              .endOf('week')
              .add(1, 'days')
              .endOf('day')
      });
    }
    // 从远到近
    timeRangeList.reverse();
    for (const timeRange of timeRangeList) {
      const curTimeRangeInfo = await this.getErrorTrendData(
        services,
        timeRange.startTime.valueOf(),
        timeRange.endTime.valueOf()
      );
      const totalData = {
        totalCount: 0,
        errorCount: 0,
        errorPercent: 0,
        enErrorCount: 0,
        enErrorPercent: 0
      };
      for (const serviceId of services) {
        totalData.totalCount += curTimeRangeInfo[serviceId].totalCount;
        totalData.errorCount += Number(curTimeRangeInfo[serviceId].errorCount);
        totalData.enErrorCount += Number(
          curTimeRangeInfo[serviceId].enErrorCount
        );

        if (result[serviceId]) {
          result[serviceId].push(curTimeRangeInfo[serviceId]);
        } else {
          result[serviceId] = [curTimeRangeInfo[serviceId]];
        }
      }
      totalData.errorPercent = Number(
        totalData.errorCount / totalData.totalCount
      );
      totalData.enErrorPercent = Number(
        totalData.enErrorCount / totalData.errorCount
      );

      if (!result['total']) {
        result['total'] = [totalData];
      } else {
        result['total'].push(totalData);
      }
    }
    Object.keys(result).forEach((serviceId: string) => {
      sourceData.push({
        serviceId:
          serviceId === 'total'
            ? '所有产品'
            : allServiceList.find(item => item.serviceId === serviceId)
                ?.serviceName || serviceId,
        data: result[serviceId],
        timeRangeList: timeRangeList.map((item: any) => {
          return `${item.startTime.format('MM-DD')} ~ ${item.endTime.format(
            'MM-DD'
          )}`;
        })
      });
    });
    const mailHtml = await renderTrendHtml(sourceData, type);
    // 发送邮件,有定时任务，这个放开的时候，一定要注意
    // const mailService = Container.get(MailClient);
    // await mailService.sendMail({
    //   ...mailOption,
    //   html: mailHtml
    // });
  }
  // 上传本地趋势图文件到bos
  static async sendFileToBos(type: string) {
    const bosClientInstance = Container.get(BosClient);
    let bucketName = 'bce-cdn';
    const startDate = moment()
      .subtract(8, 'weeks')
      .startOf('week')
      .add(4, 'days')
      .format('YYYYMMDD');
    const endDate = moment()
      .subtract(0, 'weeks')
      .startOf('week')
      .add(4, 'days')
      .format('YYYYMMDD');
    const localPath = path.join(
      process.cwd(),
      `./src/template/${type}-${startDate}~${endDate}.html`
    );
    await bosClientInstance.putObjectFromFile(
      bucketName,
      `iaas-reports/${type}-${startDate}~${endDate}.html`,
      localPath
    );
  }
  // 保存一条邮件记录到数据库
  static async saveEmail(type: string, params: any = {}) {
    const scene = mailInfoMap[type] ? type : 'iaas';
    const serviceIds = mailInfoMap[scene].serviceList;
    let {sourceData, curTimeRange, lastTimeRange} =
      await BaiduMail.generateMailData(serviceIds, type);
    const mailHtml = await renderHtml(
      curTimeRange,
      lastTimeRange,
      sourceData,
      type
    );
    const startDate = moment()
      .subtract(1, 'weeks')
      .startOf('week')
      .add(4, 'days')
      .format('YYYYMMDD');
    const endDate = moment().startOf('week').add(4, 'days').format('YYYYMMDD');
    await EmailsDTO.create({
      title: `${type}~${startDate}~${endDate}周报`,
      type: type,
      service_ids: serviceIds.join(','),
      update_time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      created_time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      update_user: 'system',
      detail: mailHtml,
      reason: JSON.stringify([]),
      progress: JSON.stringify([]),
      source_data: JSON.stringify(sourceData),
      ...params
    });
  }
  // 获取所有邮件记录
  static async getEmailList(type: string) {
    try {
      const payload: any = {
        order: [['created_time', 'desc']],
        attributes: [
          'created_time',
          'id',
          'progress',
          'publish_time',
          'published',
          'reason',
          'service_ids',
          'title',
          'trend_img',
          'type',
          'update_time',
          'update_user'
        ]
      };
      type && (payload.where = {type});
      const emailData = await EmailsDTO.findAll(payload);

      if (!emailData) {
        return {
          msg: '邮件数据不存在，请重试',
          success: false
        };
      }
      return {data: emailData, success: true};
    } catch (error) {
      return Promise.reject(error);
    }
  }
  // 通过平台发送邮件
  static async sendEmailByAPP(
    id: string,
    to: string,
    type: string,
    username: string
  ) {
    try {
      const emailData: any = await EmailsDTO.findOne({
        where: {
          id
        }
      });

      if (!emailData) {
        return {
          msg: '邮件数据不存在，请重试',
          success: false
        };
      }
      // 发送邮件
      const mailService = Container.get(MailClient);
      await mailService.sendMail({
        to,
        subject: mailInfoMap['iaas'].subject,
        html: emailData.detail
      });
      // 使用的系统内置用户，意味着正式邮件，需要把邮件状态改为已发布状态
      if (type === 'auto') {
        emailData.update({
          published: true,
          update_time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
          update_user: username,
          publish_time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        });
        await emailData.save();
      }
      return {msg: '发送成功', success: true};
    } catch (error) {
      return Promise.reject(error);
    }
  }
  // 通过ID获取邮件内容
  static async getEmailDetailById(id: string) {
    try {
      const emailData: any = await EmailsDTO.findOne({
        where: {
          id
        }
      });

      if (!emailData) {
        return {
          msg: '邮件数据不存在，请重试',
          success: false
        };
      }
      return {data: emailData, success: true};
    } catch (error) {
      return Promise.reject(error);
    }
  }
  // 更新邮件内容
  static async updateEmailByAPP(id: string, data: any, username: string) {
    try {
      const emailData: any = await EmailsDTO.findOne({
        where: {
          id
        }
      });

      if (!emailData) {
        return {
          msg: '邮件数据不存在，请重试',
          success: false
        };
      }
      emailData.update({
        update_time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        update_user: username,
        ...data
      });
      await emailData.save();
      return {msg: '发送成功', success: true};
    } catch (error) {
      return {msg: '更新失败', success: false};
    }
  }
}
