import {mapping} from '../../mapping/action-log';
import {ESClient} from '../../client';
import axios from 'axios';
import {
  ACTION_INDEX,
  validateActions,
  new_db,
  chartType,
  KEYWORD_TYPE,
  DEFAULT_CLIENT
} from '../../utils';
import {
  ActionManage as ActionManageDTO,
  ChartManage as ChartManageDTO,
  ChartRelationActionManage as ChartRelationActionManageDTO
} from '../../model';
import {Op, fn, col} from 'sequelize';
import moment from 'moment';
import {Page} from '../index';

interface Query {
  page?: string | number;
  perPage?: string | number;
  apiPath?: string;
  pagePath?: string;
  reqId?: string;
  userId?: string;
  status?: number | string;
  method?: string;
  region?: string;
  apiPrefix?: string;
  pagePrefix?: string;
  id?: string;
  error?: boolean | string;
  orderBy?: string;
  order?: string;
  serviceId?: string;
  env?: string;
  startTime?: string;
  endTime?: string;
  errorMessage?: string;
  jumpTicket?: boolean;
  [prop: string]: any;
}

export class Action {
  // 根据服务，获取对应的Client类
  static getClient(esType: string) {
    if (esType) {
      return ESClient[esType];
    }
    return ESClient[DEFAULT_CLIENT];
  }
  /*
    获取action索引的mapping
  */
  static async getMapping(esType: string) {
    const esClient = this.getClient(esType);
    const mapping: any = await esClient.getMapping(ACTION_INDEX);
    const setting: any = await esClient.getSetting(ACTION_INDEX);
    return {
      mapping: mapping.body[ACTION_INDEX],
      setting
    };
  }

  static async clearIndex(esType: string) {
    const esClient = this.getClient(esType);
    await esClient.clear(ACTION_INDEX);
  }
  /*
    获取action索引
  */
  static async setMapping(esType: string) {
    await this.clearIndex(esType);
    const result: any = await axios({
      url: 'http://************:8903/' + ACTION_INDEX,
      method: 'PUT',
      data: {
        mappings: {
          ...mapping
        }
      }
    });
    return result;
  }

  /*
    行为日志条件查询
  */
  static async getData(esType: string, query: Query) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        must: [],
        filter: []
      }
    };
    let {
      page,
      perPage,
      startTime,
      endTime,
      apiPrefix,
      pagePrefix,
      apiPath,
      pagePath,
      reqId,
      userId,
      status,
      method,
      region,
      id,
      error,
      errorMessage,
      jumpTicket,
      trackId,
      orderBy,
      order,
      serviceId,
      env,
      locale
    } = query;
    page = parseInt(page as string, 10) || 1;
    perPage = parseInt(perPage as string, 10) || 10;
    status && (status = parseInt(status as string, 10));

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (locale) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.locale': locale
        }
      });
    }
    if (trackId) {
      esquery.bool.filter.push({
        term: {
          'trackId.keyword': trackId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    // 页面路径前缀查询
    if (pagePrefix) {
      esquery.bool.filter.push({
        // 分词查询
        prefix: {
          'baseInfo.href.keyword': pagePrefix
        }
      });
    }
    // 指定页面路径查询
    if (pagePath) {
      esquery.bool.filter.push({
        // 分词查询
        term: {
          'baseInfo._href.keyword': pagePath
        }
      });
    }
    // 指定用户查询
    if (userId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.userId': userId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    const result: any = await esClient.search(ACTION_INDEX, {
      size: perPage,
      from: (page - 1) * perPage,
      body: {
        query: esquery,
        track_total_hits: true,
        sort: {
          [timeField]: {
            order: order === 'asc' ? order : 'desc'
          }
        }
      }
    });
    const items: Array<any> = result.body.hits.hits.map((item: any) => ({
      id: item._id,
      ...item._source
    }));
    let total = result.body.hits.total;
    if (typeof total !== 'number' && typeof total?.value === 'number') {
      total = total.value;
    }
    return {
      total: total > 10000 ? 10000 : total,
      items
    };
  }

  /*
   * 添加行为
   */
  static async addAction(body: AddActionItem) {
    const {
      serviceId,
      env,
      trackId,
      actionType,
      name,
      url,
      description,
      creator
    } = body;
    if (!serviceId || !env || !name) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    if (trackId) {
      const action = await ActionManageDTO.findOne({
        where: {
          track_id: trackId,
          env,
          service_id: serviceId,
          deleted_at: null
        }
      });
      if (action) {
        return {
          msg: '该trackId已存在，请勿重复创建',
          success: false
        };
      }
    }

    await ActionManageDTO.create({
      service_id: serviceId,
      env,
      action_type: actionType,
      track_id: trackId,
      url,
      name,
      description,
      creator,
      created_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    return {
      msg: '添加成功',
      success: true
    };
  }

  /*
  行为日志条件查询
*/
  static async getAggData(esType: string, query: Query) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        must: [],
        filter: []
      }
    };
    let {
      startTime,
      endTime,
      serviceId,
      env,
      href,
      aggType,
      userId,
      page,
      perPage,
      order,
      orderBy,
      trackId
    } = query;

    if (!serviceId || !env) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    page = parseInt(page as string, 10) || 1;
    perPage = parseInt(perPage as string, 10) || 10;
    const selectedPv = trackId && trackId.indexOf('baidu.com') !== -1;

    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }

    if (trackId && aggType !== 'trackId') {
      const term = selectedPv
        ? {
            'baseInfo._href.keyword': trackId
          }
        : {
            'trackId.keyword': trackId
          };
      esquery.bool.filter.push({
        term
      });
    }

    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esquery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }
    if (href) {
      esquery.bool.filter.push({
        term: {
          'baseInfo._href.keyword': href
        }
      });
    }

    if (userId) {
      esType
        ? esquery.bool.filter.push({
            term: {
              'baseInfo.userId': userId
            }
          })
        : esquery.bool.filter.push({
            term: {
              'baseInfo.userId.keyword': userId
            }
          });
    }

    const params = {
      serviceId,
      env,
      pageNo: 1,
      pageSize: 10000,
      order,
      orderBy,
      keyword: '',
      keywordType: ''
    };
    const res = await this.getActionList(params);

    const actions = res.data.items || [];

    if (!actions.length) {
      return [];
    }

    const actionNameMap: any = {};
    const trackIds = actions.map(item => {
      const el = item.toJSON();
      actionNameMap[el.trackId] = el.name;
      return el.trackId;
    });

    const hrefList = actions
      .filter((i: any) => i.url)
      ?.map(item => {
        const el = item.toJSON();
        return el.url;
      });

    esquery.bool.filter.push({
      terms: {
        'trackId.keyword': trackIds
      }
    });

    if (aggType === 'time') {
      // 聚合字段
      const aggs: any = {
        group_by_time: {
          date_histogram: {
            field: timeField,
            interval: 'day',
            time_zone: 'Asia/Shanghai',
            format: 'yyyy-MM-dd'
          },
          aggs: {
            group_by_user: {
              cardinality: {
                field: esType ? 'baseInfo.userId' : 'baseInfo.userId.keyword'
              }
            }
          }
        }
      };
      const result: any = await esClient.search(ACTION_INDEX, {
        body: {
          query: esquery,
          track_total_hits: true,
          aggs
        }
      });
      const pvRes =
        hrefList.length && !selectedPv
          ? await Page.getTrendDailyData(esType, {
              ...query,
              href: hrefList
            })
          : [];
      const items: Array<any> =
        result.body.aggregations.group_by_time.buckets.map((item: any) => {
          const time = item.key_as_string;
          const pvData = pvRes.find(i => i.time === time);
          const pvCount = pvData?.pvCount || 0;
          const uvCount = pvData?.uvCount || 0;
          return {
            time,
            timeKey: item.key,
            actionCount: item.doc_count + pvCount,
            actionCountByUser: item.group_by_user.value + uvCount
          };
        });
      return items;
    } else if (aggType === 'trackId') {
      const aggs: any = {
        trackId: {
          terms: {
            field: 'trackId.keyword',
            size: trackIds.length,
            include: trackIds,
            min_doc_count: 0
          }
        }
      };
      const result: any = await esClient.search(ACTION_INDEX, {
        body: {
          query: esquery,
          track_total_hits: true,
          aggs
        }
      });
      const pvRes =
        hrefList.length && !selectedPv
          ? await Page.getChildPageTrendData(esType, {
              ...query,
              href: hrefList
            })
          : {items: []};
      let items: Array<any> = result?.body?.aggregations?.trackId?.buckets?.map(
        (item: any) => {
          return {
            ...item,
            trackId: item.key,
            trackName: actionNameMap[item.key] || item.key,
            count: item.doc_count
          };
        }
      );
      items = [
        ...items,
        ...pvRes.items.map((i: any) => {
          return {
            count: i.pvCount,
            doc_count: i.pvCount,
            key: i.href,
            trackId: '',
            url: i.href,
            trackName: i.name
          };
        })
      ];
      return items;
    } else if (aggType === 'userId') {
      const sortBy = ({count: 'count'} as any)[orderBy || 'count'];

      const totalRes = await esClient.search(ACTION_INDEX, {
        body: {
          query: esquery,
          aggs: {
            total: {
              cardinality: {
                field: esType ? 'baseInfo.userId' : 'baseInfo.userId.keyword'
              }
            }
          }
        }
      });
      const total = totalRes?.body?.aggregations?.total?.value;
      if (!total) {
        return {
          data: [],
          total: 0
        };
      }

      const aggs: any = {
        group_by: {
          terms: {
            field: esType ? 'baseInfo.userId' : 'baseInfo.userId.keyword',
            size: total
          },
          aggs: {
            count: {
              value_count: {
                field: esType ? 'baseInfo.userId' : 'baseInfo.userId.keyword'
              }
            },
            sort: {
              bucket_sort: {
                sort: [
                  {
                    [sortBy]: order || 'desc'
                  }
                ],
                from: perPage * (page - 1),
                size: perPage
              }
            }
          }
        }
      };
      const result: any = await esClient.search(ACTION_INDEX, {
        body: {
          query: esquery,
          track_total_hits: true,
          aggs
        }
      });
      const pvRes =
        hrefList.length && !selectedPv
          ? await Page.getUserTrendDailyData(esType, {
              ...query,
              href: hrefList
            })
          : {items: []};
      const items = result?.body?.aggregations?.group_by?.buckets.map(
        (item: any) => {
          const pvData = pvRes.items.find(i => i.userId === item.key);
          const pvCount = pvData?.pvCount || 0;
          return {
            ...item,
            doc_count: item.doc_count + pvCount
          };
        }
      );
      return {
        total,
        items
      };
    }

    return [];
  }

  /*
   * 编辑行为
   */
  static async editAction(body: EditActionItem) {
    const {id, name, description, updater} = body;
    if (!id || !name || !updater) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    const action = await ActionManageDTO.findOne({
      where: {
        id: id,
        deleted_at: null
      }
    });
    if (!action) {
      return {
        msg: '该行为不存在，请重试',
        success: false
      };
    }

    action.update({
      name,
      updater,
      description,
      updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    await action.save();

    return {
      msg: '更新成功',
      success: true
    };
  }

  /*
   * 删除行为
   */
  static async deleteAction(body: DeleteActionItem) {
    const {id} = body;
    if (!id) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    const action = await ActionManageDTO.findOne({
      where: {
        id: id,
        deleted_at: null
      }
    });
    if (!action) {
      return {
        msg: '该行为不存在，请重试',
        success: false
      };
    }

    const chart = await ChartRelationActionManageDTO.findOne({
      where: {
        [Op.and]: [{chart_id: {[Op.not]: null}}, {action_id: id}]
      }
    });

    if (chart) {
      return {
        msg: '该行为项正在使用中，请先删除图表',
        success: false
      };
    }

    // action.update({
    //   deleted_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    // });

    await action.destroy();

    return {
      msg: '删除成功',
      success: true
    };
  }

  /*
   * 获取行为列表
   */
  static async getActionList(query: GetActionListItem) {
    const {
      serviceId,
      env,
      actionType,
      pageNo,
      pageSize,
      order,
      orderBy,
      keyword,
      keywordType
    } = query;

    const _pageNo = parseInt(pageNo as any, 10) || 1;
    const _pageSize = parseInt(pageSize as any, 10) || 10;
    let _orderBy = orderBy;
    let _order = order;

    switch (_orderBy) {
      case 'createdAt':
        _orderBy = 'created_at';
        break;
      case 'updatedAt':
        _orderBy = 'updated_at';
        break;
      default:
        _orderBy = 'created_at';
    }

    switch (_order) {
      case 'desc':
        _order = 'desc';
        break;
      case 'asc':
        _order = 'asc';
        break;
      default:
        _order = 'desc';
    }

    let andWhere: any = [
      {
        deleted_at: null
      }
    ];

    if (serviceId) {
      andWhere.push({
        service_id: serviceId
      });
    }

    if (env) {
      andWhere.push({
        env: env
      });
    }

    if (keywordType && ['track_id', ...KEYWORD_TYPE].includes(keywordType)) {
      andWhere.push({
        [keywordType]: {
          [Op.like]: `%${keyword}%`
        }
      });
    }

    actionType &&
      andWhere.push({
        ['action_type']: {
          [Op.or]: actionType === 'pv' ? ['pv'] : [null, 'event']
        }
      });

    const {count, rows} = await ActionManageDTO.findAndCountAll({
      where: {
        [Op.and]: andWhere
      },
      offset: (_pageNo - 1) * _pageSize,
      limit: _pageSize,
      order: [[_orderBy, _order]],
      attributes: [
        'id',
        ['track_id', 'trackId'],
        'url',
        ['action_type', 'actionType'],
        'name',
        'creator',
        'updater',
        'description',
        ['created_at', 'createdAt'],
        ['updated_at', 'updatedAt']
      ]
    });

    return {
      data: {
        total: count,
        items: rows
      },
      msg: '获取列表成功',
      success: true
    };
  }

  /*
   * 获取trackId列表
   */
  static async geTrackIDList(esType: string, query: GetListItem) {
    const esClient = this.getClient(esType);
    const esquery: any = {
      bool: {
        filter: []
      }
    };
    const {serviceId, env, keyword} = query;
    if (serviceId) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    if (env) {
      esquery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (keyword) {
      esquery.bool.filter.push({
        wildcard: {
          'track-id': `*${keyword}*`
        }
      });
    }

    const totalRes: any = await esClient.search(ACTION_INDEX, {
      size: 0,
      body: {
        query: esquery,
        aggs: {
          total: {
            cardinality: {
              field: 'trackId.keyword'
            }
          }
        }
      }
    });

    const total = totalRes?.body?.aggregations?.total?.value;
    if (!total) {
      return {
        items: [],
        total: 0
      };
    }

    const result: any = await esClient.search(ACTION_INDEX, {
      size: 0,
      body: {
        query: esquery,
        track_total_hits: true,
        aggs: {
          trackList: {
            terms: {
              field: 'trackId.keyword',
              size: total
            }
          }
        }
      }
    });

    const resultList = result.body.aggregations.trackList.buckets || [];
    const items: Array<any> = resultList.map((item: any) => {
      return {
        trackId: item.key
      };
    });

    return {
      total,
      items
    };
  }

  /*
   * 添加图表
   */
  static async addChart(body: AddChartItem) {
    const {serviceId, env, type, name, description, creator, actions} = body;
    if (!serviceId || !env || !name || !type || !actions) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }
    if (!chartType.includes(type)) {
      return {
        msg: '图表类型错误',
        success: false
      };
    }

    if (!validateActions(type, actions)) {
      return {
        msg: '行为项错误',
        success: false
      };
    }

    const chart = await ChartManageDTO.create({
      service_id: serviceId,
      env,
      type,
      name,
      description,
      creator,
      created_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    const chartId = chart.toJSON().id;
    const arr = actions.map((item, index) => {
      return {
        order: index + 1,
        color: item.color,
        action_id: item.actionId,
        chart_id: chartId
      };
    });
    await ChartRelationActionManageDTO.bulkCreate(arr);

    return {
      msg: '添加成功',
      success: true
    };
  }

  /*
   * 获取图表列表
   */
  static async getChartList(query: GetChartListQuery) {
    const {
      serviceId,
      env,
      pageNo,
      pageSize,
      order,
      orderBy,
      keyword,
      keywordType
    } = query;

    const _pageNo = parseInt(pageNo as any, 10) || 1;
    const _pageSize = parseInt(pageSize as any, 10) || 10;
    let _orderBy = orderBy;
    let _order = order;

    switch (_orderBy) {
      case 'createdAt':
        _orderBy = 'created_at';
        break;
      case 'updatedAt':
        _orderBy = 'updated_at';
        break;
      default:
        _orderBy = 'created_at';
    }

    switch (_order) {
      case 'desc':
        _order = 'desc';
        break;
      case 'asc':
        _order = 'asc';
        break;
      default:
        _order = 'desc';
    }
    let andWhere: any = [
      {
        deleted_at: null
      }
    ];

    if (serviceId) {
      andWhere.push({
        service_id: serviceId
      });
    }

    if (env) {
      andWhere.push({
        env: env
      });
    }

    if (keywordType && KEYWORD_TYPE.includes(keywordType)) {
      andWhere.push({
        [keywordType]: {
          [Op.like]: `%${keyword}%`
        }
      });
    }
    const {rows, count} = await ChartManageDTO.findAndCountAll({
      where: {
        [Op.and]: andWhere
      },
      offset: (_pageNo - 1) * _pageSize,
      limit: _pageSize,
      order: [[_orderBy, _order]],
      attributes: {
        exclude: ['env', 'service_id', 'deleted_at', 'actions']
      }
    });

    return {
      data: {
        total: count,
        items: rows
      },
      msg: '获取列表成功',
      success: true
    };
  }

  /*
   * 删除图表
   */
  static async deleteChart(body: DeleteChartItem) {
    const {id} = body;
    if (!id) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    const action = await ChartManageDTO.findOne({
      where: {
        id: id,
        deleted_at: null
      }
    });
    if (!action) {
      return {
        msg: '该图表不存在，请重试',
        success: false
      };
    }

    action.update({
      deleted_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    await action.save();

    return {
      msg: '删除成功',
      success: true
    };
  }

  /*
   * 图表详情
   */
  static async detailChart(query: detailChartQuery) {
    const {id} = query;
    if (!id) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    const chart = await ChartManageDTO.findOne({
      where: {
        id: id,
        deleted_at: null
      },
      attributes: {
        exclude: ['env', 'service_id', 'deleted_at']
      },
      include: [
        {
          model: ChartRelationActionManageDTO,
          as: 'actions',
          attributes: ['color'],
          include: [
            {
              model: ActionManageDTO,
              attributes: [
                'name',
                'description',
                ['id', 'actionId'],
                ['track_id', 'trackId']
              ]
            }
          ]
        }
      ],
      order: [['actions', 'order', 'asc']]
    });
    if (!chart) {
      return {
        msg: '该图表不存在，请重试',
        success: false
      };
    }
    const actions = chart.toJSON()?.actions?.map((item: any) => {
      let _item = {
        ...item,
        ...item.action_manage
      };
      delete _item.action_manage;
      return _item;
    });

    const data = {
      ...chart.toJSON(),
      actions
    };

    return {
      data,
      msg: '获取详情成功',
      success: true
    };
  }

  /*
   * 编辑图表
   */
  static async editChart(body: EditChartItem) {
    const {id, type, name, description, actions, updater} = body;
    if (!id || !name || !updater || !type || !actions) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }
    if (!chartType.includes(type)) {
      return {
        msg: '图表类型错误',
        success: false
      };
    }

    if (!validateActions(type, actions)) {
      return {
        msg: '行为项错误',
        success: false
      };
    }

    try {
      // 事务处理
      await new_db.transaction(async t => {
        const chart: any = await ChartManageDTO.findOne({
          where: {
            id,
            deleted_at: null
          }
        });

        if (chart) {
          // 更新chart基本信息
          chart.update({
            name,
            description,
            type,
            updater,
            updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
          });
          // chart.setActions([]); // 删除之前关联的actions
          await chart.save();
          // 删除之前的绑定关系
          await ChartRelationActionManageDTO.destroy({
            where: {
              chart_id: chart.id
            }
          });
          const arr = actions.map((item, index) => {
            return {
              order: index + 1,
              color: item.color,
              action_id: item.actionId,
              chart_id: id
            };
          });
          // 批量创建新的actions数据
          await ChartRelationActionManageDTO.bulkCreate(arr, {transaction: t});
        }
      });

      return {
        msg: '修改成功',
        success: true
      };
    } catch (error) {
      return {
        msg: `修改失败, ${error}`,
        success: false
      };
    }
  }

  /*
   * 预览图表，获取图表行为具体信息
   */
  static async previewChart(esType: string, body: previewChartBody) {
    const esClient = this.getClient(esType);
    const {serviceId, env, id, type, startTime, endTime, actions} = body;
    if (
      !(id || (type && actions)) ||
      !serviceId ||
      !env ||
      !startTime ||
      !endTime
    ) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    let actionIds = [];
    let _type = '';
    let data = {};
    let _actions: any = [];
    if (id) {
      const chart = await ChartManageDTO.findOne({
        where: {
          id: id,
          deleted_at: null
        },
        include: [
          {
            model: ChartRelationActionManageDTO,
            as: 'actions',
            attributes: ['action_id', 'color', 'order'],
            include: [
              {
                model: ActionManageDTO,
                attributes: ['id', 'name', 'track_id', 'description', 'url']
              }
            ]
          }
        ],
        order: [['actions', 'order', 'asc']]
      });

      if (!chart) {
        return {
          msg: '该图表不存在，请重试',
          success: false
        };
      }
      _type = chart.toJSON().type;

      chart.toJSON().actions.forEach((item: any) => {
        actionIds.push(item.action_id);
        _actions.push({
          color: item.color,
          order: item.order,
          ...item.action_manage
        });
      });
    } else if (type && actions) {
      if (!validateActions(type, actions)) {
        return {
          msg: '行为项错误',
          success: false
        };
      }

      _type = type;
      actionIds = actions.map((item: any) => item.actionId);
      const res = await ActionManageDTO.findAll({
        where: {
          id: {
            [Op.in]: actionIds
          }
        },
        attributes: ['id', 'name', 'track_id', 'description']
      });
      _actions = res.map(item => {
        const _item = actions.find(
          (action: any) => action.actionId == item.toJSON().id
        );
        return {
          ...item.toJSON(),
          color: _item?.color
        };
      });
    }

    if (!chartType.includes(_type)) {
      return {
        msg: '图表类型错误',
        success: false
      };
    }

    const esQuery: any = {
      bool: {
        filter: []
      }
    };

    const columns = [
      {label: '日期', name: 'date'},
      {label: '总数', name: 'total'}
    ];

    const actionMap: any = {};
    _actions.forEach((item: any) => {
      columns.push({
        label: item.name,
        name: (item.track_id || item.url).replace(/:/g, '')
      });
      actionMap[item.track_id || item.url] = {
        name: item.name,
        description: item.description,
        color: item.color,
        order: item.order
      };
    });

    const urlActions = _actions.filter((i: any) => i.url);
    const href = urlActions.map((i: any) => i.url);
    const pvQuery = {
      env,
      serviceId,
      startTime,
      endTime,
      href
    };
    let urlData: any = [];
    const hrefMap: any = {};
    if (href?.length) {
      const PromiseList: any = [];
      href.forEach((item: any) => {
        PromiseList.push(
          Page.getTrendDailyData(esType, {...pvQuery, href: [item]})
        );
      });
      let res2 = await Promise.all(PromiseList);
      res2 = res2.map((i: any) =>
        i.map((j: any) => ({count: j.pvCount, date: j.time}))
      );
      const [res1] = await Promise.all([
        Page.getChildPageTrendData(esType, pvQuery)
      ]);
      href.forEach((i: string, index: number) => {
        hrefMap[i] = res2[index];
      });
      const urlMap: any = {};
      res1.items.forEach(i => {
        urlMap[i.href] = i.pvCount;
      });
      urlData = urlActions.map((i: any) => ({
        ...i,
        total: urlMap[i.url],
        data: hrefMap[i.url],
        key: i.url,
        doc_count: urlMap[i.url],
        url: i.url
      }));
    }

    _actions = _actions.filter((i: any) => i.track_id);

    if (env) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.env.keyword': env
        }
      });
    }
    if (serviceId) {
      esQuery.bool.filter.push({
        term: {
          'baseInfo.serviceId': serviceId
        }
      });
    }
    const timeField = esType ? 'timeStamp' : '@timestamp';
    if (startTime) {
      esQuery.bool.filter.push({
        range: {
          [timeField]: {
            gte: startTime,
            lte: endTime
          }
        }
      });
    }

    if (_type === 'table') {
      const daily_aggs: any = {
        group_by_time: {
          date_histogram: {
            field: timeField,
            calendar_interval: 'day',
            time_zone: 'Asia/Shanghai',
            format: 'yyyy-MM-dd',
            min_doc_count: 0
          },
          aggs: {
            actions: {
              terms: {
                field: 'trackId.keyword',
                size: _actions.length,
                include: _actions.map((item: any) => item.track_id),
                min_doc_count: 0
              }
            }
          }
        }
      };

      if (startTime) {
        daily_aggs.group_by_time.date_histogram.extended_bounds = {
          min: startTime,
          max: endTime
        };
      }

      const result: any = await esClient.search(ACTION_INDEX, {
        size: 0,
        body: {
          query: esQuery,
          track_total_hits: true,
          aggs: daily_aggs
        }
      });
      const total = result.body.aggregations.group_by_time.buckets.length;
      const rows = result.body.aggregations.group_by_time.buckets.map(
        (item: any) => {
          const _item: any = {
            date: item.key_as_string
          };
          item.actions.buckets.map((item: any) => {
            _item[item.key] = item.doc_count;
          });
          return _item;
        }
      );
      for (let href in hrefMap) {
        const _item = hrefMap[href];
        rows.forEach((i: any) => {
          const matchItem = _item.find((data: any) => data.date === i.date);
          if (matchItem) {
            i[href.replace(/:/g, '')] = matchItem.count;
          }
        });
      }
      rows.forEach((i: any) => {
        let total = 0;
        for (let key in i) {
          if (key && typeof i[key] === 'number') {
            total += i[key];
          }
        }
        i.total = total;
      });
      data = {
        columns,
        total,
        rows
      };
    } else {
      const daily_aggs: any = {
        trackId: {
          terms: {
            field: 'trackId.keyword',
            size: _actions.length,
            include: _actions.map((item: any) => item.track_id),
            min_doc_count: 0
          },
          aggs: {
            group_by_time: {
              date_histogram: {
                field: timeField,
                calendar_interval: 'day',
                time_zone: 'Asia/Shanghai',
                format: 'yyyy-MM-dd',
                min_doc_count: 0
              }
            }
          }
        }
      };

      if (startTime) {
        daily_aggs.trackId.aggs.group_by_time.date_histogram.extended_bounds = {
          min: startTime,
          max: endTime
        };
      }

      const result: any = await esClient.search(ACTION_INDEX, {
        size: 0,
        body: {
          query: esQuery,
          track_total_hits: true,
          aggs: daily_aggs
        }
      });
      const items: any[] = [];
      let buckets = result.body.aggregations.trackId.buckets.sort(
        (a: any, b: any) => {
          return a.order - b.order;
        }
      );
      buckets = [...urlData, ...buckets].sort(
        (a: any, b: any) => b.total - a.total
      );
      // 处理数据格式
      for (let i = 0; i < buckets.length; i++) {
        const item = buckets[i];
        const _item: any = {
          name: actionMap[item.key].name,
          description: actionMap[item.key].description,
          color: actionMap[item.key].color,
          order: actionMap[item.key].order,
          total: item.doc_count
        };

        if (i >= 1) {
          _item.rate = Number(buckets[i - 1].doc_count)
            ? (
                Number(item.doc_count) / Number(buckets[i - 1].doc_count)
              ).toFixed(6)
            : '-';
        } else {
          // 第一个数据 算 总转化率
          if (buckets.length > 1) {
            _item.rate = Number(item.doc_count)
              ? (
                  Number(buckets[buckets.length - 1].doc_count) /
                  Number(item.doc_count)
                ).toFixed(6)
              : '-';
          }
        }
        // 计算每天的转化率
        let data: any = [];
        const timeBuckets = item.group_by_time?.buckets || [];
        for (let j = 0; j < timeBuckets.length; j++) {
          const timeItem = timeBuckets[j];
          const _timeItem: any = {
            count: timeItem.doc_count,
            date: timeItem.key_as_string
          };

          if (i >= 1) {
            const prevTimeBuckets = buckets[i - 1].group_by_time?.buckets;
            const prevTimeItem = prevTimeBuckets[j];
            _timeItem.rate = Number(prevTimeItem.doc_count)
              ? (
                  Number(timeItem.doc_count) / Number(prevTimeItem.doc_count)
                ).toFixed(6)
              : '-';
          } else {
            // 第一个数据 算 总转化率
            const lastTimeBuckets =
              buckets[buckets.length - 1].group_by_time?.buckets;
            const lastTimeItem = lastTimeBuckets[j];
            _timeItem.rate = Number(timeItem.doc_count)
              ? (
                  Number(lastTimeItem.doc_count) / Number(timeItem.doc_count)
                ).toFixed(6)
              : '-';
          }
          data.push(_timeItem);
        }
        if (hrefMap[item.url]) {
          data = [...data, ...hrefMap[item.url]];
        }
        _item.data = data;

        items.push(_item);
      }
      data = {
        items
      };
    }

    return {
      data,
      msg: '获取成功',
      success: true
    };
  }

  /*
   * 预览图表列表，获取图表行为具体信息
   */
  static async previewListChart(esType: string, query: previewListChartQuery) {
    const {
      serviceId,
      env,
      pageNo,
      pageSize,
      order,
      orderBy,
      keyword,
      keywordType,
      startTime,
      endTime
    } = query;

    if (!serviceId || !env || !startTime || !endTime) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    const _pageNo = parseInt(pageNo as any, 10) || 1;
    const _pageSize = parseInt(pageSize as any, 10) || 10;
    let _orderBy = orderBy;
    let _order = order;

    switch (_orderBy) {
      case 'createdAt':
        _orderBy = 'created_at';
        break;
      case 'updatedAt':
        _orderBy = 'updated_at';
        break;
      default:
        _orderBy = 'created_at';
    }

    switch (_order) {
      case 'desc':
        _order = 'desc';
        break;
      case 'asc':
        _order = 'asc';
        break;
      default:
        _order = 'desc';
    }

    let andWhere: any = [
      {
        deleted_at: null
      }
    ];

    if (serviceId) {
      andWhere.push({
        service_id: serviceId
      });
    }

    if (env) {
      andWhere.push({
        env: env
      });
    }

    if (keywordType && KEYWORD_TYPE.includes(keywordType)) {
      andWhere.push({
        [keywordType]: {
          [Op.like]: `%${keyword}%`
        }
      });
    }

    const {rows, count} = await ChartManageDTO.findAndCountAll({
      where: {
        [Op.and]: andWhere
      },
      offset: (_pageNo - 1) * _pageSize,
      limit: _pageSize,
      order: [[_orderBy, _order]],
      attributes: {
        exclude: ['env', 'service_id', 'deleted_at', 'actions']
      }
    });

    const items = rows.map((item: any) => {
      return {
        ...item.toJSON(),
        data: this.previewChart(esType, {
          serviceId,
          env,
          id: item.toJSON().id,
          startTime,
          endTime
        })
      };
    });

    const results = await Promise.all(items.map(item => item.data));

    items.forEach((item, index) => {
      item.data = results[index].data;
    });

    return {
      data: {items, total: count},
      msg: '获取成功',
      success: true
    };
  }
}
