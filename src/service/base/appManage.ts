/**
 * 应用管理操作
 */
import {DEFAULT_CLIENT} from '../../utils';
import {
  AppManage as AppManageDTO,
  AppTag as AppTagDTO,
  AppTagRef as AppTagRefDTO
} from '../../model';
import {Op} from 'sequelize';
import moment from 'moment';

export class AppManage {
  /*
   * 添加应用
   */
  static async addApp(body: AddAppItem) {
    const {
      serviceId,
      env,
      serviceName,
      description,
      tags,
      creator = DEFAULT_CLIENT
    } = body;
    if (!serviceId) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    // 查询数据库是否存在相同serviceId
    const action = await AppManageDTO.findOne({
      where: {
        service_id: serviceId
      }
    });
    if (action) {
      return {
        msg: '该serviceId已存在，请勿重复创建',
        success: false
      };
    }

    // 添加tag
    let tagInfo: Array<{tagId: string; tagName: string; isCreated: boolean}> =
      [];

    // 添加tag
    if (tags && tags.length) {
      const res = await this.addAppTag({tags, env, creator});
      tagInfo = res.data?.tagInfo as Array<{
        tagId: string;
        tagName: string;
        isCreated: boolean;
      }>;
    }

    await AppManageDTO.create({
      service_id: serviceId,
      env,
      service_name: serviceName,
      description,
      tags: (tags || []).join(','),
      creator,
      created_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    });

    // 插入映射关系
    tagInfo.length &&
      (await this.addAppTagRef({
        env,
        serviceId,
        tags: tagInfo
      }));

    return {
      msg: '添加成功',
      success: true
    };
  }

  /**
   * 编辑应用
   */
  static async editApp(body: AddAppItem) {
    const {
      serviceId,
      env,
      serviceName,
      description,
      tags,
      updater = DEFAULT_CLIENT
    } = body;
    if (!serviceId) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    const action = await AppManageDTO.findOne({
      where: {
        service_id: serviceId
      }
    });

    if (!action) {
      await this.addApp({
        env,
        serviceId,
        serviceName,
        tags,
        description,
        creator: updater
      });
    } else {
      await AppManageDTO.update(
        {
          service_name: serviceName,
          description,
          tags: (tags || []).join(','),
          updater,
          updated_at: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
        },
        {
          where: {
            service_id: serviceId
          }
        }
      );
      await action.save();

      // 添加tag
      if (tags && tags.length) {
        const res = await this.addAppTag({tags, env, creator: updater});
        const tagInfo = res.data?.tagInfo as Array<{
          tagId: string;
          tagName: string;
          isCreated: boolean;
        }>;
        // 更新tag映射
        this.updateAppTagRef({
          env,
          tags: tagInfo,
          serviceId
        });
      }
    }

    return {
      msg: '更新成功',
      success: true
    };
  }

  /**
   * 获取应用列表
   */
  static async queryAppList(query: GetAppListItem) {
    const {serviceName, pageNo, pageSize, tags} = query;
    let _pageNo = parseInt(pageNo as any, 10) || 1;
    let _pageSize = parseInt(pageSize as any, 10) || 10;

    if (!pageNo && !pageSize) {
      _pageSize = 10000;
    }

    // 需根据tags过滤
    let andWhere: any = [];
    if (tags && tags.length) {
      const tagsOp = [] as any[];
      tags.forEach(tag => {
        tagsOp.push(tag);
      });
      andWhere.push({
        tags: {[Op.in]: tagsOp}
      });
    }
    // 根据搜索内容过滤
    if (serviceName) {
      andWhere.push({
        [Op.or]: [
          {
            service_name: {
              [Op.like]: `%${serviceName}%`
            }
          },
          {
            service_id: {
              [Op.like]: `%${serviceName}%`
            }
          }
        ]
      });
    }

    let offset = (_pageNo - 1) * _pageSize;
    let limit = _pageSize;

    // 查询
    const {rows, count} = await AppManageDTO.findAndCountAll({
      where: {
        [Op.and]: andWhere
      },
      offset: (_pageNo - 1) * _pageSize,
      limit: _pageSize,
      attributes: [
        'id',
        ['service_name', 'serviceName'],
        ['service_id', 'serviceId'],
        'tags',
        'description',
        'creator',
        ['created_at', 'createdAt']
      ]
    });
    const items = rows.map(item => ({
      ...item.dataValues,
      tags: ((item.get('tags') as string) || '').split(',')
    }));

    return {
      data: {
        total: count,
        items
      },
      msg: '获取列表成功',
      success: true
    };
  }

  /**
   * 删除应用
   */
  static async deleteApp(body: {
    serviceId: string;
    env?: string;
    creator?: string;
  }) {
    const {serviceId, env, creator} = body;
    if (!serviceId) {
      return {
        msg: '缺少serviceId，请检查',
        success: false
      };
    }

    const AppActions = await AppManageDTO.findAll({
      where: {
        service_id: serviceId
        // TODO: 后续有权限管理后增加限制
        // env,
        // creator
      }
    });

    if (!AppActions || !AppActions.length) {
      return {
        msg: '当前serviceId不存在',
        success: false
      };
    }

    try {
      // 删除应用
      await AppManageDTO.destroy({
        where: {
          service_id: serviceId
          // TODO: 后续有权限管理后增加限制
          // env,
          // creator
        }
      });

      // 删除app-tag映射
      await AppTagRefDTO.destroy({
        where: {
          [Op.and]: [
            {service_id: serviceId}
            // {env},
            // {creator}
          ]
        }
      });

      return {
        msg: '删除成功',
        success: true
      };
    } catch (err) {
      return {
        msg: `删除失败: ${err}`,
        success: false
      };
    }
  }

  /**
   * 获取tag列表
   */
  static async queryAppTagList() {
    const {rows, count} = await AppTagDTO.findAndCountAll({
      attributes: [
        ['tag_name', 'tagName'],
        ['tag_id', 'tagId']
      ]
    });

    return {
      data: {
        total: count,
        items: rows
      },
      msg: '获取列表成功',
      success: true
    };
  }

  /**
   * 添加标签
   */
  static async addAppTag(body: {env: string; tags: string[]; creator: string}) {
    const {env, tags, creator} = body;
    if (!tags || !tags.length) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    if (tags.some(tag => !tag.trim())) {
      return {
        msg: '标签值不能为空字符串',
        success: false
      };
    }

    // 添加tag
    const tagInfo: Array<{tagId: string; tagName: string; isCreated: boolean}> =
      [];
    tags &&
      tags.length &&
      (await Promise.all(
        tags.map(async (tag: string) => {
          const [tagRes, created] = await AppTagDTO.findOrCreate({
            where: {tag_name: tag},
            defaults: {
              env,
              tag_name: tag,
              creator: creator ?? DEFAULT_CLIENT
            }
          });
          tagInfo.push({
            tagId: String(tagRes.get('tag_id')),
            tagName: String(tagRes.get('tag_name') ?? ''),
            isCreated: created
          });
          return Promise.resolve();
        })
      ));
    return {
      msg: '添加成功',
      data: {tagInfo},
      success: true
    };
  }

  /**
   * 删除标签
   */
  static async deleteAppTag(body: {
    env: string;
    tag: {tagName: string; tagId: string};
  }) {
    const {env, tag} = body;
    if (!tag || !tag.tagId) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }

    // TODO：本期需要处理脏数据，暂不增加该校验
    // if (tags.some(tag => !tag.trim())) {
    //   return {
    //     msg: '标签值不能为空字符串',
    //     success: false
    //   };
    // }

    try {
      const allTagAction = await AppTagDTO.findAll({
        where: {
          [Op.and]: [
            {
              tag_id: tag.tagId
            },
            {tag_name: tag.tagName}
          ]
        }
      });

      // TODO：本期需要处理脏数据，暂不增加该校验
      // if (!allTagAction || !allTagAction.length) {
      //   return {
      //     msg: '不存在该标签',
      //     success: false
      //   };
      // }

      // 删除标签
      await AppTagDTO.destroy({
        where: {
          // TODO：本期需要处理脏数据，暂不增加该校验
          // [Op.and]: [{tag_id: tag.tagId}, {tag_name: tag.tagName}, {env}]
          [Op.and]: [{tag_id: tag.tagId}, {tag_name: tag.tagName}]
        }
      });

      // tag对应的app映射
      const AppTagRefActions = await AppTagRefDTO.findAll({
        where: {
          // [Op.and]: [{tag_id: tag.tagId}, {tag_name: tag.tagName}, {env}]
          [Op.and]: [{tag_id: tag.tagId}, {tag_name: tag.tagName}]
        }
      });

      const appTagInfo = AppTagRefActions.map(item => ({
        serviceId: item.get('service_id'),
        tagId: item.get('tag_id') as string,
        tagName: item.get('tag_name') as string
      }));

      // 删除映射
      await AppTagRefDTO.destroy({
        where: {
          // [Op.and]: [{tag_id: tag.tagId}, {tag_name: tag.tagName}, {env}]
          [Op.and]: [{tag_id: tag.tagId}, {tag_name: tag.tagName}]
        }
      });

      // serviceId
      const AppActions = await AppManageDTO.findAll({
        where: {
          [Op.and]: [
            {
              service_id: {
                [Op.in]: appTagInfo.map(info => info.serviceId)
              }
            }
            // {env}
          ]
        }
      });

      const appInfos = AppActions.map(item => ({...item.get()})).map(
        (item: {tags: string}) => {
          const tags = item.tags
            .split(',')
            .filter(tagName => tagName !== tag.tagName);
          return {...item, tags: tags && tags.length ? tags.join(',') : null};
        }
      );

      // 更新APP数据
      await AppManageDTO.bulkCreate(appInfos, {
        updateOnDuplicate: ['tags']
      });

      return {
        msg: '删除成功',
        success: true
      };
    } catch (err) {
      return {
        msg: `删除失败${err}`,
        success: false
      };
    }
  }

  /**
   * 插入serviceid - tag 映射
   */
  static async addAppTagRef(body: {
    env: string;
    tags: Array<{tagId: string; tagName: string}>;
    serviceId: string;
  }) {
    const {env, tags, serviceId} = body;
    if (!tags || !tags.length || !serviceId) {
      return {
        msg: '缺少参数，请检查',
        success: false
      };
    }
    const appTagRefArr = tags.map(tag => {
      return {
        env,
        tag_id: tag.tagId,
        tag_name: tag.tagName,
        service_id: serviceId
      };
    });
    await AppTagRefDTO.bulkCreate(appTagRefArr);
    return {
      msg: '更新成功',
      success: true
    };
  }

  /**
   * 更新serviceid对应的tag映射
   */
  static async updateAppTagRef(body: {
    env: string;
    tags: Array<{tagId: string; tagName: string; isCreated: boolean}>;
    serviceId: string;
  }) {
    const {env, tags, serviceId} = body;
    if (!tags || !tags.length || !serviceId) {
      return {
        msg: '缺少tags参数，请检查',
        success: false
      };
    }

    // 查找tagName对应的id[Op.in]: [1, 2],
    const allTagAction = await AppTagDTO.findAll({
      where: {
        tag_name: {
          [Op.in]: tags.map(item => item.tagName)
        }
      }
    });

    // 查找并删除serviceId对应的tag映射
    await AppTagRefDTO.destroy({
      where: {
        service_id: serviceId
      }
    });

    this.addAppTagRef({
      env,
      serviceId,
      tags: allTagAction.map(item => ({
        tagId: item.get('tag_id') as string,
        tagName: item.get('tag_name') as string
      }))
    });

    return {
      msg: '更新成功',
      success: true
    };
  }
}
