/**
 * 权限管理
 */
import {DEFAULT_CLIENT} from '../../utils';
import axios from 'axios';
import Container from 'typedi';
import {Op} from 'sequelize';
import {md5} from 'js-md5';

const AK = 'uuapclient-1023292817574744065-7tCBe-online';
const SK = '97c81e70652c4c60b800d3';

type AuthNormalValue = string | null;

interface AuthRoleRawProps {
  // 角色编码
  code: string;
  // 角色名称
  name: string;
  // 角色介绍
  description: string;
  // 父角色编码
  pcode: AuthNormalValue;
  inheritable: number;
  authorizable: number;
  // 角色英文名称
  englishName: AuthNormalValue;
  adminCode: AuthNormalValue;
}

/**
 * 资源树属性
 */
interface AuthResourceTreeRawProps {
  // 资源树code
  code: string;
  // 资源树名称
  name: string;
  // 资源树授权策略
  strategy: string;
  // 资源树类型
  metaType: string;
  type: null;
  pointList: any[];
}

/**
 * 资源节点属性
 */
interface AuthResourceMetaRawProps {
  // 资源code
  code: string;
  // 上级资源节点code
  pcode: AuthNormalValue;
  // 资源名称
  name: string;
  // 资源英文名称
  englishName: string;
  // 不知道是啥
  remark: string;
  // 资源类型
  nodeType: string;
  // 是否启用
  status: boolean;
  // 当前层级节点顺序
  seq: number;
  extension: Record<string, string>;
}

interface AuthResourceMetaProps extends AuthResourceMetaRawProps {
  children: Array<AuthResourceMetaProps> | null;
}
/**
 * 资源节点返回数据结构
 */
interface AuthResourceRawProps {
  // 资源树信息
  resourceMeta: AuthResourceTreeRawProps;
  // 资源节点信息
  resourceTreeModel: Array<AuthResourceMetaProps>;
}

/**
 * 查询角色具有的资源节点返回数据结构
 */
interface AuthRoleResourceProps {
  // 资源树信息
  resourceMeta: AuthResourceTreeRawProps;
  // 资源节点信息
  resourceTreeList: Array<AuthResourceMetaRawProps>;
}

/**
 * 计算签名
 * @param timestamp
 * @returns
 */
const generateSignature = (timestamp: number = Number(new Date())) => {
  return md5(AK + SK + timestamp);
};

/**
 * 遍历资源节点
 */
const getResourceMetaList = (list: Array<AuthResourceMetaProps>): string[] => {
  return list.reduce((prev, cur) => {
    if (cur.children && cur.children.length > 0) {
      const children = getResourceMetaList(cur.children);
      return [...prev, ...[cur.code], ...children];
    } else {
      return [...prev, cur.code];
    }
  }, [] as string[]);
};

export class AuthManage {
  static defaultHeaders = {
    'version': '1.0.0',
    'appkey': AK,
    'User-Agent': 'iAPI/1.0.0 (https://iapi.baidu-int.com)',
    'Content-Type': 'application/json'
  };

  /**
   * 查询应用下全部资源树
   * @param query
   * @returns
   */
  static async queryGlobalReourceTree(): Promise<{
    msg: string;
    success: boolean;
    data?: Array<Pick<AuthResourceTreeRawProps, 'code'>>;
  }> {
    const timestamp = Number(new Date());
    const signature = generateSignature(timestamp);

    var config = {
      method: 'post',
      url: 'https://uac.baidu-int.com/api/v2/resource/meta/all',
      headers: {
        ...this.defaultHeaders,
        timestamp: timestamp,
        sign: signature
      },
      data: {}
    };

    try {
      const resRaw = await axios<{data: AuthResourceTreeRawProps[]}>(config);
      const data = resRaw.data.data.map(item => ({code: item.code}));

      return {
        data,
        msg: '请求成功',
        success: true
      };
    } catch (err) {
      return {
        msg: '请求失败',
        success: false
      };
    }
  }

  /**
   * 查询应用下某资源树下的资源节点信息
   * @param query
   * @returns
   */
  static async queryReourceMeta(body: {metaCode: string}): Promise<{
    msg: string;
    success: boolean;
    data: Array<AuthResourceRawProps>;
  }> {
    const timestamp = Number(new Date());
    const signature = generateSignature(timestamp);

    var config = {
      method: 'post',
      url: 'https://uac.baidu-int.com/api/v2/resource/tree',
      headers: {
        ...this.defaultHeaders,
        timestamp: timestamp,
        sign: signature
      },
      data: body
    };

    try {
      const resRaw = await axios<{data: AuthResourceRawProps[]}>(config);
      const data = resRaw.data.data;

      return {
        data,
        msg: '请求成功',
        success: true
      };
    } catch (err) {
      return {
        msg: '请求失败',
        data: [],
        success: false
      };
    }
  }

  /**
   * 查询分析平台全部资源节点信息
   * @param query
   * @returns
   */
  static async queryGlobalReourceMeta(): Promise<{
    msg: string;
    success: boolean;
    data: Array<any>;
  }> {
    try {
      const {data: treeList} = await this.queryGlobalReourceTree();
      const metaList = await Promise.all(
        treeList!.map(
          async ({code}) =>
            (
              await this.queryReourceMeta({metaCode: code}).catch(err => ({
                data: []
              }))
            ).data
        )
      );

      return {
        data: metaList || [],
        msg: '请求成功',
        success: true
      };
    } catch (err) {
      return {
        data: [],
        msg: '请求失败',
        success: false
      };
    }
  }

  /**
   * 查询分析平台全部角色
   * @param query
   * @returns
   */
  static async queryGlobalRoles(query: {data: Record<string, any>}): Promise<{
    msg: string;
    success: boolean;
    data: Array<
      Pick<AuthRoleRawProps, 'code' | 'name' | 'pcode' | 'description'>
    >;
  }> {
    const timestamp = Number(new Date());
    const signature = generateSignature(timestamp);

    var config = {
      method: 'post',
      url: 'https://uac.baidu-int.com/api/api/v1/entAppRoles',
      headers: {
        ...this.defaultHeaders,
        timestamp: timestamp,
        sign: signature
      },
      data: {data: {entId: 0}}
    };

    try {
      const resRaw = await axios<{data: AuthRoleRawProps[]}>(config);
      const data = resRaw.data.data;

      return {
        data: data,
        msg: '请求成功',
        success: true
      };
    } catch (err) {
      return {
        data: [],
        msg: '请求失败',
        success: false
      };
    }
  }

  /**
   * 查询用户的所有角色
   * @param query
   * @returns
   */
  static async queryAllUserRoles(query: {userAccount: string}): Promise<{
    msg: string;
    success: boolean;
    data: Array<
      Pick<AuthRoleRawProps, 'code' | 'name' | 'pcode' | 'description'>
    >;
  }> {
    const timestamp = Number(new Date());
    const signature = generateSignature(timestamp);

    var config = {
      method: 'post',
      url: 'https://uac.baidu-int.com/api/api/v1/userRoles',
      headers: {
        ...this.defaultHeaders,
        timestamp: timestamp,
        sign: signature
      },
      data: query
    };

    try {
      const resRaw = await axios<{data: AuthRoleRawProps[]}>(config);
      const data = resRaw.data.data;
      return {
        data,
        msg: '请求成功',
        success: true
      };
    } catch (err) {
      return {
        msg: '请求失败',
        data: [],
        success: false
      };
    }
  }

  /**
   * 查询用户在分析平台的全部角色
   * @param query
   * @returns
   */
  static async queryUserRoles(body: {userAccount: string}): Promise<{
    msg: string;
    success: boolean;
    data: Array<
      Pick<AuthRoleRawProps, 'code' | 'name' | 'pcode' | 'description'>
    >;
  }> {
    const {userAccount} = body;
    if (!userAccount) {
      return {
        data: [],
        msg: '参数缺失',
        success: false
      };
    }

    try {
      // 查询 用户角色 / 平台角色 / 平台资源
      const [{data: userAllRoles}, {data: allRoles}] = await Promise.all([
        this.queryAllUserRoles({userAccount}).catch(err => ({
          data: [],
          success: false,
          msg: '查询用户角色失败'
        })),
        this.queryGlobalRoles({data: {entId: 0}}).catch(err => ({
          data: [],
          success: false,
          msg: '查询全局角色失败'
        }))
      ]);

      // 如果serviceId不为global，则需要拼接为某个具体的应用角色
      // 用户角色中筛选出平台下的角色
      const userRoleWithEnv = userAllRoles?.filter(userRole =>
        allRoles.some(role => role.code === userRole.code)
      );

      return {
        data: userRoleWithEnv,
        msg: '请求成功',
        success: true
      };
    } catch (err) {
      return {
        data: [],
        msg: '请求失败',
        success: false
      };
    }
  }

  /**
   * 查询指定角色下全部资源
   * @param query
   * @returns
   */
  static async queryResourceInRoles(body: {
    roleCodeList: string[];
    metaCodeList: string[];
  }): Promise<{
    msg: string;
    success: boolean;
    data: Record<string, Record<string, AuthRoleResourceProps>>;
  }> {
    const timestamp = Number(new Date());
    const signature = generateSignature(timestamp);

    var config = {
      method: 'post',
      url: 'https://uac.baidu-int.com/api/v2/role/auth/multi',
      headers: {
        ...this.defaultHeaders,
        timestamp: timestamp,
        sign: signature
      },
      data: body
    };

    try {
      const resRaw = await axios<{
        data: Record<string, Record<string, AuthRoleResourceProps>>;
      }>(config);
      const data = resRaw.data.data;

      return {
        data: data,
        msg: '请求成功',
        success: true
      };
    } catch (err) {
      return {
        data: {},
        msg: '请求失败',
        success: false
      };
    }
  }

  /**
   * 查询用户在平台下具备的所有角色及对应权限
   * @param body
   * @returns
   */
  static async queryUserRoleAndResource( userAccount: string): Promise<{
    msg: string;
    success: boolean;
    data: Record<string, string[]>;
  }> {

    try {
      // 查询 用户角色 / 平台角色 / 平台资源
      const {data: userAllRoles} = await this.queryUserRoles({userAccount});

      const roleCodeList = userAllRoles.map(role => role.code);

      const {data: allResourceMeta} = await this.queryGlobalReourceMeta().catch(err => ({
        data: [],
        success: false,
        msg: '查询全局资源失败'
      }))

      const metaCodeList = allResourceMeta.map(tree => tree.resourceMeta?.code);
      
      // 查询角色下的资源节点的资源权限
      const {data: userResource} = await this.queryResourceInRoles({
        roleCodeList,
        metaCodeList
      });

      const resourceList: {[role: string]: string[]} = {};
      Object.keys(userResource).forEach(role => {
        const roleMap: string[] = [];
        Object.values(userResource[role]).forEach((tree: AuthRoleResourceProps) => {
          roleMap.push(tree.resourceMeta.code);
          tree.resourceTreeList?.forEach((meta: AuthResourceMetaRawProps) => {
            roleMap.push(meta.code);
          });
        });
        resourceList[role] = roleMap;
      });

      return {
        data: resourceList,
        msg: '用户权限查询成功',
        success: true
      };
    } catch (err) {
      return {
        msg: '查询用户权限失败',
        data: {},
        success: true
      };
    }
  }

  /**
   * 查询用户指定应用下的具备的所有权限
   * @param body
   * @returns
   */
  static async queryResourceInUser(body: {
    serviceId: string;
    userAccount: string;
  }): Promise<{
    msg: string;
    success: boolean;
    data: AuthRoleResourceProps[];
  }> {
    const {serviceId, userAccount} = body;
    if (!serviceId || !userAccount) {
      return {
        data: [],
        msg: '参数缺失',
        success: false
      };
    }

    try {
      // 查询应用角色 / 用户角色 / 应用资源
      const [{data: userRoles}, {data: allRoles}, {data: allResourceMeta}] =
        await Promise.all([
          this.queryAllUserRoles({userAccount}).catch(err => ({
            data: [],
            success: false,
            msg: '查询用户角色失败'
          })),
          this.queryGlobalRoles({data: {entId: 0}}).catch(err => ({
            data: [],
            success: false,
            msg: '查询全局角色失败'
          })),
          this.queryGlobalReourceMeta().catch(err => ({
            data: [],
            success: false,
            msg: '查询全局资源失败'
          }))
        ]);

      // 用户角色中筛选出当前应用下的角色
      const userRoleWithEnv = userRoles?.filter(userRole =>
        allRoles.some(role => role.code === userRole.code)
      );
      const roleCodeList = userRoleWithEnv?.map(role => role.code);
      const metaCodeList = allResourceMeta.map(tree => tree.resourceMeta?.code);
      // 查询角色下的资源节点的资源权限
      const {data: userResource} = await this.queryResourceInRoles({
        roleCodeList,
        metaCodeList
      });
      const data = Object.values(userResource).reduce((prev, cur) => {
        return [...prev, ...Object.values(cur)];
      }, [] as AuthRoleResourceProps[]);

      return {
        data,
        msg: '用户权限查询成功',
        success: true
      };
    } catch (err) {
      return {
        msg: '用户权限查询失败',
        data: [],
        success: false
      };
    }
  }

  /**
   * 查询用户是否具备指定权限
   * @param body
   * @returns
   */
  static async queryResourceAuth(body: {
    serviceId: string;
    resourceCode: string[];
    userAccount: string;
  }) {
    const {serviceId, resourceCode, userAccount} = body;
    if (!serviceId || !resourceCode || !userAccount) {
      return {
        msg: '参数缺失',
        success: false
      };
    }

    try {
      // 查询 用户角色 / 平台角色 / 平台资源
      const [{data: userAllRoles}, {data: allRoles}, {data: allResourceMeta}] =
        await Promise.all([
          this.queryAllUserRoles({userAccount}).catch(err => ({
            data: [],
            success: false,
            msg: '查询用户角色失败'
          })),
          this.queryGlobalRoles({data: {entId: 0}}).catch(err => ({
            data: [],
            success: false,
            msg: '查询全局角色失败'
          })),
          this.queryGlobalReourceMeta().catch(err => ({
            data: [],
            success: false,
            msg: '查询全局资源失败'
          }))
        ]);

      // 如果serviceId不为global，则需要拼接为某个具体的应用角色
      // TODO：若为global，应过滤出global角色，待确定global角色、资源再处理
      const userRoles =
        serviceId === 'global'
          ? userAllRoles
          : userAllRoles.filter(role =>
              role.code
                .toLocaleLowerCase()
                .includes(serviceId.toLocaleLowerCase())
            );
      // 用户角色中筛选出平台下的角色
      const userRoleWithEnv = userRoles?.filter(userRole =>
        allRoles.some(role => role.code === userRole.code)
      );
      const roleCodeList = userRoleWithEnv?.map(role => role.code);
      const metaCodeList = allResourceMeta.map(tree => tree.resourceMeta?.code);

      let data: Record<string, boolean> = {};

      // 若有管理员权限，返回true
      if (roleCodeList.some(role => role.includes('admin'))) {
        Array.isArray(resourceCode)
          ? resourceCode.forEach(code => {
              data[code] = true;
            })
          : (data[resourceCode] = true);
        return {
          data,
          msg: '用户权限查询成功',
          success: true
        };
      }

      if (!userRoleWithEnv.length) {
        Array.isArray(resourceCode)
          ? resourceCode.forEach(code => {
              data[code] = false;
            })
          : (data[resourceCode] = false);
        return {
          data,
          msg: '用户权限查询成功',
          success: true
        };
      }

      // 查询角色下的资源节点的资源权限
      const {data: userResource} = await this.queryResourceInRoles({
        roleCodeList,
        metaCodeList
      });

      let resourceList: string[] = [];
      Object.values(userResource).forEach(role => {
        Object.values(role).forEach((tree: AuthRoleResourceProps) => {
          resourceList.push(tree.resourceMeta.code);
          tree.resourceTreeList?.forEach((meta: AuthResourceMetaRawProps) => {
            resourceList.push(meta.code);
          });
        });
      });

      // 判断用户拥有的资源中是否包含查询权限
      Array.isArray(resourceCode)
        ? resourceCode.forEach(code => {
            data[code] = !!resourceList?.includes(code);
          })
        : (data[resourceCode] = !!resourceList?.includes(resourceCode));
      return {
        data,
        msg: '用户权限查询成功',
        success: true
      };
    } catch (err) {
      return {
        msg: err,
        success: false
      };
    }
  }
}
