import winston from 'winston';
import 'winston-daily-rotate-file';
import {
  API_LOG_DIR,
  PAGE_LOG_DIR,
  ACTION_LOG_DIR,
  PERFORMANCE_LOG_DIR,
  SATISFY_LOG_DIR
} from '../utils/const';

import {DEFAULT_ROOTDIR_NAME} from '../utils';
import {filterMappingData} from '../utils/rules';
import {mapping as actionMapping} from '../mapping/action-log';
import {mapping as apiMapping} from '../mapping/api-log';
import {mapping as appPerfMapping} from '../mapping/appPerformance';
import {mapping as pageMapping} from '../mapping/page-log';
import {mapping as satisfyMapping} from '../mapping/satisfy-log';

const rootDir = DEFAULT_ROOTDIR_NAME;
const defaultOptions = {
  dirname: `../${rootDir}/default`,
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  format: winston.format.combine(
    winston.format.printf(({message}) => {
      return JSON.stringify(message);
    })
  )
};

export const apiInfoLoggers = (rootDirName?: string) => {
  if (rootDirName === DEFAULT_ROOTDIR_NAME) {
    return oldApiInfoLoggers;
  } else {
    return newApiInfoLoggers;
  }
};

const oldApiInfoLoggers = winston.createLogger({
  level: 'info',
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'api-info-%DATE%.log',
      ...defaultOptions,
      dirname: `../${DEFAULT_ROOTDIR_NAME}/api`,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(apiMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      )
    })
  ]
});
const newApiInfoLoggers = winston.createLogger({
  level: 'info',
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'api-info-%DATE%.log',
      ...defaultOptions,
      dirname: API_LOG_DIR,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(apiMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      )
    })
  ]
});

export const apiErrorLoggers = (rootDirName?: string) => {
  if (rootDirName === DEFAULT_ROOTDIR_NAME) {
    return oldApiErrorLoggers;
  } else {
    return newApiErrorLoggers;
  }
};
export const oldApiErrorLoggers = winston.createLogger({
  level: 'error',
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'error',
      filename: 'api-error-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(apiMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: `../${DEFAULT_ROOTDIR_NAME}/api`
    })
  ]
});

export const newApiErrorLoggers = winston.createLogger({
  level: 'error',
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'error',
      filename: 'api-error-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(apiMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: API_LOG_DIR
    })
  ]
});
export const pageInfoLoggers = (rootDirName?: string) => {
  if (rootDirName === DEFAULT_ROOTDIR_NAME) {
    return oldPageInfoLoggers;
  } else {
    return newPageInfoLoggers;
  }
};
const oldPageInfoLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'page-info-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(pageMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: `../${DEFAULT_ROOTDIR_NAME}/page`
    })
  ]
});
const newPageInfoLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'page-info-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(pageMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: PAGE_LOG_DIR
    })
  ]
});
export const pageErrorLoggers = (rootDirName?: string) => {
  if (rootDirName === DEFAULT_ROOTDIR_NAME) {
    return oldPageErrorLoggers;
  } else {
    return newPageErrorLoggers;
  }
};

const oldPageErrorLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'error',
      filename: 'page-error-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(pageMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: `../${DEFAULT_ROOTDIR_NAME}/page`
    })
  ]
});

const newPageErrorLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'error',
      filename: 'page-error-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(pageMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: PAGE_LOG_DIR
    })
  ]
});

export const actionLoggers = (rootDirName?: string) => {
  if (rootDirName === DEFAULT_ROOTDIR_NAME) {
    return oldActionLoggers;
  } else {
    return newActionLoggers;
  }
};

const oldActionLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'action-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(actionMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: `../${DEFAULT_ROOTDIR_NAME}/action`
    })
  ]
});

const newActionLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'action-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(actionMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: ACTION_LOG_DIR
    })
  ]
});

export const performanceLoggers = (rootDirName?: string) => {
  if (rootDirName === DEFAULT_ROOTDIR_NAME) {
    return oldPerformanceLoggers;
  } else {
    return newPerformanceLoggers;
  }
};

const oldPerformanceLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'performance-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(appPerfMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: `../${DEFAULT_ROOTDIR_NAME}/performance`
    })
  ]
});

const newPerformanceLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'performance-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(appPerfMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: PERFORMANCE_LOG_DIR
    })
  ]
});
export const snapshotErrorLoggers = (uuid: string, rootDirName?: string) => {
  return winston.createLogger({
    transports: [
      new winston.transports.DailyRotateFile({
        level: 'error',
        filename: `${uuid}-error-snapshotEvent-%DATE%.log`,
        ...defaultOptions,
        format: winston.format.combine(
          winston.format.printf(({message}) => {
            try {
              message = filterMappingData(pageMapping, message);
            } catch {}
            return JSON.stringify(message);
          })
        ),
        dirname: `../${rootDirName || rootDir}/snapshot`
      })
    ]
  });
};

export const satisfyLoggers = (rootDirName?: string) => {
  if (rootDirName === DEFAULT_ROOTDIR_NAME) {
    return oldSatisfyLoggers;
  } else {
    return newSatisfyLoggers;
  }
};
const oldSatisfyLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'satisfy-info-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(satisfyMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: `../${DEFAULT_ROOTDIR_NAME}/satisfy`
    })
  ]
});

const newSatisfyLoggers = winston.createLogger({
  transports: [
    new winston.transports.DailyRotateFile({
      level: 'info',
      filename: 'satisfy-info-%DATE%.log',
      ...defaultOptions,
      format: winston.format.combine(
        winston.format.printf(({message}) => {
          try {
            message = filterMappingData(satisfyMapping, message);
          } catch {}
          return JSON.stringify(message);
        })
      ),
      dirname: SATISFY_LOG_DIR
    })
  ]
});
