import {scheduleJob, RecurrenceRule} from 'node-schedule';
import moment from 'moment';
import child_process from 'child_process';
import util from 'util';
import fs from 'fs';

const {LOG_DIR = '', NEW_LOG_DIR = ''} = process.env;
const exec = util.promisify(child_process.exec);
const handler = async () => {
  const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
  console.log('start remove log');
  console.log('log dir：', LOG_DIR);
  console.log('yesterday：', yesterday);
  console.log('log dir exists', fs.existsSync(LOG_DIR));
  if (fs.existsSync(LOG_DIR)) {
    const cmd = `
    cd ${LOG_DIR}
    rm -rf ./api/*${yesterday}*
    rm -rf ./page/*${yesterday}*
    rm -rf ./performance/*${yesterday}*
    rm -rf ./action/*${yesterday}*
    rm -rf ./satisfy/*${yesterday}*
  `;
    try {
      await exec(cmd);
    } catch (error) {
      console.log('remove failed ', error);
    }
  }
  console.log('new log dir exists', fs.existsSync(NEW_LOG_DIR));
  if (fs.existsSync(NEW_LOG_DIR)) {
    const newCmd = `
    cd ${NEW_LOG_DIR}
    rm -rf ./api/*${yesterday}*
    rm -rf ./page/*${yesterday}*
    rm -rf ./performance/*${yesterday}*
    rm -rf ./action/*${yesterday}*
    rm -rf ./satisfy/*${yesterday}*
  `;
    try {
      await exec(newCmd);
    } catch (error) {
      console.log('remove new log dir failed ', error);
    }
  }
};

// 每周一凌晨2点执行
export default () => {
  const rule = new RecurrenceRule();
  // 每天下午4点清空昨天的日志
  rule.hour = 16;
  rule.minute = 0;
  rule.second = 0;
  rule.tz = 'Asia/Shanghai';
  scheduleJob(rule, () => {
    handler();
  });
};
