import {scheduleJob, RecurrenceRule} from 'node-schedule';
import path from 'path';
import moment from 'moment';
import {
  specialEmailReceivers,
  iaasPmEmailReceivers,
  testFeReceivers,
  mailInfoMap,
  isProd
} from '../utils';
import {redisClient} from '../client';
import {Container} from 'typedi';
import {BaiduMail} from '../service';

const {LOG_DIR = '', NEW_LOG_DIR = ''} = process.env;
const sendMail = async (type: string, to: any, isTest = false) => {
  try {
    // 尝试获取锁，锁的有效期为30分钟
    const redisInstance = Container.get(redisClient);
    await redisInstance.lock(`lock:send-mail-${type}`, 30 * 60 * 1000);
    // 如果成功获取锁，则执行任务
    console.log('send mail job lock acquired, executing task...');
    await BaiduMail.sendEmail(
      {
        to,
        subject: mailInfoMap[type].subject
      },
      mailInfoMap[type].serviceList,
      type
    );
    const params: any = {
      published: true,
      publish_time: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    };
    isTest && (params.title = `自动测试邮件-${type}`);
    // 平台存一份已发送邮件的记录，方便后续查询
    await BaiduMail.saveEmail(type, params);
    // await redisInstance.releaseLock(`lock:send-mail-${type}`);
    console.log('send mail job  Lock released, task completed');
  } catch (error) {
    console.log(`send mail error type: ${type} to: ${to}`, error);
  }
};
const sendTrendToBos = async (type: string) => {
  try {
    // 尝试获取锁，锁的有效期为30分钟
    const redisInstance = Container.get(redisClient);
    await redisInstance.lock(`lock:send-trend-to-bos-${type}`, 30 * 60 * 1000);
    // 如果成功获取锁，则执行任务
    console.log('send trend to bos job Lock acquired, executing task...');
    await BaiduMail.genTrendReport(mailInfoMap[type].serviceList, type);
    await BaiduMail.sendFileToBos(type);
    // await redisInstance.releaseLock(`lock:send-trend-to-bos-${type}`);
    console.log('send trend to bos job Lock released, task completed');
  } catch (error) {
    console.log(`send trend to bos error type: ${type}`, error);
  }
};

const createNewEmail = async (type: string) => {
  try {
    // 尝试获取锁，锁的有效期为30分钟
    const redisInstance = Container.get(redisClient);
    await redisInstance.lock(`lock:create-new-email-${type}`, 30 * 60 * 1000);
    // 如果成功获取锁，则执行任务
    console.log('create new email job Lock, executing task...');
    await BaiduMail.saveEmail(type);
    // await redisInstance.releaseLock(`lock:create-new-email-${type}`);
    console.log('create new email job Lock released, task completed');
  } catch (error) {
    console.log(`create new email error type: ${type}`, error);
  }
};

// 定时任务
export default () => {
  console.log('send mail job start');
  // 每周一上午十一点发专项邮件；提前一小时发测试邮件
  const specialRule = new RecurrenceRule();
  const specialTestRule = new RecurrenceRule();
  specialRule.hour = 11;
  specialRule.minute = 0;
  specialTestRule.hour = 10;
  specialTestRule.minute = 0;
  specialRule.dayOfWeek = 1;
  specialTestRule.dayOfWeek = 1;
  specialRule.tz = 'Asia/Shanghai';
  specialTestRule.tz = 'Asia/Shanghai';
  // 测试邮件
  scheduleJob(specialTestRule, () => {
    sendMail('special', testFeReceivers, true);
  });
  // 正式邮件
  scheduleJob(specialRule, () => {
    sendMail('special', specialEmailReceivers);
  });

  // 每周四下午一点发报错率邮件；提前一小时发测试邮件
  const errorRateRule = new RecurrenceRule();
  const errorRateTestRule = new RecurrenceRule();
  errorRateRule.dayOfWeek = 4;
  errorRateTestRule.dayOfWeek = 4;
  errorRateRule.hour = 13;
  errorRateRule.minute = 0;
  errorRateTestRule.hour = 12;
  errorRateTestRule.minute = 0;
  errorRateRule.tz = 'Asia/Shanghai';
  errorRateTestRule.tz = 'Asia/Shanghai';
  // 测试邮件
  scheduleJob(errorRateTestRule, () => {
    sendMail('iaaspm', testFeReceivers, true);
  });
  // 正式邮件
  scheduleJob(errorRateRule, () => {
    sendMail('iaaspm', iaasPmEmailReceivers);
  });
  // 生成趋势图，并上传bos
  scheduleJob(errorRateRule, () => {
    sendTrendToBos('iaas');
    createNewEmail('iaas');
  });
};
