import {scheduleJob, RecurrenceRule, scheduledJobs, cancelJob} from 'node-schedule';
import {getAllReports, envs} from '../utils';
import {Reports as ReportDTO} from '../model/reports';
import moment from 'moment';

const handler = async (startTime: number, endTime: number) => {
  startTime = startTime.toString().length === 10 ? startTime * 1000 : startTime;
  endTime = endTime.toString().length === 10 ? endTime * 1000 : endTime;
  const serviceReport = await getAllReports(startTime, endTime);
  const tasks: any[] = [];
  console.log('start gen report');
  console.log('services：', Reflect.ownKeys(serviceReport));
  console.log('envs', envs);
  Reflect.ownKeys(serviceReport).forEach(key => {
    envs.forEach(env => {
      tasks.push(new Promise(async (resolve) => {
          const query: any = {
            service_id: key,
            env: env,
            start_time: moment(startTime).utc().format('YYYY-MM-DD HH:mm:ss') + 'Z',
            end_time: moment(endTime).utc().format('YYYY-MM-DD HH:mm:ss') + 'Z',
          };
          const res: any = await ReportDTO.findOne({
            where: query,
            attributes: ['detail']
          });
          if (!res?.detail) {
            resolve(ReportDTO.create({
              service_id: key,
              env,
              start_time: moment(startTime).format('YYYY-MM-DD HH:mm:ss'),
              end_time: moment(endTime).format('YYYY-MM-DD HH:mm:ss'),
              detail: serviceReport[key][env]
            }))
          } else {
            resolve(res.detail);
          }
        })
      );
    });
  });
  try {
    await Promise.all(tasks);
  } catch (err) {
    console.error('gen report error',err);
  }
};

// 每周一凌晨2点执行
const jobWeek = () => {
  const rule = new RecurrenceRule();
  rule.hour = 2;
  rule.dayOfWeek = 1;
  rule.tz = 'Asia/Shanghai';
  scheduleJob(rule, fireTime => {
    const startDate = moment()
      .week(moment().week() - 1)
      .startOf('week')
      .add(1, 'days')
      .valueOf();
    const endDate = moment()
      .week(moment().week() - 1)
      .endOf('week')
      .add(1, 'days')
      .valueOf();
    handler(startDate, endDate);
  });
};

// // 每月初凌晨两点执行
const jobMonth = () => {
  const rule = new RecurrenceRule();
  rule.hour = 2;
  rule.date = 1;
  rule.tz = 'Asia/Shanghai';
  scheduleJob(rule, fireTime => {
    const startDate = moment()
      .month(moment().month() - 1)
      .startOf('month')
      .valueOf();
    const endDate = moment()
      .month(moment().month() - 1)
      .endOf('month')
      .valueOf();
    handler(startDate, endDate);
  });
};


// // 立即生成上周、上个月的报告
const immediateJob = async () => {
  const weekStartTime = moment()
    .week(moment().week() - 1)
    .startOf('week')
    .add(1, 'days')
    .valueOf();
  const weekEndTime = moment()
    .week(moment().week() - 1)
    .endOf('week')
    .add(1, 'days')
    .valueOf();
  const monthStartTime = moment()
    .month(moment().month() - 1)
    .startOf('month')
    .valueOf();
  const monthEndTime = moment()
    .month(moment().month() - 1)
    .endOf('month')
    .valueOf();
  await handler(weekStartTime, weekEndTime);
  await handler(monthStartTime, monthEndTime);
};

export const startCronJob = async () => {
  try {
    const jobNames = Reflect.ownKeys(scheduledJobs);
    // 先清除原有任务
    for(let name of jobNames) {
      cancelJob(name as string);
    };
  } finally {
    if (process.env.NODE_APP_INSTANCE === '0') {
      jobWeek();
      jobMonth();
    }
  }
};
export const startGenReportJob = async () => {
  await immediateJob();
};

