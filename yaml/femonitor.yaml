apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: '187'
  generation: 208
  labels:
    app: femonitor
  managedFields:
    - apiVersion: apps/v1
      fieldsType: FieldsV1
      manager: kubectl-edit
      operation: Update
    - apiVersion: apps/v1
      fieldsType: FieldsV1
      fieldsV1:
        'f:metadata':
          'f:annotations':
            .: {}
            'f:deployment.kubernetes.io/revision': {}
          'f:labels':
            .: {}
            'f:app': {}
        'f:spec':
          'f:progressDeadlineSeconds': {}
          'f:revisionHistoryLimit': {}
          'f:selector': {}
          'f:strategy':
            'f:rollingUpdate':
              .: {}
              'f:maxSurge': {}
              'f:maxUnavailable': {}
            'f:type': {}
          'f:template':
            'f:metadata':
              'f:labels':
                .: {}
                'f:app': {}
            'f:spec':
              'f:affinity':
                .: {}
                'f:nodeAffinity': {}
                'f:podAffinity': {}
                'f:podAntiAffinity': {}
              'f:containers':
                'k:{"name":"femonitor-web"}':
                  .: {}
                  'f:env':
                    .: {}
                    'k:{"name":"NODE_ENV"}':
                      .: {}
                      'f:name': {}
                      'f:value': {}
                  'f:image': {}
                  'f:imagePullPolicy': {}
                  'f:livenessProbe':
                    .: {}
                    'f:failureThreshold': {}
                    'f:httpGet':
                      .: {}
                      'f:path': {}
                      'f:port': {}
                      'f:scheme': {}
                    'f:initialDelaySeconds': {}
                    'f:periodSeconds': {}
                    'f:successThreshold': {}
                    'f:timeoutSeconds': {}
                  'f:name': {}
                  'f:ports':
                    .: {}
                    'k:{"containerPort":8085,"protocol":"TCP"}':
                      .: {}
                      'f:containerPort': {}
                      'f:protocol': {}
                  'f:readinessProbe':
                    .: {}
                    'f:failureThreshold': {}
                    'f:httpGet':
                      .: {}
                      'f:path': {}
                      'f:port': {}
                      'f:scheme': {}
                    'f:initialDelaySeconds': {}
                    'f:periodSeconds': {}
                    'f:successThreshold': {}
                    'f:timeoutSeconds': {}
                  'f:resources':
                    .: {}
                    'f:limits':
                      .: {}
                      'f:cpu': {}
                      'f:memory': {}
                      'f:nvidia.com/gpu': {}
                    'f:requests':
                      .: {}
                      'f:cpu': {}
                      'f:memory': {}
                      'f:nvidia.com/gpu': {}
                  'f:securityContext':
                    .: {}
                    'f:privileged': {}
                  'f:startupProbe':
                    .: {}
                    'f:failureThreshold': {}
                    'f:httpGet':
                      .: {}
                      'f:path': {}
                      'f:port': {}
                      'f:scheme': {}
                    'f:initialDelaySeconds': {}
                    'f:periodSeconds': {}
                    'f:successThreshold': {}
                    'f:timeoutSeconds': {}
                  'f:terminationMessagePath': {}
                  'f:terminationMessagePolicy': {}
              'f:dnsPolicy': {}
              'f:restartPolicy': {}
              'f:schedulerName': {}
              'f:securityContext': {}
              'f:terminationGracePeriodSeconds': {}
              'f:tolerations': {}
      manager: dashboard
      operation: Update
    - apiVersion: apps/v1
      fieldsType: FieldsV1
      fieldsV1:
        'f:metadata':
          'f:annotations':
            'f:deployment.kubernetes.io/revision': {}
        'f:status':
          'f:availableReplicas': {}
          'f:conditions':
            .: {}
            'k:{"type":"Available"}':
              .: {}
              'f:lastTransitionTime': {}
              'f:lastUpdateTime': {}
              'f:message': {}
              'f:reason': {}
              'f:status': {}
              'f:type': {}
            'k:{"type":"Progressing"}':
              .: {}
              'f:lastTransitionTime': {}
              'f:lastUpdateTime': {}
              'f:message': {}
              'f:reason': {}
              'f:status': {}
              'f:type': {}
          'f:observedGeneration': {}
          'f:readyReplicas': {}
          'f:replicas': {}
          'f:updatedReplicas': {}
      manager: kube-controller-manager
      operation: Update
  name: femonitor
  namespace: default
spec:
  progressDeadlineSeconds: 600
  replicas: 3
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: femonitor
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: femonitor
    spec:
      affinity:
        nodeAffinity: {}
        podAffinity: {}
        podAntiAffinity: {}
      containers:
        - env:
            - name: NODE_ENV
              value: production
          image: 'iregistry.baidu-int.com/fe-monitor/monitor-server:20230530_1685446827116'
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /
              port: 8085
              scheme: HTTP
            initialDelaySeconds: 180
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          name: femonitor-web
          ports:
            - containerPort: 8085
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 8085
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 90
            successThreshold: 1
            timeoutSeconds: 10
          resources:
            limits:
              cpu: '2'
              memory: 10Gi
              nvidia.com/gpu: '0'
            requests:
              cpu: 250m
              memory: 512Mi
              nvidia.com/gpu: '0'
          securityContext:
            privileged: false
          startupProbe:
            failureThreshold: 3
            httpGet:
              path: /healthcheck
              port: 8085
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 90
            successThreshold: 1
            timeoutSeconds: 10
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      tolerations:
        - effect: NoSchedule
          key: node-role.kubernetes.io/master
          operator: Exists
status:
  availableReplicas: 3
  observedGeneration: 208
  readyReplicas: 3
  replicas: 3
  updatedReplicas: 3
