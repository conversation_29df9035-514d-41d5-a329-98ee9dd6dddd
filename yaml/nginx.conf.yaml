apiVersion: v1
data:
  nginx.conf: |
    user  nginx;
    worker_processes  1;
    events {
        worker_connections  1024;
    }
    http {
      server_tokens off;
      client_max_body_size 500m;
      gzip on;
      gzip_proxied any;
      gzip_types
          text/css
          text/javascript
          text/xml
          text/plain
          application/javascript
          application/x-javascript
          application/json
          model/gltf+json
          model/gltf-binary;
      gzip_vary on;
      error_page 500 502 503 504 /50x.html;
      upstream femonitor {
        ip_hash;
        server service-femonitor:8085;
      }
      proxy_cache_path cache levels=1:2 keys_zone=FEMONITOR_CACHE:30m inactive=7d max_size=5g;
      server {
        listen 80;
        server_name _;
        proxy_connect_timeout       300;
        proxy_send_timeout          300;
        proxy_read_timeout          90m;
        location / {
          proxy_pass http://femonitor;
          proxy_redirect off;
          proxy_set_header Host $host;
          proxy_set_header X-Real-Ip $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Host $server_name;
        }

        location /static/ {
          expires 1d;
          proxy_cache FEMONITOR_CACHE;
          proxy_cache_key $host$request_uri;
          proxy_ignore_headers Set-Cookie;
          proxy_cache_revalidate on;
          proxy_cache_use_stale error timeout invalid_header updating
                                  http_500 http_502 http_503 http_504;
          # proxy_cache_background_update on;
          proxy_cache_valid 200 1d;
          proxy_pass http://femonitor;
          proxy_set_header Host $http_host;
          proxy_set_header LogID $http_Bfe_logid;
          proxy_set_header X-Forward-For $http_CLIENTIP;
          # proxy_set_header X-Request-ID $request_id;
          proxy_connect_timeout 10s;
        }
      }
      server {
        listen 80;
        server_name console.bce.baidu.com;
        proxy_connect_timeout       300;
        proxy_send_timeout          300;
        proxy_read_timeout          90m;
        location / {
          proxy_pass http://femonitor;
          proxy_redirect off;
          proxy_set_header Host $host;
          proxy_set_header X-Real-Ip $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Host $server_name;
        }
        location /static/ {
          expires 1d;
          proxy_cache FEMONITOR_CACHE;
          proxy_cache_key $host$request_uri;
          proxy_ignore_headers Set-Cookie;
          proxy_cache_revalidate on;
          proxy_cache_use_stale error timeout invalid_header updating
                                  http_500 http_502 http_503 http_504;
          # proxy_cache_background_update on;
          proxy_cache_valid 200 1d;
          proxy_pass http://femonitor;
          proxy_set_header Host $http_host;
          proxy_set_header LogID $http_Bfe_logid;
          proxy_set_header X-Forward-For $http_CLIENTIP;
          # proxy_set_header X-Request-ID $request_id;
          proxy_connect_timeout 10s;
        }
      }
    }
kind: ConfigMap
metadata:
  managedFields:
    - apiVersion: v1
      fieldsType: FieldsV1
      fieldsV1:
        'f:data':
          .: {}
          'f:nginx.conf': {}
      manager: dashboard
      operation: Update
  name: femonitor-nginx-config
  namespace: default
