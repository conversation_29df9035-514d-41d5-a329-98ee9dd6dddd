apiVersion: v1
kind: Service
metadata:
  annotations:
    prometheus.io/scrape: 'true'
  managedFields:
    - apiVersion: v1
      fieldsType: FieldsV1
      fieldsV1:
        'f:metadata':
          'f:annotations':
            .: {}
            'f:prometheus.io/scrape': {}
        'f:spec':
          'f:clusterIP': {}
          'f:clusterIPs': {}
          'f:externalTrafficPolicy': {}
          'f:ports':
            .: {}
            'k:{"port":8085,"protocol":"TCP"}':
              .: {}
              'f:name': {}
              'f:nodePort': {}
              'f:port': {}
              'f:protocol': {}
              'f:targetPort': {}
          'f:selector':
            .: {}
            'f:app': {}
          'f:sessionAffinity': {}
          'f:type': {}
      manager: dashboard
      operation: Update
  name: service-femonitor
  namespace: default
spec:
  clusterIP: **************
  clusterIPs:
    - **************
  externalTrafficPolicy: Cluster
  ports:
    - name: femonitor
      nodePort: 8085
      port: 8085
      protocol: TCP
      targetPort: 8085
  selector:
    app: femonitor
  sessionAffinity: None
  type: NodePort
status:
  loadBalancer: {}
